export function getIgnoreCaseMatchChars(char: any): any[];
export const JsUnicodeProperties: Set<string>;
export const JsUnicodePropertiesMap: Map<any, any>;
export const JsUnicodePropertiesOfStringsMap: Map<any, any>;
export const PosixClassesMap: Map<string, string>;
export const PosixProperties: Set<string>;
export function slug(name: any): any;
export const UnicodePropertiesWithSpecificCase: Set<string>;
