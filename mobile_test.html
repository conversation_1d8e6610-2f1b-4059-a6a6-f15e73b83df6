<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端登录页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .device-frame {
            width: 375px;
            height: 667px;
            border: 8px solid #333;
            border-radius: 25px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            background: #000;
        }
        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>📱 移动端登录页面测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <div class="info">
            <strong>测试目标：</strong>验证移动端登录页面的显示效果和交互功能
        </div>
        <ul>
            <li>✅ 四个功能图标在移动端横排显示</li>
            <li>✅ 页面可以正常上下滚动</li>
            <li>✅ 登录表单在移动端正常显示</li>
            <li>✅ 保持桌面端的原有布局</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>移动设备模拟器</h2>
        <div class="test-buttons">
            <button onclick="changeDevice('iphone')">iPhone (375x667)</button>
            <button onclick="changeDevice('android')">Android (360x640)</button>
            <button onclick="changeDevice('tablet')">平板 (768x1024)</button>
        </div>
        
        <div class="device-frame" id="deviceFrame">
            <iframe 
                id="deviceScreen" 
                class="device-screen" 
                src="http://localhost:5173/"
                title="移动端登录页面">
            </iframe>
        </div>
    </div>

    <div class="test-section">
        <h2>测试检查项</h2>
        <div class="warning">
            <strong>请在上方模拟器中检查以下项目：</strong>
        </div>
        <ol>
            <li><strong>图标布局</strong>：四个功能图标（🗺️🏨🍽️📸）是否在一行横排显示？</li>
            <li><strong>页面滚动</strong>：是否可以上下滚动查看完整内容？</li>
            <li><strong>登录表单</strong>：账号、密码、验证码输入框是否正常显示？</li>
            <li><strong>按钮交互</strong>：登录、注册按钮是否可以正常点击？</li>
            <li><strong>响应式适配</strong>：在不同屏幕尺寸下是否都能正常显示？</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>桌面端对比测试</h2>
        <div class="info">
            <strong>桌面端测试：</strong>
            <a href="http://localhost:5173/" target="_blank">在新窗口打开桌面版</a>
        </div>
        <p>请确认桌面端的布局没有受到移动端优化的影响：</p>
        <ul>
            <li>左右对称布局保持不变</li>
            <li>四个功能图标保持2x2网格布局</li>
            <li>整体视觉效果保持精致美观</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>功能测试</h2>
        <div class="success">
            <strong>登录测试账号：</strong>
        </div>
        <ul>
            <li>账号: testuser</li>
            <li>密码: test123456</li>
            <li>验证码: 不区分大小写</li>
        </ul>
        
        <div class="warning">
            <strong>测试步骤：</strong>
        </div>
        <ol>
            <li>清除浏览器localStorage（模拟未登录状态）</li>
            <li>在移动端模拟器中尝试注册新用户</li>
            <li>使用测试账号登录</li>
            <li>验证登录成功后是否正确跳转到攻略制作页面</li>
        </ol>
    </div>

    <script>
        function changeDevice(type) {
            const frame = document.getElementById('deviceFrame');
            const screen = document.getElementById('deviceScreen');
            
            switch(type) {
                case 'iphone':
                    frame.style.width = '375px';
                    frame.style.height = '667px';
                    break;
                case 'android':
                    frame.style.width = '360px';
                    frame.style.height = '640px';
                    break;
                case 'tablet':
                    frame.style.width = '768px';
                    frame.style.height = '1024px';
                    break;
            }
            
            // 刷新iframe以应用新的尺寸
            screen.src = screen.src;
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('移动端测试页面已加载');
            
            // 检查后端服务状态
            fetch('http://localhost:3999/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('后端服务正常:', data);
                })
                .catch(error => {
                    console.error('后端服务异常:', error);
                    alert('注意：后端服务可能未启动，请确保 topmeans_srv 服务正在运行');
                });
        };
    </script>
</body>
</html>
