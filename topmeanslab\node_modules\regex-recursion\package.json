{"name": "regex-recursion", "version": "6.0.2", "description": "Recursive matching plugin for Regex+", "author": "<PERSON>", "license": "MIT", "type": "module", "exports": {".": {"types": "./types/index.d.ts", "import": "./src/index.js"}}, "browser": "./dist/regex-recursion.min.js", "types": "./types/index.d.ts", "files": ["dist", "src", "types"], "repository": {"type": "git", "url": "git+https://github.com/slevithan/regex-recursion.git"}, "keywords": ["recursion", "regex", "regexp"], "dependencies": {"regex-utilities": "^2.3.0"}, "devDependencies": {"esbuild": "^0.24.2", "jasmine": "^5.5.0", "regex": "^6.0.1", "typescript": "^5.7.3"}, "scripts": {"bundle:global": "esbuild src/index.js --global-name=Regex.plugins --bundle --minify --sourcemap --outfile=dist/regex-recursion.min.js", "types": "tsc src/index.js --rootDir src --declaration --allowJs --emitDeclarationOnly --outDir types", "prebuild": "rm -rf dist/* types/*", "build": "pnpm run bundle:global && pnpm run types", "pretest": "pnpm run build", "test": "jasmine"}}