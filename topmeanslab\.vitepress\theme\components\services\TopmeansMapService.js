import html2canvas from 'html2canvas';
import log from 'loglevel';

const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;

/**
 * 地图服务类 - 负责高德地图相关的所有操作
 */
export class TopmeansMapService {
    constructor() {
        this.AMap = null;
        this.mapInstances = [];
    }

    /**
     * 初始化地图服务
     */
    async initialize() {
        if (typeof window === 'undefined') return;

        let sc = '';
        let sk = '';

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap_keys`, {
                method: 'GET',
                credentials: 'include'
            });
            if (!response.ok) {
                log.error('获取API密钥失败，请检查网络连接', response);
                throw new Error('获取API密钥失败，请检查网络连接');
            }

            const { AMAP_CODE, AMAP_KEY } = await response.json();
            sc = AMAP_CODE;
            sk = AMAP_KEY;
        } catch (err) {
            log.error('获取API密钥异常，请检查网络连接', err);
            throw err;
        }

        window._AMapSecurityConfig = { securityJsCode: sc };

        try {
            // 异步加载地图脚本
            this.AMap = await this.loadAMapScript(sk);
            return this.AMap;
        } catch (err) {
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 设置地址自动完成功能 - 自定义实现版本
     */
    setupAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        // 创建自定义的自动完成组件
        this.createCustomAutoComplete(startInputId, onStartSelect);
        this.createCustomAutoComplete(endInputId, onEndSelect);
    }

    /**
     * 创建自定义的自动完成组件
     */
    createCustomAutoComplete(inputId, onSelect) {
        const inputElement = document.getElementById(inputId);
        if (!inputElement) return;

        // 创建提示列表容器
        const suggestContainer = this.createSuggestContainer(inputId);

        // 使用高德的搜索服务
        this.AMap.plugin(['AMap.PlaceSearch'], () => {
            const placeSearch = new this.AMap.PlaceSearch({
                pageSize: 10,
                pageIndex: 1,
                citylimit: false,
                extensions: 'all'
            });

            let searchTimeout = null;
            let currentSuggestions = [];
            let selectedIndex = -1;

            // 输入事件处理
            const handleInput = (e) => {
                const query = e.target.value.trim();

                if (query.length < 2) {
                    this.hideSuggestions(suggestContainer);
                    return;
                }

                // 防抖处理
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                searchTimeout = setTimeout(() => {
                    this.searchPlaces(placeSearch, query, suggestContainer, onSelect, inputElement);
                }, 300);
            };

            // 键盘导航处理
            const handleKeydown = (e) => {
                const items = suggestContainer.querySelectorAll('.custom-suggest-item');

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                        this.updateSelection(items, selectedIndex);
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        selectedIndex = Math.max(selectedIndex - 1, -1);
                        this.updateSelection(items, selectedIndex);
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (selectedIndex >= 0 && items[selectedIndex]) {
                            this.selectSuggestion(items[selectedIndex], onSelect, inputElement, suggestContainer);
                        }
                        break;
                    case 'Escape':
                        this.hideSuggestions(suggestContainer);
                        inputElement.blur();
                        break;
                }
            };

            // 焦点处理 - 延迟隐藏以允许点击选择
            const handleBlur = () => {
                setTimeout(() => {
                    // 检查是否点击了建议项
                    if (!suggestContainer.matches(':hover')) {
                        this.hideSuggestions(suggestContainer);
                    }
                }, 150);
            };

            // 窗口事件处理
            const handleResize = () => {
                if (suggestContainer.style.display !== 'none') {
                    this.positionSuggestContainer(suggestContainer, inputElement);
                }
            };

            const handleScroll = () => {
                if (suggestContainer.style.display !== 'none') {
                    this.positionSuggestContainer(suggestContainer, inputElement);
                }
            };

            // 绑定事件
            inputElement.addEventListener('input', handleInput);
            inputElement.addEventListener('keydown', handleKeydown);
            inputElement.addEventListener('blur', handleBlur);
            inputElement.addEventListener('focus', () => {
                if (inputElement.value.trim().length >= 2) {
                    // 重新搜索以显示建议
                    const query = inputElement.value.trim();
                    this.searchPlaces(placeSearch, query, suggestContainer, onSelect, inputElement);
                }
            });

            // 绑定窗口事件
            window.addEventListener('resize', handleResize);
            window.addEventListener('scroll', handleScroll, true);

            // 存储清理函数
            inputElement._cleanup = () => {
                inputElement.removeEventListener('input', handleInput);
                inputElement.removeEventListener('keydown', handleKeydown);
                inputElement.removeEventListener('blur', handleBlur);
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('scroll', handleScroll, true);
                if (searchTimeout) clearTimeout(searchTimeout);
                if (suggestContainer.parentNode) {
                    suggestContainer.parentNode.removeChild(suggestContainer);
                }
            };
        });
    }

    /**
     * 创建建议列表容器
     */
    createSuggestContainer(inputId) {
        const inputElement = document.getElementById(inputId);
        const container = document.createElement('div');

        container.className = 'custom-amap-suggest';
        container.id = `${inputId}-suggest`;
        container.style.cssText = `
            position: fixed;
            background: var(--vp-c-bg);
            border: 2px solid var(--vp-c-divider);
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            z-index: 99999;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            min-width: 300px;
        `;

        // 使用fixed定位，直接插入到body中
        document.body.appendChild(container);

        return container;
    }

    /**
     * 动态定位建议容器
     */
    positionSuggestContainer(container, inputElement) {
        const inputRect = inputElement.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const containerMaxHeight = 300;

        // 计算输入框下方可用空间
        const spaceBelow = viewportHeight - inputRect.bottom;
        const spaceAbove = inputRect.top;

        // 决定显示在上方还是下方
        const showBelow = spaceBelow >= containerMaxHeight || spaceBelow >= spaceAbove;

        if (showBelow) {
            // 显示在输入框下方
            container.style.top = `${inputRect.bottom + 4}px`;
            container.style.maxHeight = `${Math.min(containerMaxHeight, spaceBelow - 20)}px`;
        } else {
            // 显示在输入框上方
            container.style.top = `${inputRect.top - Math.min(containerMaxHeight, spaceAbove - 20)}px`;
            container.style.maxHeight = `${Math.min(containerMaxHeight, spaceAbove - 20)}px`;
        }

        // 设置左右位置
        container.style.left = `${inputRect.left}px`;
        container.style.width = `${Math.max(inputRect.width, 300)}px`;
    }

    /**
     * 搜索地点
     */
    searchPlaces(placeSearch, query, container, onSelect, inputElement) {
        placeSearch.search(query, (status, result) => {
            if (status === 'complete' && result.poiList && result.poiList.pois) {
                this.showSuggestions(container, result.poiList.pois, onSelect, inputElement);
            } else {
                this.hideSuggestions(container);
            }
        });
    }

        /**
     * 显示建议列表
     */
    showSuggestions(container, pois, onSelect, inputElement) {
        container.innerHTML = '';

        // 动态计算位置
        this.positionSuggestContainer(container, inputElement);

        pois.slice(0, 8).forEach((poi, index) => {
            const item = document.createElement('div');
            item.className = 'custom-suggest-item';
            item.style.cssText = `
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid var(--vp-c-divider-light);
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                gap: 4px;
            `;

            const name = document.createElement('div');
            name.style.cssText = `
                font-weight: 500;
                color: var(--vp-c-text-1);
                font-size: 0.875rem;
            `;
            name.textContent = poi.name;

            const address = document.createElement('div');
            address.style.cssText = `
                color: var(--vp-c-text-2);
                font-size: 0.8rem;
                line-height: 1.3;
            `;
            address.textContent = poi.address || poi.pname + poi.cityname + poi.adname;

            item.appendChild(name);
            item.appendChild(address);

            // 鼠标事件 - 移除mouseleave事件，让CSS的:hover伪类处理hover效果
            item.addEventListener('mouseenter', () => {
                this.clearSelection(container);
            });

            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.selectSuggestion(item, onSelect, inputElement, container, poi);
            });

            // 最后一项不显示边框
            if (index === pois.length - 1 || index === 7) {
                item.style.borderBottom = 'none';
            }

            container.appendChild(item);
        });

        container.style.display = 'block';

        // 添加暗色主题支持
        this.updateThemeStyles(container);
    }

    /**
     * 隐藏建议列表
     */
    hideSuggestions(container) {
        container.style.display = 'none';
        container.innerHTML = '';
    }

        /**
     * 更新选择状态
     */
    updateSelection(items, selectedIndex) {
        this.clearSelection(items[0].parentNode);

        if (selectedIndex >= 0 && items[selectedIndex]) {
            // 添加选中类而不是直接设置样式
            items[selectedIndex].classList.add('selected');

            // 滚动到可见区域
            items[selectedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }

    /**
     * 清除选择状态
     */
    clearSelection(container) {
        const items = container.querySelectorAll('.custom-suggest-item');
        items.forEach(item => {
            // 移除内联样式和选中类，让CSS类来控制样式
            item.style.removeProperty('background');
            item.style.removeProperty('color');
            item.classList.remove('selected');
        });
    }

    /**
     * 选择建议项
     */
    selectSuggestion(item, onSelect, inputElement, container, poi = null) {
        if (!poi) {
            // 从DOM中提取POI信息（备用方案）
            const name = item.querySelector('div:first-child').textContent;
            const address = item.querySelector('div:last-child').textContent;
            poi = { name, address };
        }

        // 设置输入框值
        inputElement.value = poi.name;

        // 提取位置信息
        const locationInfo = this.extractLocationInfo(poi);

        // 调用回调
        onSelect(poi.name, locationInfo);

        // 隐藏建议列表
        this.hideSuggestions(container);

        // 移除焦点
        inputElement.blur();
    }

        /**
     * 更新主题样式
     */
    updateThemeStyles(container) {
        const isDark = document.documentElement.classList.contains('dark');

        if (isDark) {
            // 暗色主题：使用更深的背景色确保不透明
            container.style.setProperty('background', 'var(--vp-c-bg-soft)', 'important');
            container.style.setProperty('border-color', 'var(--vp-c-divider)', 'important');
            container.style.setProperty('box-shadow', '0 12px 40px rgba(0, 0, 0, 0.5)', 'important');

            const items = container.querySelectorAll('.custom-suggest-item');
            items.forEach(item => {
                const name = item.querySelector('div:first-child');
                const address = item.querySelector('div:last-child');
                if (name) name.style.setProperty('color', 'var(--vp-c-text-1)', 'important');
                if (address) address.style.setProperty('color', 'var(--vp-c-text-2)', 'important');
            });
        } else {
            // 浅色主题：确保背景完全不透明
            container.style.setProperty('background', 'var(--vp-c-bg)', 'important');
            container.style.setProperty('border-color', 'var(--vp-c-divider)', 'important');
            container.style.setProperty('box-shadow', '0 12px 40px rgba(0, 0, 0, 0.2)', 'important');

            const items = container.querySelectorAll('.custom-suggest-item');
            items.forEach(item => {
                const name = item.querySelector('div:first-child');
                const address = item.querySelector('div:last-child');
                if (name) name.style.setProperty('color', 'var(--vp-c-text-1)', 'important');
                if (address) address.style.setProperty('color', 'var(--vp-c-text-2)', 'important');
            });
        }
    }

    /**
     * 提取地点详细信息
     */
    extractLocationInfo(poi) {
        if (!poi) return null;

        // 解析地址信息，通常格式为："省市区具体地址"
        const address = poi.district || poi.address || '';
        const addressParts = this.parseAddress(address);

        return {
            lng: poi.location?.lng || null,
            lat: poi.location?.lat || null,
            province: addressParts.province || null,
            city: addressParts.city || null,
            district: addressParts.district || null,
            address: address,
            adcode: poi.adcode || null
        };
    }

    /**
     * 解析地址字符串，提取省市区信息
     */
    parseAddress(address) {
        if (!address) return { province: null, city: null, district: null };

        // 常见的省份后缀
        const provincePattern = /(.*?)(省|自治区|市|特别行政区)/;
        // 常见的城市后缀
        const cityPattern = /(.*?)(市|地区|州|盟)/;
        // 常见的区县后缀
        const districtPattern = /(.*?)(区|县|市|旗)/;

        let province = null;
        let city = null;
        let district = null;

        // 尝试匹配省份
        const provinceMatch = address.match(provincePattern);
        if (provinceMatch) {
            province = provinceMatch[0];
            address = address.replace(province, '');
        }

        // 尝试匹配城市
        const cityMatch = address.match(cityPattern);
        if (cityMatch) {
            city = cityMatch[0];
            address = address.replace(city, '');
        }

        // 尝试匹配区县
        const districtMatch = address.match(districtPattern);
        if (districtMatch) {
            district = districtMatch[0];
        }

        return { province, city, district };
    }

    /**
     * 封装地图脚本加载
     */
    loadAMapScript(key) {
        return new Promise(async (resolve, reject) => {
            if (window.AMap) return resolve(window.AMap);

            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/amap`, {
                    method: 'GET',
                    credentials: 'include'
                });
                if (!response.ok) {
                    throw new Error('地图脚本加载失败，请检查网络连接');
                }

                const { scriptUrl } = await response.json();

                const script = document.createElement('script');
                script.src = scriptUrl;
                script.onload = () => resolve(window.AMap);
                script.onerror = (err) => reject(
                    new Error('地图脚本加载失败，请检查网络连接', { cause: err })
                );
                document.head.appendChild(script);
            } catch (err) {
                reject(new Error('地图脚本加载失败，请检查网络连接', { cause: err }));
            }
        });
    }

    /**
     * 封装地理编码操作
     */
    async getGeocodePosition(geocoder, address) {
        return new Promise((resolve, reject) => {
            geocoder.getLocation(address, (status, result) => {
                if (status === 'complete' && result.geocodes.length) {
                    resolve(result.geocodes[0].location);
                } else {
                    reject(new Error(`无法解析地址: ${address}`));
                }
            });
        });
    }

    /**
     * 初始化地图实例
     */
    async initMap(index, start, end, policy, circleLocations) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        const { s_lng, s_lat } = start;
        const { e_lng, e_lat } = end;

        try {
            const map = new this.AMap.Map(`map-container-${index}`, {
                renderer: "canvas",
                resizeEnable: true,
                viewMode: "2D",
                crossOrigin: 'anonymous',
                WebGLParams: {
                    preserveDrawingBuffer: true
                }
            });

            // 确保数组有足够的长度，并在对应索引位置设置地图实例
            while (this.mapInstances.length <= index) {
                this.mapInstances.push(null);
            }
            this.mapInstances[index] = map;

            // 构造路线导航类
            const driving = new this.AMap.Driving({
                map: map,
                panel: "",  // 指定空字符串禁用默认面板
                renderer: "canvas", // 使用Canvas绘制路线
                policy: policy
            });

            // 等待路线规划完成
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error(`路线规划超时: ${index}`));
                }, 15000); // 15秒超时

                // 根据起终点经纬度信息规划驾车导航路线
                driving.search(new AMap.LngLat(s_lng, s_lat), new AMap.LngLat(e_lng, e_lat), {waypoints: circleLocations}, function (status, result) {
                    clearTimeout(timeout);
                    if (status !== 'complete') {
                        log.error('获取驾车数据失败：' + result);
                        reject(new Error(`路线规划失败: ${result}`));
                    } else {
                        // 给地图一些时间完成渲染
                        setTimeout(() => {
                            resolve();
                        }, 1000); // 等待1秒确保地图完全渲染
                    }
                });
            });

            return map;
        } catch (err) {
            log.error('地图初始化失败:', err);
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 驾车路线规划
     */
    async drivingPlanning(index, start, end, policy = 0, circleLocations = []) {
        try {
            const container = document.getElementById(`map-container-${index}`);
            if (!container) {
                log.error('地图容器未找到');
                throw new Error('地图容器未找到');
            }

            // 先销毁已存在的地图实例
            this.destroyMapInstance(index);

            await this.initMap(index, start, end, policy, circleLocations);
        } catch (err) {
            log.error('路线生成错误', err);
            throw new Error(err.message || '路线生成失败');
        }
    }

    /**
     * 保存地图为图片
     */
    async saveMapAsImage(index, account, formattedDateTime, vueInstance = null) {
        let originalExpandedState = null;
        let originalSelectedDay = null;
        let wasTemporarilyExpanded = false;

        try {
            const mapContainer = document.getElementById(`map-container-${index}`);
            if (!mapContainer) {
                throw new Error('地图容器未找到');
            }

            // 检查地图实例是否存在
            const mapInstance = this.mapInstances[index];
            if (!mapInstance) {
                throw new Error(`地图实例 ${index} 未找到`);
            }

            // 检查地图容器是否可见，如果不可见则临时展开
            const mapWrapper = mapContainer.closest('.planning-box');
            if (mapWrapper && vueInstance) {
                const computedStyle = window.getComputedStyle(mapWrapper);
                const isHidden = computedStyle.display === 'none' || computedStyle.visibility === 'hidden';

                if (isHidden) {
                    // 保存当前的折叠状态
                    originalExpandedState = vueInstance.expandedSectionType;
                    originalSelectedDay = vueInstance.selectedDayIndex;
                    wasTemporarilyExpanded = true;

                    // 临时展开地图区域
                    vueInstance.expandedSectionType = 'driving';
                    vueInstance.selectedDayIndex = index;

                    // 等待DOM更新
                    await vueInstance.$nextTick();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            // 再次检查地图容器是否现在可见
            const containerRect = mapContainer.getBoundingClientRect();
            if (containerRect.width === 0 || containerRect.height === 0) {
                // 如果仍然不可见，尝试强制显示
                const tempStyles = [];
                let currentElement = mapContainer;

                while (currentElement && currentElement !== document.body) {
                    const style = window.getComputedStyle(currentElement);
                    if (style.display === 'none') {
                        tempStyles.push({
                            element: currentElement,
                            originalDisplay: currentElement.style.display
                        });
                        currentElement.style.display = 'block';
                    }
                    if (style.visibility === 'hidden') {
                        tempStyles.push({
                            element: currentElement,
                            originalVisibility: currentElement.style.visibility
                        });
                        currentElement.style.visibility = 'visible';
                    }
                    currentElement = currentElement.parentElement;
                }

                // 等待重新渲染
                await new Promise(resolve => setTimeout(resolve, 500));

                // 强制刷新地图以确保渲染完成
                if (typeof mapInstance.resize === 'function') {
                    mapInstance.resize();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                try {
                    // 进行截图
                    const canvas = await this.captureMapCanvas(mapContainer, index);
                    const imageData = canvas.toDataURL('image/png');

                    // 检查图片数据是否有效
                    if (!imageData || imageData.length < 1000) {
                        throw new Error('地图图片数据无效或过小');
                    }

                    // 发送图片数据到后端
                    const result = await this.uploadMapImage(imageData, account, formattedDateTime, index);
                    return result;
                } finally {
                    // 恢复所有临时修改的样式
                    tempStyles.forEach(({ element, originalDisplay, originalVisibility }) => {
                        if (originalDisplay !== undefined) {
                            element.style.display = originalDisplay;
                        }
                        if (originalVisibility !== undefined) {
                            element.style.visibility = originalVisibility;
                        }
                    });
                }
            }

            // 如果地图容器可见，直接进行正常截图流程
            // 等待地图完全渲染
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 检查地图容器是否有内容
            const mapContent = mapContainer.querySelector('.amap-container');
            if (!mapContent) {
                await new Promise(resolve => setTimeout(resolve, 3000));
            }

            // 强制刷新地图以确保渲染完成
            if (typeof mapInstance.resize === 'function') {
                mapInstance.resize();
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 进行截图
            const canvas = await this.captureMapCanvas(mapContainer, index);
            const imageData = canvas.toDataURL('image/png');

            // 检查图片数据是否有效
            if (!imageData || imageData.length < 1000) {
                throw new Error('地图图片数据无效或过小');
            }

            // 发送图片数据到后端
            const result = await this.uploadMapImage(imageData, account, formattedDateTime, index);
            return result;

        } catch (err) {
            log.error('保存地图为图片失败:', err);
            throw err;
        } finally {
            // 恢复原始的折叠状态
            if (wasTemporarilyExpanded && vueInstance) {
                // 延迟恢复，确保截图完成
                setTimeout(() => {
                    vueInstance.expandedSectionType = originalExpandedState;
                    vueInstance.selectedDayIndex = originalSelectedDay;
                }, 500);
            }
        }
    }

    /**
     * 截取地图画布
     */
    async captureMapCanvas(mapContainer, index) {
        const canvas = await html2canvas(mapContainer, {
            useCORS: true,        // 启用跨域
            allowTaint: true,     // 允许污染模式
            logging: false,       // 关闭详细日志，避免干扰
            scale: 2,             // 提高分辨率
            backgroundColor: '#f5f5f5', // 设置背景色
            onclone: (clonedDoc) => {
                // 在克隆文档中找到地图容器，确保其可见
                const clonedContainer = clonedDoc.getElementById(`map-container-${index}`);
                if (clonedContainer) {
                    clonedContainer.style.display = 'block';
                    clonedContainer.style.visibility = 'visible';
                    clonedContainer.style.opacity = '1';
                    clonedContainer.style.position = 'relative';
                    clonedContainer.style.zIndex = '1';
                }

                // 确保所有父容器也是可见的
                let parent = clonedContainer?.parentElement;
                while (parent && parent !== clonedDoc.body) {
                    parent.style.display = 'block';
                    parent.style.visibility = 'visible';
                    parent.style.opacity = '1';
                    parent = parent.parentElement;
                }
            },
            ignoreElements: (element) => {
                // 忽略地图控制按钮等不需要的元素
                return element.classList.contains('map-controls') ||
                       element.classList.contains('amap-copyright') ||
                       element.classList.contains('header-toggle') ||
                       element.id === 'some-obstructive-element';
            },
        });

        // 检查截图是否成功
        if (!canvas || canvas.width === 0 || canvas.height === 0) {
            throw new Error(`地图截图失败: 画布尺寸为 ${canvas?.width || 0}x${canvas?.height || 0}`);
        }

        return canvas;
    }

    /**
     * 上传地图图片到后端
     */
    async uploadMapImage(imageData, account, formattedDateTime, index) {
        const response = await fetch(`${BACKEND_SRV_URL}/api/save_amap_img`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                image: imageData, // Base64 图片数据
                user: account,
                filename: `map-${formattedDateTime}-${index}.png` // 文件名
            })
        });

        if (!response.ok) {
            log.error('图片保存失败，请检查网络连接', response);
            throw new Error('图片保存失败');
        }

        return await response.json();
    }

    /**
     * 销毁指定索引的地图实例
     */
    destroyMapInstance(index) {
        if (this.mapInstances[index]) {
            try {
                this.mapInstances[index].destroy();
            } catch (err) {
                log.warn(`销毁地图实例 ${index} 时出错:`, err);
            }
            this.mapInstances[index] = null;
        }
    }

    /**
     * 清理地图实例
     */
    cleanup() {
        this.mapInstances.forEach((map, index) => {
            if (map && map.destroy) {
                try {
                    map.destroy();
                } catch (err) {
                    log.warn(`清理地图实例 ${index} 时出错:`, err);
                }
            }
        });
        this.mapInstances = [];

        // 清理自定义自动完成组件
        this.cleanupCustomAutoComplete();
    }

    /**
     * 清理自定义自动完成组件
     */
    cleanupCustomAutoComplete() {
        const inputIds = ['start-tipinput', 'end-tipinput'];

        inputIds.forEach(inputId => {
            const inputElement = document.getElementById(inputId);
            if (inputElement && inputElement._cleanup) {
                inputElement._cleanup();
                delete inputElement._cleanup;
            }

            // 移除建议容器
            const suggestContainer = document.getElementById(`${inputId}-suggest`);
            if (suggestContainer && suggestContainer.parentNode) {
                suggestContainer.parentNode.removeChild(suggestContainer);
            }
        });
    }

    /**
     * 获取地图实例
     */
    getMapInstance(index) {
        return this.mapInstances[index];
    }

    /**
     * 获取所有地图实例
     */
    getAllMapInstances() {
        return this.mapInstances;
    }

    /**
 * 使用高德地图地理编码API精确获取地点坐标
 * 这比AI查询更准确，适用于获取中途地点的坐标
 * 遇到异常或超时时返回空对象，不会中断整个规划流程
 */
    async getAccurateCoordinates(address, province, city) {
        try {
            if (!this.AMap) {
                console.warn('地图服务未初始化，返回空对象');
                return {};
            }

            return new Promise((resolve, reject) => {
                // 添加超时处理，避免API卡死
                const timeout = setTimeout(() => {
                    console.warn(`地理编码超时: ${address}，返回空对象`);
                    resolve({}); // 超时时返回空对象而不是reject
                }, 10000); // 10秒超时

                try {
                    this.AMap.plugin('AMap.Geocoder', () => {
                        try {
                            const geocoder = new this.AMap.Geocoder({
                                city: city,
                                radius: 500,
                                extensions: "all"
                            });

                            geocoder.getLocation(address, (status, result) => {
                                try {
                                    if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                                        clearTimeout(timeout);
                                        resolve(result.geocodes[0].location);
                                    } else {
                                        const errorMsg = `高德地理编码失败 - 地址: ${address}, 状态: ${status}`;
                                        console.warn(errorMsg, result);
                                        clearTimeout(timeout);
                                        resolve({}); // 编码失败时返回空对象
                                    }
                                } catch (callbackError) {
                                    console.warn(`地理编码回调处理错误:`, callbackError);
                                    clearTimeout(timeout);
                                    resolve({}); // 回调错误时返回空对象
                                }
                            });
                        } catch (pluginError) {
                            console.warn(`地理编码插件加载错误:`, pluginError);
                            clearTimeout(timeout);
                            resolve({}); // 插件错误时返回空对象
                        }
                    });
                } catch (promiseError) {
                    console.warn(`地理编码Promise创建错误:`, promiseError);
                    clearTimeout(timeout);
                    resolve({}); // Promise错误时返回空对象
                }
            });
        } catch (outerError) {
            // 最外层异常捕获，确保不会抛出异常
            console.warn(`地理编码外层异常:`, outerError);
            return {}; // 返回空对象
        }
    }

    /**
     * 计算两点之间的距离（公里）
     */
    calculateDistance(lng1, lat1, lng2, lat2) {
        const R = 6371; // 地球半径（公里）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    /**
* 批量获取多个地点的精确坐标
* 适用于一次性获取多个中途地点的坐标
*/
    async getBatchAccurateCoordinates(addresses, referenceLocation = null) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        if (!Array.isArray(addresses) || addresses.length === 0) {
            return [];
        }

        const results = [];

        // 使用Promise.allSettled来处理多个并发请求，避免某个地点失败影响其他地点
        const promises = addresses.map(address =>
            this.getAccurateCoordinates(address, referenceLocation).catch(error => ({
                error: error.message,
                address: address
            }))
        );

        const settled = await Promise.allSettled(promises);

        for (let i = 0; i < settled.length; i++) {
            const result = settled[i];
            if (result.status === 'fulfilled') {
                if (result.value.error) {
                    // 地理编码失败的情况
                    results.push({
                        success: false,
                        address: addresses[i],
                        error: result.value.error,
                        lng: null,
                        lat: null
                    });
                } else {
                    // 成功获取坐标
                    results.push({
                        success: true,
                        address: addresses[i],
                        ...result.value
                    });
                }
            } else {
                // Promise本身失败
                results.push({
                    success: false,
                    address: addresses[i],
                    error: result.reason?.message || '未知错误',
                    lng: null,
                    lat: null
                });
            }
        }

        const successCount = results.filter(r => r.success).length;
        return results;
    }
}

// 创建单例实例
export const mapService = new TopmeansMapService();
