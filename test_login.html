<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>TopMeansLab 登录功能测试</h1>
    
    <div class="test-section">
        <h2>测试环境检查</h2>
        <p>前端地址: <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></p>
        <p>后端地址: <a href="http://localhost:3999" target="_blank">http://localhost:3999</a></p>
        <button class="btn-primary" onclick="testBackendHealth()">测试后端健康状态</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h2>注册功能测试</h2>
        <button class="btn-primary" onclick="testRegister()">测试注册新用户</button>
        <div id="register-result"></div>
    </div>

    <div class="test-section">
        <h2>登录功能测试</h2>
        <button class="btn-success" onclick="testLogin()">测试用户登录</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h2>功能验证清单</h2>
        <ul>
            <li>✅ 创建了全屏登录页面组件 (LoginPage.vue)</li>
            <li>✅ 修改了验证码组件支持不区分大小写</li>
            <li>✅ 创建了登录页面路由 (/login)</li>
            <li>✅ 修改了路由守卫，未登录用户自动跳转到登录页</li>
            <li>✅ 后端API正常工作 (注册、登录)</li>
            <li>✅ 前端环境变量配置正确</li>
            <li>✅ 数据库连接正常</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>使用说明</h2>
        <ol>
            <li>访问 <a href="http://localhost:5173" target="_blank">http://localhost:5173</a> 会自动跳转到登录页面</li>
            <li>或直接访问 <a href="http://localhost:5173/login" target="_blank">http://localhost:5173/login</a></li>
            <li>可以注册新用户或使用测试用户登录：
                <ul>
                    <li>账号: testuser</li>
                    <li>密码: test123456</li>
                </ul>
            </li>
            <li>验证码不区分大小写</li>
            <li>支持记住密码功能</li>
            <li>登录成功后会跳转到首页</li>
        </ol>
    </div>

    <script>
        async function testBackendHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch('http://localhost:3999/api/health');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 后端服务正常</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 后端服务异常</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRegister() {
            const resultDiv = document.getElementById('register-result');
            const testUser = {
                account: `testuser_${Date.now()}`,
                password: 'test123456',
                nickname: '测试用户',
                avatar: '/images/default-avatar.jpg',
                signature: '这是一个测试用户'
            };

            try {
                const response = await fetch('http://localhost:3999/api/user/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testUser)
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 注册成功</h4>
                            <p>新用户账号: ${testUser.account}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 注册失败</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 注册请求失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const loginData = {
                account: 'testuser',
                password: 'test123456'
            };

            try {
                const response = await fetch('http://localhost:3999/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 登录成功</h4>
                            <p>用户: ${data.user.nickname}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 登录失败</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 登录请求失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 页面加载时自动测试后端健康状态
        window.onload = function() {
            testBackendHealth();
        };
    </script>
</body>
</html>
