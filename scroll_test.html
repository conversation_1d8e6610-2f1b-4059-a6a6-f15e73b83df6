<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .mobile-frame {
            width: 375px;
            height: 667px;
            border: 3px solid #333;
            border-radius: 20px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            background: #000;
        }
        .mobile-screen {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .test-steps {
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .scroll-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 移动端滚动功能测试</h1>
        
        <div class="test-info">
            <h3>🎯 测试目标</h3>
            <ul>
                <li>验证移动端页面可以正常上下滚动</li>
                <li>验证验证码区域布局正确</li>
                <li>确保所有内容都能完整查看</li>
            </ul>
        </div>

        <div class="test-steps">
            <h3>📋 测试步骤</h3>
            <ol>
                <li>在下方模拟器中查看登录页面</li>
                <li>尝试向下滚动查看完整的登录表单</li>
                <li>检查验证码和"看不清，换一张"的位置</li>
                <li>测试登录功能是否正常</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="refreshFrame()">🔄 刷新页面</button>
            <button onclick="clearStorage()">🗑️ 清除登录状态</button>
            <button onclick="testScroll()">📏 测试滚动</button>
        </div>

        <div class="mobile-frame">
            <div class="scroll-indicator" id="scrollIndicator">滚动位置: 0px</div>
            <iframe 
                id="mobileScreen" 
                class="mobile-screen" 
                src="http://localhost:5173/"
                title="移动端登录页面">
            </iframe>
        </div>

        <div class="test-info success">
            <h3>✅ 预期效果</h3>
            <ul>
                <li><strong>滚动功能</strong>：页面可以流畅上下滚动</li>
                <li><strong>验证码布局</strong>：在移动端，验证码输入框在上方，验证码图片和"看不清，换一张"在下方</li>
                <li><strong>完整显示</strong>：所有内容都在登录框内，没有溢出</li>
                <li><strong>用户体验</strong>：可以轻松查看和操作所有表单元素</li>
            </ul>
        </div>

        <div class="test-steps">
            <h3>🔧 技术修复点</h3>
            <ul>
                <li><code>overflow-y: auto</code> - 允许垂直滚动</li>
                <li><code>-webkit-overflow-scrolling: touch</code> - iOS平滑滚动</li>
                <li><code>flex-direction: column</code> - 验证码区域垂直布局</li>
                <li><code>min-height: calc(100vh + 100px)</code> - 确保有足够的滚动空间</li>
            </ul>
        </div>

        <div class="test-info">
            <h3>🧪 测试账号</h3>
            <p><strong>账号:</strong> testuser</p>
            <p><strong>密码:</strong> test123456</p>
            <p><strong>验证码:</strong> 不区分大小写</p>
        </div>
    </div>

    <script>
        function refreshFrame() {
            const iframe = document.getElementById('mobileScreen');
            iframe.src = iframe.src;
        }

        function clearStorage() {
            // 通过postMessage与iframe通信清除localStorage
            const iframe = document.getElementById('mobileScreen');
            iframe.contentWindow.postMessage({action: 'clearStorage'}, '*');
            setTimeout(() => {
                refreshFrame();
            }, 500);
        }

        function testScroll() {
            alert('请在模拟器中手动测试滚动功能：\n1. 用鼠标滚轮滚动\n2. 拖拽滚动条\n3. 触摸滑动（如果支持）');
        }

        // 监听iframe滚动事件（如果可能）
        window.addEventListener('message', function(event) {
            if (event.data.type === 'scroll') {
                document.getElementById('scrollIndicator').textContent = 
                    `滚动位置: ${event.data.scrollY}px`;
            }
        });

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('移动端滚动测试页面已加载');
            
            // 检查服务状态
            Promise.all([
                fetch('http://localhost:5173/').then(r => r.ok),
                fetch('http://localhost:3999/api/health').then(r => r.ok)
            ]).then(([frontend, backend]) => {
                if (frontend && backend) {
                    console.log('✅ 前端和后端服务都正常');
                } else {
                    console.warn('⚠️ 部分服务可能未启动');
                }
            }).catch(error => {
                console.error('❌ 服务检查失败:', error);
            });
        };
    </script>
</body>
</html>
