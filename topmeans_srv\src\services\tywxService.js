// 本文件提供阿里通义万相 AI API 服务
const logger = require('../log/logger');
const fetch = require("node-fetch");

const TYWX_AI_IMG_TASK_URL = process.env.TYWX_AI_IMG_TASK_URL;
const TYWX_AI_IMG_FETCH_URL = process.env.TYWX_AI_IMG_FETCH_URL;
const TYWX_AI_IMG_ACCESS_KEY = process.env.TYWX_AI_IMG_ACCESS_KEY;

async function createImgGenTask(prompt) {
    try {
        const fullUrl = TYWX_AI_IMG_TASK_URL;

        const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-DashScope-Async' : 'enable',
                'Authorization': `Bearer ${TYWX_AI_IMG_ACCESS_KEY}`,
            },
            body: JSON.stringify({
                "model": "wanx2.1-t2i-turbo",
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "size": "800*600",
                    "n": 1
                }
            })
        });

        // 检查响应状态
        if (!response.ok) {
            const errorText = await response.text();
            logger.error(`请求失败, 状态码: ${response.status}, 错误信息: ${errorText}`);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        // 解析响应数据
        const data = await response.json();
        const task_id = data.output.task_id;
        logger.info(`通义万相生成图片任务已创建,task_id:${task_id}`);

        // 轮询等待
        const imgApiUrl = `${TYWX_AI_IMG_FETCH_URL}${task_id}`;
        // 最大重试次数
        const maxRetries = 30;
        let retryCount = 0;

        while (retryCount++ < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            const response = await fetch(imgApiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TYWX_AI_IMG_ACCESS_KEY}`,
                }
            })

            if (!response.ok) {
                const errorText = await response.text();
                logger.error(`请求失败, 状态码: ${response.status}, 错误信息: ${errorText}`);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const subData = await response.json();
            const status = subData.output.task_status;
            if (status === 'SUCCEEDED') {
                logger.info(`图片生成成功，prompt: ${prompt}，url: ${subData.output.results[0].url}`)
                return {success: true, url: subData.output.results[0].url};
            }
        }
        return {success: false, url: ''};
    } catch (error) {
        logger.error(`通义万相生成图片失败: ${error.message}`);
        throw error;
    }
}

exports.fetchAIImg = async (req, res) => {
    const { prompt } = req.body;

    if (!prompt) {
        logger.error(`缺少参数：${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    try {
        const result = await createImgGenTask(prompt);
        return res.json(result);
    } catch (error) {
        logger.error(`处理请求失败: ${error.message}`);
        return res.json({success: false, url: ''});
    }
}
