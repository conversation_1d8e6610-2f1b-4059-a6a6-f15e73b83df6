{"version": 3, "sources": ["../../src/utils.js", "../../src/options.js", "../../src/unicode.js", "../../src/tokenize.js", "../../src/utils-ast.js", "../../src/traverse.js", "../../src/parse.js", "../../src/transform.js", "../../src/generate.js", "../../src/subclass.js", "../../src/index.js"], "sourcesContent": ["import {EsVersion, Target} from './options.js';\n\nconst cp = String.fromCodePoint;\nconst r = String.raw;\n\nconst envFlags = {\n  flagGroups: (() => {\n    try {\n      new RegExp('(?i:)');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n  unicodeSets: (() => {\n    try {\n      new RegExp('', 'v');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n};\n// Detect WebKit bug: <github.com/slevithan/oniguruma-to-es/issues/30>\nenvFlags.literalHyphenIncorrectlyCreatesRange = (() => {\n  if (!envFlags.unicodeSets) {\n    return false;\n  }\n  try {\n    new RegExp(r`[\\d\\-a]`, 'v');\n  } catch {\n    return true;\n  }\n  return false;\n})();\n\nfunction getNewCurrentFlags(current, {enable, disable}) {\n  return {\n    dotAll: !disable?.dotAll && !!(enable?.dotAll || current.dotAll),\n    ignoreCase: !disable?.ignoreCase && !!(enable?.ignoreCase || current.ignoreCase),\n  };\n}\n\nfunction getOrInsert(map, key, defaultValue) {\n  if (!map.has(key)) {\n    map.set(key, defaultValue);\n  }\n  return map.get(key);\n}\n\n/**\n@param {keyof Target} target\n@param {keyof Target} min\n@returns {boolean}\n*/\nfunction isMinTarget(target, min) {\n  return EsVersion[target] >= EsVersion[min];\n}\n\nfunction throwIfNot(value, msg) {\n  if (!value) {\n    throw new Error(msg ?? 'Value expected');\n  }\n  return value;\n}\n\nexport {\n  cp,\n  envFlags,\n  getNewCurrentFlags,\n  getOrInsert,\n  isMinTarget,\n  r,\n  throwIfNot,\n};\n", "import {envFlags} from './utils.js';\n\nconst Accuracy = /** @type {const} */ ({\n  default: 'default',\n  strict: 'strict',\n});\n\nconst EsVersion = {\n  ES2025: 2025,\n  ES2024: 2024,\n  ES2018: 2018,\n};\n\nconst Target = /** @type {const} */ ({\n  auto: 'auto',\n  ES2025: 'ES2025',\n  ES2024: 'ES2024',\n  ES2018: 'ES2018',\n});\n\n/**\nReturns a complete set of options, with default values set for options that weren't provided.\n@param {import('.').ToRegExpOptions} [options]\n@returns {Required<import('.').ToRegExpOptions>}\n*/\nfunction getOptions(options) {\n  if (options?.target !== undefined && !Target[options.target]) {\n    throw new Error(`Unexpected target \"${options.target}\"`)\n  }\n  // Set default values\n  const opts = {\n    // Sets the level of emulation rigor/strictness.\n    accuracy: 'default',\n    // Disables advanced emulation that relies on returning a `RegExp` subclass, resulting in\n    // certain patterns not being emulatable.\n    avoidSubclass: false,\n    // Oniguruma flags; a string with `i`, `m`, `x`, `D`, `S`, `W` in any order (all optional).\n    // Oniguruma's `m` is equivalent to JavaScript's `s` (`dotAll`).\n    flags: '',\n    // Include JavaScript flag `g` (`global`) in the result.\n    global: false,\n    // Include JavaScript flag `d` (`hasIndices`) in the result.\n    hasIndices: false,\n    // Delay regex construction until first use if the transpiled pattern is at least this length.\n    lazyCompileLength: Infinity,\n    // JavaScript version used for generated regexes. Using `auto` detects the best value based on\n    // your environment. Later targets allow faster processing, simpler generated source, and\n    // support for additional features.\n    target: 'auto',\n    // Disables optimizations that simplify the pattern when it doesn't change the meaning.\n    verbose: false,\n    ...options,\n    // Advanced options that override standard behavior, error checking, and flags when enabled.\n    rules: {\n      // Useful with TextMate grammars that merge backreferences across patterns.\n      allowOrphanBackrefs: false,\n      // Use ASCII-based `\\b` and `\\B`, which increases search performance of generated regexes.\n      asciiWordBoundaries: false,\n      // Allow unnamed captures and numbered calls (backreferences and subroutines) when using\n      // named capture. This is Oniguruma option `ONIG_OPTION_CAPTURE_GROUP`; on by default in\n      // `vscode-oniguruma`.\n      captureGroup: false,\n      // Change the recursion depth limit from Oniguruma's `20` to an integer `2`–`20`.\n      recursionLimit: 20,\n      // `^` as `\\A`; `$` as`\\Z`. Improves search performance of generated regexes without changing\n      // meaning if searching line by line. This is Oniguruma option `ONIG_OPTION_SINGLELINE`.\n      singleline: false,\n      ...(options?.rules),\n    },\n  };\n  if (opts.target === 'auto') {\n    opts.target = envFlags.flagGroups ? 'ES2025' : (envFlags.unicodeSets ? 'ES2024' : 'ES2018');\n  }\n  return opts;\n}\n\nexport {\n  Accuracy,\n  EsVersion,\n  getOptions,\n  Target,\n};\n", "import {cp, r} from './utils.js';\n\nconst CharsWithoutIgnoreCaseExpansion = new Set([\n  cp(0x130), // İ\n  cp(0x131), // ı\n]);\n\nfunction getIgnoreCaseMatchChars(char) {\n  // Some chars should not match the chars they case swap to\n  if (CharsWithoutIgnoreCaseExpansion.has(char)) {\n    return [char];\n  }\n  const set = new Set();\n  const lower = char.toLowerCase();\n  // Everything else is based on `lower`\n  const upper = lower.toUpperCase();\n  const title = LowerToTitleCaseMap.get(lower);\n  const altLower = LowerToAlternativeLowerCaseMap.get(lower);\n  const altUpper = LowerToAlternativeUpperCaseMap.get(lower);\n  // Exclude ucase if multiple chars; count code point length. Excludes ucase versions of German\n  // es-zed 'ß', ligatures like 'ﬀ', and chars with no precomposed ucase like 'ŉ'. See\n  // <unicode.org/Public/UNIDATA/SpecialCasing.txt>\n  if ([...upper].length === 1) {\n    set.add(upper);\n  }\n  altUpper && set.add(altUpper);\n  title && set.add(title);\n  // Lcase of 'İ' is multiple chars, but it's excluded by `CharsWithoutIgnoreCaseExpansion`\n  set.add(lower);\n  altLower && set.add(altLower);\n  return [...set];\n}\n\n// The following set includes:\n// - All ES2024 general categories and their aliases (all are supported by Oniguruma). See\n//   <github.com/mathiasbynens/unicode-match-property-value-ecmascript/blob/main/data/mappings.js>\n// - All ES2024 binary properties and their aliases (all are supported by Oniguruma). See\n//   <tc39.es/ecma262/multipage/text-processing.html#table-binary-unicode-properties>\n// Unicode properties must be mapped to property names supported by JS, and must also apply JS's\n// stricter rules for casing, whitespace, and underscores in Unicode property names. In order to\n// remain lightweight, this library assumes properties not in this list are Unicode script names\n// (which require a `Script=` or `sc=` prefix in JS). Unlike JS, Oniguruma doesn't support script\n// extensions, and it supports some properties that aren't supported in JS (including blocks with\n// an `In_` prefix). See also:\n// - Properties supported in Oniguruma: <github.com/kkos/oniguruma/blob/master/doc/UNICODE_PROPERTIES>\n// - Properties supported in JS by spec version: <github.com/eslint-community/regexpp/blob/main/src/unicode/properties.ts>\nconst JsUnicodeProperties = new Set(\n`C Other\nCc Control cntrl\nCf Format\nCn Unassigned\nCo Private_Use\nCs Surrogate\nL Letter\nLC Cased_Letter\nLl Lowercase_Letter\nLm Modifier_Letter\nLo Other_Letter\nLt Titlecase_Letter\nLu Uppercase_Letter\nM Mark Combining_Mark\nMc Spacing_Mark\nMe Enclosing_Mark\nMn Nonspacing_Mark\nN Number\nNd Decimal_Number digit\nNl Letter_Number\nNo Other_Number\nP Punctuation punct\nPc Connector_Punctuation\nPd Dash_Punctuation\nPe Close_Punctuation\nPf Final_Punctuation\nPi Initial_Punctuation\nPo Other_Punctuation\nPs Open_Punctuation\nS Symbol\nSc Currency_Symbol\nSk Modifier_Symbol\nSm Math_Symbol\nSo Other_Symbol\nZ Separator\nZl Line_Separator\nZp Paragraph_Separator\nZs Space_Separator\nASCII\nASCII_Hex_Digit AHex\nAlphabetic Alpha\nAny\nAssigned\nBidi_Control Bidi_C\nBidi_Mirrored Bidi_M\nCase_Ignorable CI\nCased\nChanges_When_Casefolded CWCF\nChanges_When_Casemapped CWCM\nChanges_When_Lowercased CWL\nChanges_When_NFKC_Casefolded CWKCF\nChanges_When_Titlecased CWT\nChanges_When_Uppercased CWU\nDash\nDefault_Ignorable_Code_Point DI\nDeprecated Dep\nDiacritic Dia\nEmoji\nEmoji_Component EComp\nEmoji_Modifier EMod\nEmoji_Modifier_Base EBase\nEmoji_Presentation EPres\nExtended_Pictographic ExtPict\nExtender Ext\nGrapheme_Base Gr_Base\nGrapheme_Extend Gr_Ext\nHex_Digit Hex\nIDS_Binary_Operator IDSB\nIDS_Trinary_Operator IDST\nID_Continue IDC\nID_Start IDS\nIdeographic Ideo\nJoin_Control Join_C\nLogical_Order_Exception LOE\nLowercase Lower\nMath\nNoncharacter_Code_Point NChar\nPattern_Syntax Pat_Syn\nPattern_White_Space Pat_WS\nQuotation_Mark QMark\nRadical\nRegional_Indicator RI\nSentence_Terminal STerm\nSoft_Dotted SD\nTerminal_Punctuation Term\nUnified_Ideograph UIdeo\nUppercase Upper\nVariation_Selector VS\nWhite_Space space\nXID_Continue XIDC\nXID_Start XIDS`.split(/\\s/)\n);\n\nconst JsUnicodePropertiesMap = new Map();\nfor (const p of JsUnicodeProperties) {\n  JsUnicodePropertiesMap.set(slug(p), p);\n}\n\nconst JsUnicodePropertiesOfStrings = new Set([\n  // ES2024 properties of strings; none are supported by Oniguruma\n  'Basic_Emoji',\n  'Emoji_Keycap_Sequence',\n  'RGI_Emoji',\n  'RGI_Emoji_Flag_Sequence',\n  'RGI_Emoji_Modifier_Sequence',\n  'RGI_Emoji_Tag_Sequence',\n  'RGI_Emoji_ZWJ_Sequence',\n]);\n\nconst JsUnicodePropertiesOfStringsMap = new Map();\nfor (const p of JsUnicodePropertiesOfStrings) {\n  JsUnicodePropertiesOfStringsMap.set(slug(p), p);\n}\n\nconst LowerToAlternativeLowerCaseMap = new Map([\n  ['s', cp(0x17F)], // s, ſ\n  [cp(0x17F), 's'], // ſ, s\n]);\n\nconst LowerToAlternativeUpperCaseMap = new Map([\n  [cp(0xDF), cp(0x1E9E)], // ß, ẞ\n  [cp(0x6B), cp(0x212A)], // k, K (Kelvin)\n  [cp(0xE5), cp(0x212B)], // å, Å (Angstrom)\n  [cp(0x3C9), cp(0x2126)], // ω, Ω (Ohm)\n]);\n\n// See <github.com/node-unicode/unicode-16.0.0/tree/main/General_Category/Titlecase_Letter>\nconst LowerToTitleCaseMap = new Map([\n  titleEntry(0x1C5),\n  titleEntry(0x1C8),\n  titleEntry(0x1CB),\n  titleEntry(0x1F2),\n  ...titleRange(0x1F88, 0x1F8F),\n  ...titleRange(0x1F98, 0x1F9F),\n  ...titleRange(0x1FA8, 0x1FAF),\n  titleEntry(0x1FBC),\n  titleEntry(0x1FCC),\n  titleEntry(0x1FFC),\n]);\n\n// Unlike Oniguruma's Unicode properties via `\\p` and `\\P`, these names are case sensitive and\n// don't allow inserting whitespace and underscores. Definitions at\n// <github.com/kkos/oniguruma/blob/master/doc/RE> (see: POSIX bracket: Unicode Case)\n// Note: Handling in the transformer assumes all values here are a single, negateable node that's\n// not pre-negated at the top level. It also uses ASCII versions of `graph` and `print` for target\n// `ES2018` (which doesn't allow intersection) if `accuracy` isn't `strict`\nconst PosixClassesMap = new Map([\n  ['alnum', r`[\\p{Alpha}\\p{Nd}]`],\n  ['alpha', r`\\p{Alpha}`],\n  ['ascii', r`\\p{ASCII}`],\n  ['blank', r`[\\p{Zs}\\t]`],\n  ['cntrl', r`\\p{cntrl}`],\n  ['digit', r`\\p{Nd}`],\n  ['graph', r`[\\P{space}&&\\P{cntrl}&&\\P{Cn}&&\\P{Cs}]`],\n  ['lower', r`\\p{Lower}`],\n  ['print', r`[[\\P{space}&&\\P{cntrl}&&\\P{Cn}&&\\P{Cs}]\\p{Zs}]`],\n  ['punct', r`[\\p{P}\\p{S}]`], // New value from Oniguruma 6.9.9\n  ['space', r`\\p{space}`],\n  ['upper', r`\\p{Upper}`],\n  ['word', r`[\\p{Alpha}\\p{M}\\p{Nd}\\p{Pc}]`],\n  ['xdigit', r`\\p{AHex}`],\n]);\n\n// Apart from the property names provided by Unicode, Oniguruma explicitly adds several names (see\n// <github.com/kkos/oniguruma/blob/master/doc/RE>) that can be used within `\\p{}` and `\\P{}` (those\n// below). These should be listed here in lowercase, though they aren't case sensitive when used\nconst PosixProperties = new Set([\n  'alnum',\n  'blank',\n  'graph',\n  'print',\n  'word',\n  'xdigit',\n  // The following are available with the same name in JS (see `JsUnicodeProperties`), so can be\n  // handled as standard Unicode properties\n  // 'alpha', // (JS: Alpha)\n  // 'ascii', // (JS: ASCII)\n  // 'cntrl', // (JS: cntrl)\n  // 'digit', // (JS: digit)\n  // 'lower', // (JS: Lower)\n  // 'punct', // (JS: punct)\n  // 'space', // (JS: space)\n  // 'upper', // (JS: Upper)\n]);\n\nfunction range(start, end) {\n  // const range = Array.from(Array(end + 1 - start), (_, i) => i + start);\n  // const range = Array(end + 1 - start).fill(start).map((x, i) => x + i);\n  const range = [];\n  for (let i = start; i <= end; i++) {\n    range.push(i);\n  }\n  return range;\n}\n\n// Generates a Unicode property lookup name: lowercase, without spaces, hyphens, underscores\nfunction slug(name) {\n  return name.replace(/[- _]+/g, '').toLowerCase();\n}\n\nfunction titleEntry(codePoint) {\n  const char = cp(codePoint);\n  return [char.toLowerCase(), char];\n}\n\nfunction titleRange(start, end) {\n  return range(start, end).map(codePoint => titleEntry(codePoint));\n}\n\nconst UnicodePropertiesWithSpecificCase = new Set([\n  'Lower', 'Lowercase',\n  'Upper', 'Uppercase',\n  'Ll', 'Lowercase_Letter',\n  'Lt', 'Titlecase_Letter',\n  'Lu', 'Uppercase_Letter',\n  // The `Changes_When_*` properties (and their aliases) could be included, but they're very rare.\n  // Some other properties include a handful of chars with specific cases only, but these chars are\n  // generally extreme edge cases and using such properties case insensitively generally produces\n  // undesired behavior anyway\n]);\n\nexport {\n  getIgnoreCaseMatchChars,\n  JsUnicodeProperties,\n  JsUnicodePropertiesMap,\n  JsUnicodePropertiesOfStringsMap,\n  PosixClassesMap,\n  PosixProperties,\n  slug,\n  UnicodePropertiesWithSpecificCase,\n};\n", "import {PosixClassesMap} from './unicode.js';\nimport {r} from './utils.js';\n\nconst TokenTypes = /** @type {const} */ ({\n  Alternator: 'Alternator',\n  Assertion: 'Assertion',\n  Backreference: 'Backreference',\n  Character: 'Character',\n  CharacterClassClose: 'CharacterClassClose',\n  CharacterClassHyphen: 'CharacterClassHyphen',\n  CharacterClassIntersector: 'CharacterClassIntersector',\n  CharacterClassOpen: 'CharacterClassOpen',\n  CharacterSet: 'CharacterSet',\n  Directive: 'Directive',\n  GroupClose: 'GroupClose',\n  GroupOpen: 'GroupOpen',\n  Subroutine: 'Subroutine',\n  Quantifier: 'Quantifier',\n  // These aren't allowed in char classes, so they aren't equivalent to JS `[\\q{}]`\n  VariableLengthCharacterSet: 'VariableLengthCharacterSet',\n  // Intermediate representation not included in results\n  EscapedNumber: 'EscapedNumber',\n});\n\nconst TokenCharacterSetKinds = {\n  any: 'any',\n  digit: 'digit',\n  dot: 'dot',\n  hex: 'hex',\n  non_newline: 'non_newline',\n  posix: 'posix',\n  property: 'property',\n  space: 'space',\n  word: 'word',\n};\n\nconst TokenDirectiveKinds = {\n  flags: 'flags',\n  keep: 'keep',\n};\n\nconst TokenGroupKinds = {\n  absent_repeater: 'absent_repeater',\n  atomic: 'atomic',\n  capturing: 'capturing',\n  group: 'group',\n  lookahead: 'lookahead',\n  lookbehind: 'lookbehind',\n};\n\nconst EscapeCharCodes = new Map([\n  ['a',  7], // alert/bell (Not available in JS)\n  ['b',  8], // backspace (only in char classes)\n  ['e', 27], // escape (Not available in JS)\n  ['f', 12], // form feed\n  ['n', 10], // line feed\n  ['r', 13], // carriage return\n  ['t',  9], // horizontal tab\n  ['v', 11], // vertical tab\n]);\n\nconst charClassOpenPattern = r`\\[\\^?`;\nconst sharedEscapesPattern = `${\n  // Control char\n  'c.? | C(?:-.?)?'\n}|${\n  // Unicode property; Onig considers `\\p` an identity escape, but e.g. `\\p{`, `\\p{ ^L}`, and\n  // `\\p{gc=L}` are invalid\n  r`[pP]\\{(?:\\^?[-\\x20_]*[A-Za-z][-\\x20\\w]*\\})?`\n}|${\n  // Hex encoded byte sequence; attempt match before other `\\xNN` hex char\n  r`x[89A-Fa-f]\\p{AHex}(?:\\\\x[89A-Fa-f]\\p{AHex})*`\n}|${\n  // Hex char\n  r`u(?:\\p{AHex}{4})? | x\\{[^\\}]*\\}? | x\\p{AHex}{0,2}`\n}|${\n  // Enclosed octal code point\n  r`o\\{[^\\}]*\\}?`\n}|${\n  // Escaped number\n  r`\\d{1,3}`\n}`;\n// Even with flag x, Onig doesn't allow whitespace to separate a quantifier from the `?` or `+`\n// that makes it lazy or possessive. Possessive suffixes don't apply to interval quantifiers\nconst quantifierRe = /[?*+][?+]?|\\{(?:\\d+(?:,\\d*)?|,\\d+)\\}\\??/;\nconst tokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | [gk]<[^>]*>?\n    | [gk]'[^']*'?\n    | .\n  )\n  | \\( (?:\n    \\? (?:\n      [:=!>({]\n      | <[=!]\n      | <[^>]*>\n      | '[^']*'\n      | ~\\|?\n      | #(?:[^)\\\\]|\\\\.?)*\n      | [^:)]*[:)]\n    )?\n    | \\*\n  )?\n  | ${quantifierRe.source}\n  | ${charClassOpenPattern}\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\nconst charClassTokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | .\n  )\n  | \\[:(?:\\^?\\p{Alpha}+|\\^):\\]\n  | ${charClassOpenPattern}\n  | &&\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\n\n/**\n@typedef {{\n  type: keyof TokenTypes;\n  raw: string;\n  [key: string]: string | number | boolean;\n}} Token\n@typedef {{\n  tokens: Array<Token>;\n  flags: {\n    dotAll: boolean;\n    extended: boolean;\n    ignoreCase: boolean;\n  };\n  rules: {\n    captureGroup: boolean;\n    singleline: boolean;\n  };\n}} TokenizerResult\n*/\n/**\n@param {string} pattern Oniguruma pattern.\n@param {string} [flags] Oniguruma flags.\n@param {{\n  captureGroup?: boolean;\n  singleline?: boolean;\n}} [rules] Oniguruma compile-time options.\n@returns {TokenizerResult}\n*/\nfunction tokenize(pattern, flags = '', rules) {\n  rules = {\n    // `ONIG_OPTION_CAPTURE_GROUP`\n    captureGroup: false,\n    // `ONIG_OPTION_SINGLELINE`\n    singleline: false,\n    ...rules,\n  };\n  if (typeof pattern !== 'string') {\n    throw new Error('String expected as pattern');\n  }\n  if (!/^[imxDSW]*$/.test(flags)) {\n    throw new Error(`Flags \"${flags}\" includes unsupported value`);\n  }\n  const extended = flags.includes('x');\n  const xStack = [extended];\n  const context = {\n    captureGroup: rules.captureGroup,\n    getCurrentModX: () => xStack.at(-1),\n    numOpenGroups: 0,\n    popModX() {xStack.pop()},\n    pushModX(isXOn) {xStack.push(isXOn)},\n    replaceCurrentModX(isXOn) {xStack[xStack.length - 1] = isXOn},\n    singleline: rules.singleline,\n  };\n  let tokens = [];\n  let match;\n  tokenRe.lastIndex = 0;\n  while ((match = tokenRe.exec(pattern))) {\n    const result = getTokenWithDetails(context, pattern, match[0], tokenRe.lastIndex);\n    if (result.tokens) {\n      tokens.push(...result.tokens);\n    } else if (result.token) {\n      tokens.push(result.token);\n    }\n    if (result.lastIndex !== undefined) {\n      tokenRe.lastIndex = result.lastIndex;\n    }\n  }\n\n  const potentialUnnamedCaptureTokens = [];\n  let numNamedAndOptInUnnamedCaptures = 0;\n  tokens.forEach(t => {\n    if (t.type === TokenTypes.GroupOpen) {\n      if (t.kind === TokenGroupKinds.capturing) {\n        t.number = ++numNamedAndOptInUnnamedCaptures;\n      } else if (t.raw === '(') {\n        potentialUnnamedCaptureTokens.push(t);\n      }\n    }\n  });\n  // Enable unnamed capturing groups if no named captures (when `captureGroup` not enabled)\n  if (!numNamedAndOptInUnnamedCaptures) {\n    potentialUnnamedCaptureTokens.forEach((t, i) => {\n      t.kind = TokenGroupKinds.capturing;\n      t.number = i + 1;\n    });\n  }\n  const numCaptures = numNamedAndOptInUnnamedCaptures || potentialUnnamedCaptureTokens.length;\n  // Can now split escaped nums accurately, accounting for number of captures\n  tokens = tokens.map(\n    t => t.type === TokenTypes.EscapedNumber ? splitEscapedNumToken(t, numCaptures) : t\n  ).flat();\n\n  return {\n    tokens,\n    flags: {\n      ignoreCase: flags.includes('i'),\n      // Flag m is called `multiline` in Onig, but that has a different meaning in JS. Onig flag m\n      // is equivalent to JS flag s\n      dotAll: flags.includes('m'),\n      // Flag x is fully handled during tokenization\n      extended,\n      // Flags D, S, W are currently only supported as top-level flags\n      digitIsAscii: flags.includes('D'),\n      spaceIsAscii: flags.includes('S'),\n      wordIsAscii: flags.includes('W'),\n    },\n    rules,\n  };\n}\n\nfunction getTokenWithDetails(context, pattern, m, lastIndex) {\n  const [m0, m1] = m;\n\n  if (m0 === '[') {\n    const result = getAllTokensForCharClass(pattern, m, lastIndex);\n    return {\n      // Array of all of the char class's tokens\n      tokens: result.tokens,\n      // Jump forward to the end of the char class\n      lastIndex: result.lastIndex,\n    };\n  }\n\n  if (m0 === '\\\\') {\n    if ('AbBGzZ'.includes(m1)) {\n      return {\n        token: createToken(TokenTypes.Assertion, m, {\n          kind: m,\n        }),\n      };\n    }\n    if (/^\\\\g[<']/.test(m)) {\n      if (!/^\\\\g(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createToken(TokenTypes.Subroutine, m),\n      };\n    }\n    if (/^\\\\k[<']/.test(m)) {\n      if (!/^\\\\k(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createToken(TokenTypes.Backreference, m),\n      };\n    }\n    if (m1 === 'K') {\n      return {\n        token: createToken(TokenTypes.Directive, m, {\n          kind: TokenDirectiveKinds.keep,\n        }),\n      };\n    }\n    if (m1 === 'N') {\n      return {\n        token: createToken(TokenTypes.CharacterSet, m, {\n          kind: TokenCharacterSetKinds.non_newline,\n        }),\n      };\n    }\n    if (m1 === 'O') {\n      return {\n        token: createToken(TokenTypes.CharacterSet, m, {\n          kind: TokenCharacterSetKinds.any,\n        }),\n      };\n    }\n    if ('RX'.includes(m1)) {\n      return {\n        token: createToken(TokenTypes.VariableLengthCharacterSet, m, {\n          kind: m,\n        }),\n      };\n    }\n    // Grapheme boundaries not yet unsupported; avoid treating as an identity escape\n    if ('yY'.includes(m1)) {\n      throw new Error(`Unsupported grapheme boundary \"${m}\"`);\n    }\n    // Run last since it assumes an identity escape as final condition\n    const result = createTokenForSharedEscape(m, {inCharClass: false});\n    return Array.isArray(result) ? {tokens: result} : {token: result};\n  }\n\n  if (m0 === '(') {\n    if (m === '(*') {\n      throw new Error(`Unsupported named callout \"${m}\"`);\n    }\n    if (m === '(?{') {\n      throw new Error(`Unsupported callout \"${m}\"`);\n    }\n    // Comment group\n    if (m === '(?#') {\n      // The closing unescaped `)` isn't included in the match\n      if (pattern[lastIndex] !== ')') {\n        throw new Error('Unclosed comment group \"(?#\"');\n      }\n      return {\n        lastIndex: lastIndex + 1,\n      };\n    }\n    // Flag modifier (directive or group opener)\n    if (/^\\(\\?[-imx]+[:)]$/.test(m)) {\n      return {\n        token: createTokenForFlagMod(m, context),\n      };\n    }\n    // --- Remaining group types all reuse current flag x status ---\n    context.pushModX(context.getCurrentModX());\n    context.numOpenGroups++;\n    if (\n      // Unnamed capture if no named captures present and `captureGroup` not enabled, else\n      // noncapturing group\n      (m === '(' && !context.captureGroup) ||\n      // Noncapturing group\n      m === '(?:'\n    ) {\n      return {\n        token: createToken(TokenTypes.GroupOpen, m, {\n          // For `(`, will later change to `capturing` and add `number` prop if no named captures\n          kind: TokenGroupKinds.group,\n        }),\n      };\n    }\n    // Atomic group\n    if (m === '(?>') {\n      return {\n        token: createToken(TokenTypes.GroupOpen, m, {\n          kind: TokenGroupKinds.atomic,\n        }),\n      };\n    }\n    // Lookaround\n    if (m === '(?=' || m === '(?!' || m === '(?<=' || m === '(?<!') {\n      return {\n        token: createToken(TokenTypes.GroupOpen, m, {\n          kind: m[2] === '<' ? TokenGroupKinds.lookbehind : TokenGroupKinds.lookahead,\n          negate: m.endsWith('!'),\n        }),\n      };\n    }\n    // Unnamed capture when `captureGroup` enabled, or named capture (checked after lookbehind due\n    // to similar syntax)\n    if (\n      (m === '(' && context.captureGroup) ||\n      (m.startsWith('(?<') && m.endsWith('>')) ||\n      (m.startsWith(\"(?'\") && m.endsWith(\"'\"))\n    ) {\n      const token = createToken(TokenTypes.GroupOpen, m, {\n        kind: TokenGroupKinds.capturing,\n        // Will add `number` prop in a second pass\n      });\n      if (m !== '(') {\n        token.name = m.slice(3, -1);\n      }\n      return {\n        token,\n      };\n    }\n    if (m.startsWith('(?~')) {\n      if (m === '(?~|') {\n        throw new Error(`Unsupported absent function kind \"${m}\"`);\n      }\n      return {\n        token: createToken(TokenTypes.GroupOpen, m, {\n          kind: TokenGroupKinds.absent_repeater,\n        }),\n      };\n    }\n    if (m === '(?(') {\n      // Some forms are supportable; can be added\n      throw new Error(`Unsupported conditional \"${m}\"`);\n    }\n    throw new Error(`Invalid or unsupported group option \"${m}\"`);\n  }\n  if (m === ')') {\n    context.popModX();\n    context.numOpenGroups--;\n    if (context.numOpenGroups < 0) {\n      throw new Error('Unmatched \")\"');\n    }\n    return {\n      token: createToken(TokenTypes.GroupClose, m),\n    };\n  }\n\n  if (m === '#' && context.getCurrentModX()) {\n    // Onig's only line break char is line feed\n    const end = pattern.indexOf('\\n', lastIndex);\n    return {\n      // Jump forward to the end of the comment\n      lastIndex: end === -1 ? pattern.length : end,\n    };\n  }\n  if (/^\\s$/.test(m) && context.getCurrentModX()) {\n    const re = /\\s+/y;\n    re.lastIndex = lastIndex;\n    const rest = re.exec(pattern);\n    return {\n      // Jump forward to the end of the whitespace\n      lastIndex: rest ? re.lastIndex : lastIndex,\n    };\n  }\n\n  if (m === '.') {\n    return {\n      token: createToken(TokenTypes.CharacterSet, m, {\n        kind: TokenCharacterSetKinds.dot,\n      }),\n    };\n  }\n\n  if (m === '^' || m === '$') {\n    const kind = context.singleline ? {\n      '^': r`\\A`,\n      '$': r`\\Z`,\n    }[m] : m;\n    return {\n      token: createToken(TokenTypes.Assertion, m, {\n        kind,\n      }),\n    };\n  }\n\n  if (m === '|') {\n    return {\n      token: createToken(TokenTypes.Alternator, m),\n    };\n  }\n\n  if (quantifierRe.test(m)) {\n    return {\n      token: createTokenForQuantifier(m),\n    };\n  }\n\n  assertSingleCodePoint(m);\n  return {\n    token: createToken(TokenTypes.Character, m, {\n      value: m.codePointAt(0),\n    }),\n  };\n}\n\nfunction getAllTokensForCharClass(pattern, opener, lastIndex) {\n  const tokens = [createToken(TokenTypes.CharacterClassOpen, opener, {\n    negate: opener[1] === '^',\n  })];\n  let numCharClassesOpen = 1;\n  let match;\n  charClassTokenRe.lastIndex = lastIndex;\n  while ((match = charClassTokenRe.exec(pattern))) {\n    const m = match[0];\n    // Start of nested char class\n    // POSIX classes are handled as a single token; not as a nested char class\n    if (m[0] === '[' && m[1] !== ':') {\n      numCharClassesOpen++;\n      tokens.push(createToken(TokenTypes.CharacterClassOpen, m, {\n        negate: m[1] === '^',\n      }));\n    } else if (m === ']') {\n      if (tokens.at(-1).type === TokenTypes.CharacterClassOpen) {\n        // Allow unescaped `]` as leading char\n        tokens.push(createToken(TokenTypes.Character, m, {\n          value: 93,\n        }));\n      } else {\n        numCharClassesOpen--;\n        tokens.push(createToken(TokenTypes.CharacterClassClose, m));\n        if (!numCharClassesOpen) {\n          break;\n        }\n      }\n    } else {\n      const result = createTokenForAnyTokenWithinCharClass(m);\n      if (Array.isArray(result)) {\n        tokens.push(...result);\n      } else {\n        tokens.push(result);\n      }\n    }\n  }\n  return {\n    tokens,\n    lastIndex: charClassTokenRe.lastIndex || pattern.length,\n  }\n}\n\nfunction createTokenForAnyTokenWithinCharClass(raw) {\n  if (raw[0] === '\\\\') {\n    // Assumes an identity escape as final condition\n    return createTokenForSharedEscape(raw, {inCharClass: true});\n  }\n  // POSIX class: `[:name:]` or `[:^name:]`\n  if (raw[0] === '[') {\n    const posix = /\\[:(?<negate>\\^?)(?<name>[a-z]+):\\]/.exec(raw);\n    if (!posix || !PosixClassesMap.get(posix.groups.name)) {\n      throw new Error(`Invalid POSIX class \"${raw}\"`);\n    }\n    return createToken(TokenTypes.CharacterSet, raw, {\n      kind: TokenCharacterSetKinds.posix,\n      negate: !!posix.groups.negate,\n      value: posix.groups.name,\n    });\n  }\n  // Range (possibly invalid) or literal hyphen\n  if (raw === '-') {\n    return createToken(TokenTypes.CharacterClassHyphen, raw);\n  }\n  if (raw === '&&') {\n    return createToken(TokenTypes.CharacterClassIntersector, raw);\n  }\n  assertSingleCodePoint(raw);\n  return createToken(TokenTypes.Character, raw, {\n    value: raw.codePointAt(0),\n  });\n}\n\n// Tokens shared by base syntax and char class syntax that start with `\\`\nfunction createTokenForSharedEscape(raw, {inCharClass}) {\n  const char1 = raw[1];\n  if (char1 === 'c' || char1 === 'C') {\n    return createTokenForControlChar(raw);\n  }\n  if ('dDhHsSwW'.includes(char1)) {\n    return createTokenForShorthandCharClass(raw);\n  }\n  if (raw.startsWith(r`\\o{`)) {\n    throw new Error(`Incomplete, invalid, or unsupported octal code point \"${raw}\"`);\n  }\n  if (/^\\\\[pP]\\{/.test(raw)) {\n    if (raw.length === 3) {\n      throw new Error(`Incomplete or invalid Unicode property \"${raw}\"`);\n    }\n    return createTokenForUnicodeProperty(raw);\n  }\n  // Hex UTF-8 encoded byte sequence\n  if (/^\\\\x[89A-Fa-f]\\p{AHex}/u.test(raw)) {\n    try {\n      const bytes = raw.split(/\\\\x/).slice(1).map(hex => parseInt(hex, 16));\n      const decoded = new TextDecoder('utf-8', {\n        ignoreBOM: true,\n        fatal: true,\n      }).decode(new Uint8Array(bytes));\n      const encoder = new TextEncoder();\n      const tokens = [...decoded].map(char => {\n        // Since this regenerates `raw`, it might have different casing for hex A-F than the input\n        const raw = [...encoder.encode(char)].map(byte => `\\\\x${byte.toString(16)}`).join('');\n        return createToken(TokenTypes.Character, raw, {\n          value: char.codePointAt(0),\n        });\n      });\n      return tokens;\n    } catch {\n      throw new Error(`Multibyte code \"${raw}\" incomplete or invalid in Oniguruma`);\n    }\n  }\n  if (char1 === 'u' || char1 === 'x') {\n    return createToken(TokenTypes.Character, raw, {\n      value: getValidatedHexCharCode(raw),\n    });\n  }\n  if (EscapeCharCodes.has(char1)) {\n    return createToken(TokenTypes.Character, raw, {\n      value: EscapeCharCodes.get(char1),\n    });\n  }\n  // Escaped number: backref (possibly invalid), null, octal, or identity escape, possibly followed\n  // by 1-2 literal digits\n  if (/\\d/.test(char1)) {\n    return createToken(TokenTypes.EscapedNumber, raw, {\n      inCharClass,\n    });\n  }\n  if (raw === '\\\\') {\n    throw new Error(r`Incomplete escape \"\\\"`);\n  }\n  // Meta `\\M-x` and `\\M-\\C-x` are unsupported; avoid treating as an identity escape\n  if (char1 === 'M') {\n    // Supportable; see <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#12-onig_syn_op2_esc_capital_m_bar_meta-enable-m-x>, <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>, <https://github.com/ammar/regexp_parser/blob/8851030feda68223d74f502335fb254a20d77016/lib/regexp_parser/expression/classes/escape_sequence.rb#L75>\n    throw new Error(`Unsupported meta \"${raw}\"`);\n  }\n  // Identity escape; count code point length\n  if ([...raw].length === 2) {\n    return createToken(TokenTypes.Character, raw, {\n      value: raw.codePointAt(1),\n    });\n  }\n  throw new Error(`Unexpected escape \"${raw}\"`);\n}\n\n/**\n@param {keyof TokenTypes} type\n@param {string} raw\n@param {{[key: string]: string | number | boolean;}} [data]\n@returns {Token}\n*/\nfunction createToken(type, raw, data) {\n  return {\n    type,\n    raw,\n    ...data,\n  };\n}\n\n// Expects `\\cx` or `\\C-x`\nfunction createTokenForControlChar(raw) {\n  const char = raw[1] === 'c' ? raw[2] : raw[3];\n  if (!char || !/[A-Za-z]/.test(char)) {\n    // Unlike JS, Onig allows any char to follow `\\c` or `\\C-`, but this is an extreme edge case\n    // Supportable; see <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#11-onig_syn_op2_esc_capital_c_bar_control-enable-c-x>, <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>\n    throw new Error(`Unsupported control character \"${raw}\"`);\n  }\n  return createToken(TokenTypes.Character, raw, {\n    value: char.toUpperCase().codePointAt(0) - 64,\n  });\n}\n\nfunction createTokenForFlagMod(raw, context) {\n  // Allows multiple `-` and solo `-` without `on` or `off` flags\n  let {on, off} = /^\\(\\?(?<on>[imx]*)(?:-(?<off>[-imx]*))?/.exec(raw).groups;\n  off ??= '';\n  // Flag x is used directly by the tokenizer since it changes how to interpret the pattern\n  const isXOn = (context.getCurrentModX() || on.includes('x')) && !off.includes('x');\n  const enabledFlags = getFlagPropsForToken(on);\n  const disabledFlags = getFlagPropsForToken(off);\n  const flagChanges = {};\n  enabledFlags && (flagChanges.enable = enabledFlags);\n  disabledFlags && (flagChanges.disable = disabledFlags);\n  // Flag directive; ex: `(?im-x)`\n  if (raw.endsWith(')')) {\n    // Replace flag x value until the end of the current group\n    context.replaceCurrentModX(isXOn);\n    // Can't remove flag directives without flags like `(?-)`; they affect following quantifiers\n    return createToken(TokenTypes.Directive, raw, {\n      kind: TokenDirectiveKinds.flags,\n      flags: flagChanges,\n    });\n  }\n  // Flag group opener; ex: `(?im-x:`\n  if (raw.endsWith(':')) {\n    context.pushModX(isXOn);\n    context.numOpenGroups++;\n    const token = createToken(TokenTypes.GroupOpen, raw, {\n      kind: TokenGroupKinds.group,\n    });\n    if (enabledFlags || disabledFlags) {\n      token.flags = flagChanges;\n    }\n    return token;\n  }\n  throw new Error(`Unexpected flag modifier \"${raw}\"`);\n}\n\nfunction createTokenForQuantifier(raw) {\n  const data = {};\n  if (raw[0] === '{') {\n    const {min, max} = /^\\{(?<min>\\d*)(?:,(?<max>\\d*))?/.exec(raw).groups;\n    const limit = 100_000;\n    if (+min > limit || +max > limit) {\n      throw new Error('Quantifier value unsupported in Oniguruma');\n    }\n    data.min = +min;\n    data.max = max === undefined ? +min : (max === '' ? Infinity : +max);\n    data.greedy = !raw.endsWith('?');\n    // By default, Onig doesn't support making interval quantifiers possessive\n    data.possessive = false;\n  } else {\n    data.min = raw[0] === '+' ? 1 : 0;\n    data.max = raw[0] === '?' ? 1 : Infinity;\n    data.greedy = raw[1] !== '?';\n    data.possessive = raw[1] === '+';\n  }\n  return createToken(TokenTypes.Quantifier, raw, data);\n}\n\nfunction createTokenForShorthandCharClass(raw) {\n  const lower = raw[1].toLowerCase();\n  return createToken(TokenTypes.CharacterSet, raw, {\n    kind: {\n      'd': TokenCharacterSetKinds.digit,\n      'h': TokenCharacterSetKinds.hex, // Not available in JS\n      's': TokenCharacterSetKinds.space, // Different than JS\n      'w': TokenCharacterSetKinds.word,\n    }[lower],\n    negate: raw[1] !== lower,\n  });\n}\n\nfunction createTokenForUnicodeProperty(raw) {\n  const {p, neg, value} = /^\\\\(?<p>[pP])\\{(?<neg>\\^?)(?<value>[^}]+)/.exec(raw).groups;\n  const negate = (p === 'P' && !neg) || (p === 'p' && !!neg);\n  return createToken(TokenTypes.CharacterSet, raw, {\n    kind: TokenCharacterSetKinds.property,\n    negate,\n    value,\n  });\n}\n\nfunction getFlagPropsForToken(flags) {\n  // Don't include `false` for flags that aren't included\n  const obj = {};\n  if (flags.includes('i')) {\n    obj.ignoreCase = true;\n  }\n  if (flags.includes('m')) {\n    // Onig flag m is equivalent to JS flag s\n    obj.dotAll = true;\n  }\n  if (flags.includes('x')) {\n    obj.extended = true;\n  }\n  return Object.keys(obj).length ? obj : null;\n}\n\n// - Unenclosed `\\xNN` above 0x7F is handled elsewhere as a UTF-8 encoded byte sequence\n// - Enclosed `\\x{}` with value above 0x10FFFF is allowed here; handled in the parser\nfunction getValidatedHexCharCode(raw) {\n  // Note: Onig (tested 6.9.8) has a bug where bare `\\u` and `\\x` are identity escapes if they\n  // appear at the very end of the pattern, so e.g. `\\u` matches `u`, but `\\u0`, `\\u.`, and `[\\u]`\n  // are all errors, and `\\x.` and `[\\x]` aren't errors but instead the `\\x` is equivalent to `\\0`.\n  // Don't emulate these bugs (see #21), and just treat these cases as errors. Also, Onig treats\n  // incomplete `\\x{` (with the brace and not immediately followed by a hex digit) as an identity\n  // escape, so e.g. `\\x{` matches `x{` and `^\\x{,2}$` matches `xx`, but `\\x{2,}` and `\\x{0,2}` are\n  // errors. Don't emulate this pointless ambiguity; just treat incomplete `\\x{` as an error\n  if (/^(?:\\\\u(?!\\p{AHex}{4})|\\\\x(?!\\p{AHex}{1,2}|\\{\\p{AHex}{1,8}\\}))/u.test(raw)) {\n    throw new Error(`Incomplete or invalid escape \"${raw}\"`);\n  }\n  // Might include leading 0s\n  const hex = raw[2] === '{' ?\n    /^\\\\x\\{\\s*(?<hex>\\p{AHex}+)/u.exec(raw).groups.hex :\n    raw.slice(2);\n  const dec = parseInt(hex, 16);\n  return dec;\n}\n\n// Value is 1-3 digits, which can be a backref (possibly invalid), null, octal, or identity escape,\n// possibly followed by 1-2 literal digits\nfunction splitEscapedNumToken(token, numCaptures) {\n  const {raw, inCharClass} = token;\n  // Keep any leading 0s since they indicate octal\n  const value = raw.slice(1);\n  // Backref (possibly invalid)\n  if (\n    !inCharClass &&\n    ( // Single digit 1-9 outside a char class is always treated as a backref\n      (value !== '0' && value.length === 1) ||\n      // Leading 0 makes it octal; backrefs can't include following literal digits\n      (value[0] !== '0' && +value <= numCaptures)\n    )\n  ) {\n    return [createToken(TokenTypes.Backreference, raw)];\n  }\n  const tokens = [];\n  // Returns 1-3 matches; the first (only) might be octal\n  const matches = value.match(/^[0-7]+|\\d/g);\n  for (let i = 0; i < matches.length; i++) {\n    const m = matches[i];\n    let value;\n    // Octal digits are 0-7\n    if (i === 0 && m !== '8' && m !== '9') {\n      value = parseInt(m, 8);\n      if (value > 0o177) {\n        // Octal UTF-8 encoded byte sequence; not yet supported\n        throw new Error(r`Octal encoded byte above 177 unsupported \"${raw}\"`);\n      }\n    } else {\n      value = m.codePointAt(0);\n    }\n    tokens.push(createToken(TokenTypes.Character, (i === 0 ? '\\\\' : '') + m, {\n      value,\n    }));\n  }\n  return tokens;\n}\n\nfunction assertSingleCodePoint(raw) {\n  if ([...raw].length !== 1) {\n    throw new Error(`Expected \"${raw}\" to be a single code point`);\n  }\n}\n\nexport {\n  tokenize,\n  TokenCharacterSetKinds,\n  TokenDirectiveKinds,\n  TokenGroupKinds,\n  TokenTypes,\n};\n", "import {AstAssertionKinds, AstTypes} from './parse.js';\n\nfunction hasOnlyChild({alternatives}, kidFn) {\n  return (\n    alternatives.length === 1 &&\n    alternatives[0].elements.length === 1 &&\n    (!kidFn || kidFn(alternatives[0].elements[0]))\n  );\n}\n\nfunction isAlwaysZeroLength({type}) {\n  return type === AstTypes.Assertion || type === AstTypes.Directive;\n}\n\nfunction isAlwaysNonZeroLength(node) {\n  const types = [\n    AstTypes.Character,\n    AstTypes.CharacterClass,\n    AstTypes.CharacterSet,\n  ];\n  return types.includes(node.type) || (\n    node.type === AstTypes.Quantifier &&\n    node.min &&\n    types.includes(node.element.type)\n  );\n}\n\n// Consumptive groups add to the match.\n// - Includes: Capturing, named capturing, noncapturing, atomic, and flag groups.\n// - Excludes: Lookarounds.\n//   - Special case: Absent functions are consumptive (and negated, quantified) but are different\n//     in other ways so are excluded here.\n// See also `AstTypeAliases.AnyGroup`.\nfunction isConsumptiveGroup({type}) {\n  return type === AstTypes.CapturingGroup || type === AstTypes.Group;\n}\n\nfunction isLookaround({type, kind}) {\n  return (\n    type === AstTypes.Assertion &&\n    (kind === AstAssertionKinds.lookahead || kind === AstAssertionKinds.lookbehind)\n  );\n}\n\nexport {\n  hasOnlyChild,\n  isAlwaysNonZeroLength,\n  isAlwaysZeroLength,\n  isConsumptiveGroup,\n  isLookaround,\n};\n", "import {AstTypes} from './parse.js';\nimport {throwIfNot} from './utils.js';\nimport {isConsumptiveGroup, isLookaround} from './utils-ast.js';\n\nfunction traverse(path, state, visitor) {\n  let ast = path.node;\n  while (ast.parent) {\n    ast = ast.parent;\n  }\n  function traverseArray(array, parent) {\n    for (let i = 0; i < array.length; i++) {\n      const keyShift = traverseNode(array[i], parent, i, array);\n      i = Math.max(-1, i + keyShift);\n    }\n  }\n  function traverseNode(node, parent = null, key = null, container = null) {\n    let keyShift = 0;\n    let skipTraversingKidsOfPath = false;\n    const path = {\n      node,\n      parent,\n      key,\n      container,\n      ast,\n      remove() {\n        throwIfNot(container, 'Container expected').splice(Math.max(0, key + keyShift), 1);\n        keyShift -= 1;\n      },\n      removeAllNextSiblings() {\n        return throwIfNot(container, 'Container expected').splice(key + 1);\n      },\n      removeAllPrevSiblings() {\n        const shifted = key + keyShift;\n        keyShift -= shifted;\n        return throwIfNot(container, 'Container expected').splice(0, Math.max(0, shifted));\n      },\n      replaceWith(newNode) {\n        setParent(newNode, parent);\n        if (container) {\n          container[Math.max(0, key + keyShift)] = newNode;\n        } else {\n          parent[key] = newNode;\n        }\n      },\n      skip() {\n        skipTraversingKidsOfPath = true;\n      },\n    };\n    const visitorKey = getAstTypeAliases(node).find(key => !!visitor[key]);\n    const methods = visitorKey && visitor[visitorKey];\n    const enterFn = typeof methods === 'function' ? methods : methods?.enter;\n    const exitFn = methods?.exit;\n    enterFn?.(path, state);\n    if (!skipTraversingKidsOfPath) {\n      switch (node.type) {\n        case AstTypes.Regex:\n          traverseNode(node.pattern, node, 'pattern');\n          traverseNode(node.flags, node, 'flags');\n          break;\n        case AstTypes.Alternative:\n        case AstTypes.CharacterClass:\n          traverseArray(node.elements, node);\n          break;\n        case AstTypes.Assertion:\n          if (isLookaround(node)) {\n            traverseArray(node.alternatives, node);\n          }\n          break;\n        case AstTypes.Backreference:\n        case AstTypes.Character:\n        case AstTypes.CharacterSet:\n        case AstTypes.Directive:\n        case AstTypes.Flags:\n        case AstTypes.Recursion:\n        case AstTypes.Subroutine:\n        case AstTypes.VariableLengthCharacterSet:\n          break;\n        case AstTypes.AbsentFunction:\n        case AstTypes.CapturingGroup:\n        case AstTypes.Group:\n        case AstTypes.Pattern:\n          traverseArray(node.alternatives, node);\n          break;\n        case AstTypes.CharacterClassIntersection:\n          traverseArray(node.classes, node);\n          break;\n        case AstTypes.CharacterClassRange:\n          traverseNode(node.min, node, 'min');\n          traverseNode(node.max, node, 'max');\n          break;\n        case AstTypes.Quantifier:\n          traverseNode(node.element, node, 'element');\n          break;\n        default:\n          throw new Error(`Unexpected node type \"${node.type}\"`);\n      }\n    }\n    exitFn?.(path, state);\n    return keyShift;\n  }\n  traverseNode(path.node, path.parent, path.key, path.container);\n}\n\nconst AstTypeAliases = {\n  AnyGroup: 'AnyGroup',\n  AnyNode: 'AnyNode',\n};\n\nfunction getAstTypeAliases(node) {\n  const types = [AstTypeAliases.AnyNode];\n  if (isConsumptiveGroup(node) || isLookaround(node)) {\n    types.push(AstTypeAliases.AnyGroup);\n  }\n  types.push(node.type);\n  return types;\n}\n\nfunction setParent(node, parent) {\n  // The traverser can work with ASTs whose nodes include or don't include `parent` props, so only\n  // update the parent if a prop for it exists\n  if ('parent' in parent) {\n    node.parent = parent;\n  }\n}\n\nexport {\n  traverse,\n};\n", "import {TokenCharacterSetKinds, TokenDirectiveKinds, TokenGroupKinds, TokenTypes} from './tokenize.js';\nimport {traverse} from './traverse.js';\nimport {JsUnicodePropertiesMap, JsUnicodePropertiesOfStringsMap, PosixProperties, slug} from './unicode.js';\nimport {getOrInsert, r, throwIfNot} from './utils.js';\nimport {hasOnlyChild} from './utils-ast.js';\n\nconst AstTypes = {\n  AbsentFunction: 'AbsentFunction',\n  Alternative: 'Alternative',\n  Assertion: 'Assertion',\n  Backreference: 'Backreference',\n  CapturingGroup: 'CapturingGroup',\n  Character: 'Character',\n  CharacterClass: 'CharacterClass',\n  CharacterClassIntersection: 'CharacterClassIntersection',\n  CharacterClassRange: 'CharacterClassRange',\n  CharacterSet: 'CharacterSet',\n  Directive: 'Directive',\n  Flags: 'Flags',\n  Group: 'Group',\n  Pattern: 'Pattern',\n  Quantifier: 'Quantifier',\n  Regex: 'Regex',\n  Subroutine: 'Subroutine',\n  VariableLengthCharacterSet: 'VariableLengthCharacterSet',\n  // Used only by the transformer for Regex+ ASTs\n  Recursion: 'Recursion',\n};\n\nconst AstAbsentFunctionKinds = {\n  // See <github.com/slevithan/oniguruma-to-es/issues/13>\n  repeater: 'repeater',\n};\n\nconst AstAssertionKinds = {\n  line_end: 'line_end',\n  line_start: 'line_start',\n  lookahead: 'lookahead',\n  lookbehind: 'lookbehind',\n  search_start: 'search_start',\n  string_end: 'string_end',\n  string_end_newline: 'string_end_newline',\n  string_start: 'string_start',\n  word_boundary: 'word_boundary',\n};\n\n// Identical values\nconst AstCharacterSetKinds = TokenCharacterSetKinds;\nconst AstDirectiveKinds = TokenDirectiveKinds;\n\nconst AstVariableLengthCharacterSetKinds = {\n  grapheme: 'grapheme',\n  newline: 'newline',\n};\n\n/**\n@typedef {{\n  type: 'Regex';\n  parent: null;\n  pattern: Object;\n  flags: Object;\n}} OnigurumaAst\n*/\n/**\n@param {import('./tokenize.js').TokenizerResult} tokenizerResult\n@param {{\n  skipBackrefValidation?: boolean;\n  skipPropertyNameValidation?: boolean;\n  verbose?: boolean;\n}} [options]\n@returns {OnigurumaAst}\n*/\nfunction parse({tokens, flags, rules}, options) {\n  const opts = {\n    skipBackrefValidation: false,\n    skipLookbehindValidation: false,\n    skipPropertyNameValidation: false,\n    verbose: false,\n    ...options,\n  };\n  const context = {\n    capturingGroups: [],\n    current: 0,\n    hasNumberedRef: false,\n    namedGroupsByName: new Map(),\n    parent: null,\n    skipBackrefValidation: opts.skipBackrefValidation,\n    skipLookbehindValidation: opts.skipLookbehindValidation,\n    skipPropertyNameValidation: opts.skipPropertyNameValidation,\n    subroutines: [],\n    token: null,\n    tokens,\n    verbose: opts.verbose,\n    walk,\n  };\n  function walk(parent, state) {\n    const token = tokens[context.current];\n    context.parent = parent;\n    context.token = token;\n    // Advance for the next iteration\n    context.current++;\n    switch (token.type) {\n      case TokenTypes.Alternator:\n        // Top-level only; groups handle their own alternators\n        return createAlternative();\n      case TokenTypes.Assertion:\n        return createAssertionFromToken(token);\n      case TokenTypes.Backreference:\n        return parseBackreference(context);\n      case TokenTypes.Character:\n        return createCharacter(token.value, {useLastValid: !!state.isCheckingRangeEnd});\n      case TokenTypes.CharacterClassHyphen:\n        return parseCharacterClassHyphen(context, state);\n      case TokenTypes.CharacterClassOpen:\n        return parseCharacterClassOpen(context, state);\n      case TokenTypes.CharacterSet:\n        return parseCharacterSet(context);\n      case TokenTypes.Directive:\n        return createDirectiveFromToken(token);\n      case TokenTypes.GroupOpen:\n        return parseGroupOpen(context, state);\n      case TokenTypes.Quantifier:\n        return parseQuantifier(context);\n      case TokenTypes.Subroutine:\n        return parseSubroutine(context);\n      case TokenTypes.VariableLengthCharacterSet:\n        return createVariableLengthCharacterSet(token.kind);\n      default:\n        throw new Error(`Unexpected token type \"${token.type}\"`);\n    }\n  }\n  const ast = createRegex(createPattern(), createFlags(flags));\n  let top = ast.pattern.alternatives[0];\n  while (context.current < tokens.length) {\n    const node = walk(top, {});\n    if (node.type === AstTypes.Alternative) {\n      ast.pattern.alternatives.push(node);\n      top = node;\n    } else {\n      top.elements.push(node);\n    }\n  }\n  // `context` updated by preceding `walk` loop\n  const {capturingGroups, hasNumberedRef, namedGroupsByName, subroutines} = context;\n  // Validation that requires knowledge about the complete pattern\n  if (hasNumberedRef && namedGroupsByName.size && !rules.captureGroup) {\n    throw new Error('Numbered backref/subroutine not allowed when using named capture');\n  }\n  for (const {ref} of subroutines) {\n    if (typeof ref === 'number') {\n      // Relative nums are already resolved\n      if (ref > capturingGroups.length) {\n        throw new Error(`Subroutine uses a group number that's not defined`);\n      }\n    } else if (!namedGroupsByName.has(ref)) {\n      throw new Error(r`Subroutine uses a group name that's not defined \"\\g<${ref}>\"`);\n    } else if (namedGroupsByName.get(ref).length > 1) {\n      throw new Error(r`Subroutine uses a duplicate group name \"\\g<${ref}>\"`);\n    }\n  }\n  // Add `parent` properties now that we have a final AST\n  traverse({node: ast}, null, {\n    AnyNode({node, parent}) {\n      node.parent = parent;\n    },\n  });\n  return ast;\n}\n\n// Supported (if the backref appears to the right of the reffed capture's opening paren):\n// - `\\k<name>`, `\\k'name'`\n// - When named capture not used:\n//   - `\\n`, `\\nn`, `\\nnn`\n//   - `\\k<n>`, `\\k'n'\n//   - `\\k<-n>`, `\\k'-n'`\n// Unsupported:\n// - `\\k<+n>`, `\\k'+n'` - Note that, Unlike Oniguruma, Onigmo doesn't support this as special\n//   syntax and therefore considers it a valid group name.\n// - Backref with recursion level (with num or name): `\\k<n+level>`, `\\k<n-level>`, etc.\n//   (Onigmo also supports `\\k<-n+level>`, `\\k<-n-level>`, etc.)\n// Backrefs in Onig use multiplexing for duplicate group names (the rules can be complicated when\n// overlapping with subroutines), but a `Backreference`'s simple `ref` prop doesn't capture these\n// details so multiplexed ref pointers need to be derived when working with the AST\nfunction parseBackreference(context) {\n  const {raw} = context.token;\n  const hasKWrapper = /^\\\\k[<']/.test(raw);\n  const ref = hasKWrapper ? raw.slice(3, -1) : raw.slice(1);\n  const fromNum = (num, isRelative = false) => {\n    const numCapturesToLeft = context.capturingGroups.length;\n    let orphan = false;\n    // Note: It's not an error for numbered backrefs to come before their referenced group in Onig,\n    // but an error is the best path for this library because:\n    // 1. Most placements are mistakes and can never match (based on the Onig behavior for backrefs\n    //    to nonparticipating groups).\n    // 2. Erroring matches the behavior of named backrefs.\n    // 3. The edge cases where they're matchable rely on rules for backref resetting within\n    //    quantified groups that are different in JS and aren't emulatable. Note that it's not a\n    //    backref in the first place if using `\\10` or higher and not as many capturing groups are\n    //    defined to the left (it's an octal or identity escape).\n    // [TODO] Ideally this would be refactored to include the backref in the AST when it's not an\n    // error in Onig (due to the reffed group being defined to the right), and the error handling\n    // would move to the transformer\n    if (num > numCapturesToLeft) {\n      // [WARNING] Skipping the error breaks assumptions and might create edge case issues, since\n      // backrefs are required to come after their captures; unfortunately this option is needed\n      // for TextMate grammars\n      if (context.skipBackrefValidation) {\n        orphan = true;\n      } else {\n        throw new Error(`Not enough capturing groups defined to the left \"${raw}\"`);\n      }\n    }\n    context.hasNumberedRef = true;\n    return createBackreference(isRelative ? numCapturesToLeft + 1 - num : num, {orphan});\n  };\n  if (hasKWrapper) {\n    const numberedRef = /^(?<sign>-?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n    if (numberedRef) {\n      return fromNum(+numberedRef.groups.num, !!numberedRef.groups.sign);\n    }\n    // Invalid in a backref name even when valid in a group name\n    if (/[-+]/.test(ref)) {\n      throw new Error(`Invalid backref name \"${raw}\"`);\n    }\n    if (!context.namedGroupsByName.has(ref)) {\n      throw new Error(`Group name not defined to the left \"${raw}\"`);\n    }\n    return createBackreference(ref);\n  }\n  return fromNum(+ref);\n}\n\nfunction parseCharacterClassHyphen(context, state) {\n  const {parent, tokens, walk} = context;\n  const prevSiblingNode = parent.elements.at(-1);\n  const nextToken = tokens[context.current];\n  if (\n    !state.isCheckingRangeEnd &&\n    prevSiblingNode &&\n    prevSiblingNode.type !== AstTypes.CharacterClass &&\n    prevSiblingNode.type !== AstTypes.CharacterClassRange &&\n    nextToken &&\n    nextToken.type !== TokenTypes.CharacterClassOpen &&\n    nextToken.type !== TokenTypes.CharacterClassClose &&\n    nextToken.type !== TokenTypes.CharacterClassIntersector\n  ) {\n    const nextNode = walk(parent, {\n      ...state,\n      isCheckingRangeEnd: true,\n    });\n    if (prevSiblingNode.type === AstTypes.Character && nextNode.type === AstTypes.Character) {\n      parent.elements.pop();\n      return createCharacterClassRange(prevSiblingNode, nextNode);\n    }\n    throw new Error('Invalid character class range');\n  }\n  // Literal hyphen\n  return createCharacter(45);\n}\n\nfunction parseCharacterClassOpen(context, state) {\n  const {token, tokens, verbose, walk} = context;\n  const firstClassToken = tokens[context.current];\n  let node = createCharacterClass({negate: token.negate});\n  const intersection = node.elements[0];\n  let nextToken = throwIfUnclosedCharacterClass(firstClassToken);\n  while (nextToken.type !== TokenTypes.CharacterClassClose) {\n    if (nextToken.type === TokenTypes.CharacterClassIntersector) {\n      intersection.classes.push(createCharacterClass({negate: false, baseOnly: true}));\n      // Skip the intersector\n      context.current++;\n    } else {\n      const cc = intersection.classes.at(-1);\n      cc.elements.push(walk(cc, state));\n    }\n    nextToken = throwIfUnclosedCharacterClass(tokens[context.current], firstClassToken);\n  }\n  if (!verbose) {\n    optimizeCharacterClassIntersection(intersection);\n  }\n  // Simplify tree if we don't need the intersection wrapper\n  if (intersection.classes.length === 1) {\n    const cc = intersection.classes[0];\n    // Only needed if `!verbose`; otherwise an intersection's direct kids are never negated\n    cc.negate = node.negate !== cc.negate;\n    node = cc;\n  }\n  // Skip the closing square bracket\n  context.current++;\n  return node;\n}\n\nfunction parseCharacterSet({token, skipPropertyNameValidation}) {\n  let {kind, negate, value} = token;\n  if (kind === TokenCharacterSetKinds.property) {\n    const normalized = slug(value);\n    if (PosixProperties.has(normalized)) {\n      kind = TokenCharacterSetKinds.posix;\n      value = normalized;\n    } else {\n      return createUnicodeProperty(value, {\n        negate,\n        skipPropertyNameValidation,\n      });\n    }\n  }\n  if (kind === TokenCharacterSetKinds.posix) {\n    return {\n      type: AstTypes.CharacterSet,\n      kind: AstCharacterSetKinds.posix,\n      negate,\n      value,\n    };\n  }\n  return createCharacterSet(kind, {negate});\n}\n\nfunction parseGroupOpen(context, state) {\n  const {token, tokens, capturingGroups, namedGroupsByName, skipLookbehindValidation, verbose, walk} = context;\n  let node = createByGroupKind(token);\n  const isAbsentFunction = node.type === AstTypes.AbsentFunction;\n  const isLookbehind = node.kind === AstAssertionKinds.lookbehind;\n  const isNegLookbehind = isLookbehind && node.negate;\n  // Track capturing group details for backrefs and subroutines (before parsing the group's\n  // contents so nested groups with the same name are tracked in order)\n  if (node.type === AstTypes.CapturingGroup) {\n    capturingGroups.push(node);\n    if (node.name) {\n      getOrInsert(namedGroupsByName, node.name, []).push(node);\n    }\n  }\n  // Don't allow nested absent functions\n  if (isAbsentFunction && state.isInAbsentFunction) {\n    // Is officially unsupported in Onig but doesn't throw, gives strange results\n    throw new Error('Nested absent function not supported by Oniguruma');\n  }\n  let nextToken = throwIfUnclosedGroup(tokens[context.current]);\n  while (nextToken.type !== TokenTypes.GroupClose) {\n    if (nextToken.type === TokenTypes.Alternator) {\n      node.alternatives.push(createAlternative());\n      // Skip the alternator\n      context.current++;\n    } else {\n      const alt = node.alternatives.at(-1);\n      const child = walk(alt, {\n        ...state,\n        isInAbsentFunction: state.isInAbsentFunction || isAbsentFunction,\n        isInLookbehind: state.isInLookbehind || isLookbehind,\n        isInNegLookbehind: state.isInNegLookbehind || isNegLookbehind,\n      });\n      alt.elements.push(child);\n      // Centralized validation of lookbehind contents\n      if ((isLookbehind || state.isInLookbehind) && !skipLookbehindValidation) {\n        // JS supports all features within lookbehind, but Onig doesn't. Absent functions of form\n        // `(?~|)` and `(?~|…)` are also invalid in lookbehind (the `(?~…)` and `(?~|…|…)` forms\n        // are allowed), but all forms with `(?~|` throw since they aren't yet supported\n        const msg = 'Lookbehind includes a pattern not allowed by Oniguruma';\n        if (isNegLookbehind || state.isInNegLookbehind) {\n          // - Invalid: `(?=…)`, `(?!…)`, capturing groups\n          // - Valid: `(?<=…)`, `(?<!…)`\n          if (child.kind === AstAssertionKinds.lookahead || child.type === AstTypes.CapturingGroup) {\n            throw new Error(msg);\n          }\n        } else {\n          // - Invalid: `(?=…)`, `(?!…)`, `(?<!…)`\n          // - Valid: `(?<=…)`, capturing groups\n          if (child.kind === AstAssertionKinds.lookahead || (child.kind === AstAssertionKinds.lookbehind && child.negate)) {\n            throw new Error(msg);\n          }\n        }\n      }\n    }\n    nextToken = throwIfUnclosedGroup(tokens[context.current]);\n  }\n  if (!verbose) {\n    node = getOptimizedGroup(node);\n  }\n  // Skip the closing parenthesis\n  context.current++;\n  return node;\n}\n\nfunction parseQuantifier({token, parent}) {\n  const {min, max, greedy, possessive} = token;\n  const quantifiedNode = parent.elements.at(-1);\n  if (\n    !quantifiedNode ||\n    quantifiedNode.type === AstTypes.Assertion ||\n    quantifiedNode.type === AstTypes.Directive\n  ) {\n    throw new Error(`Quantifier requires a repeatable token`);\n  }\n  const node = createQuantifier(quantifiedNode, min, max, greedy, possessive);\n  parent.elements.pop();\n  return node;\n}\n\n// Onig subroutine behavior:\n// - Subroutines can appear before the groups they reference; ex: `\\g<1>(a)` is valid.\n// - Multiple subroutines can reference the same group.\n// - Subroutines can reference groups that themselves contain subroutines, followed to any depth.\n// - Subroutines can be used recursively, and `\\g<0>` recursively references the whole pattern.\n// - Subroutines can use relative references (backward or forward); ex: `\\g<+1>(.)\\g<-1>`.\n// - Subroutines don't get their own capturing group numbers; ex: `(.)\\g<1>\\2` is invalid.\n// - Subroutines use the flags that apply to their referenced group, so e.g.\n//   `(?-i)(?<a>a)(?i)\\g<a>` is fully case sensitive.\n// - Differences from PCRE/Perl/Regex+ subroutines:\n//   - Subroutines can't reference duplicate group names (though duplicate names are valid if no\n//     subroutines reference them).\n//   - Subroutines can't use absolute or relative numbers if named capture is used anywhere.\n//   - Named backrefs must be to the right of their group definition, so the backref in\n//     `\\g<a>\\k<a>(?<a>)` is invalid (not directly related to subroutines).\n//   - Subroutines don't restore capturing group match values (for backrefs) upon exit, so e.g.\n//     `(?<a>(?<b>[ab]))\\g<a>\\k<b>` matches `abb` but not `aba`; same for numbered.\n// The interaction of backref multiplexing (an Onig-specific feature) and subroutines is complex:\n// - Only the most recent value matched by a capturing group and its subroutines is considered for\n//   backref multiplexing, and this also applies to capturing groups nested within a group that's\n//   referenced by a subroutine.\n// - Although a subroutine can't reference a group with a duplicate name, it can reference a group\n//   with a nested capture whose name is duplicated (e.g. outside of the referenced group).\n//   - These duplicate names can then multiplex; but only the most recent value matched from within\n//     the outer group (or the subroutines that reference it) is available for multiplexing.\n//   - Ex: With `(?<a>(?<b>[123]))\\g<a>\\g<a>(?<b>0)\\k<b>`, the backref `\\k<b>` can only match `0`\n//     or whatever was matched by the most recently matched subroutine. If you took out `(?<b>0)`,\n//     no multiplexing would occur.\nfunction parseSubroutine(context) {\n  const {token, capturingGroups, subroutines} = context;\n  let ref = token.raw.slice(3, -1);\n  const numberedRef = /^(?<sign>[-+]?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n  if (numberedRef) {\n    const num = +numberedRef.groups.num;\n    const numCapturesToLeft = capturingGroups.length;\n    context.hasNumberedRef = true;\n    ref = {\n      '': num,\n      '+': numCapturesToLeft + num,\n      '-': numCapturesToLeft + 1 - num,\n    }[numberedRef.groups.sign];\n    if (ref < 1) {\n      throw new Error('Invalid subroutine number');\n    }\n  // Special case for full-pattern recursion; can't be `+0`, `-0`, `00`, etc.\n  } else if (ref === '0') {\n    ref = 0;\n  }\n  const node = createSubroutine(ref);\n  subroutines.push(node);\n  return node;\n}\n\nfunction createAbsentFunction(kind) {\n  if (kind !== AstAbsentFunctionKinds.repeater) {\n    throw new Error(`Unexpected absent function kind \"${kind}\"`);\n  }\n  return {\n    type: AstTypes.AbsentFunction,\n    kind,\n    alternatives: [createAlternative()],\n  };\n}\n\nfunction createAlternative() {\n  return {\n    type: AstTypes.Alternative,\n    elements: [],\n  };\n}\n\nfunction createAssertion(kind, options) {\n  // Use `createLookaround` for lookahead and lookbehind assertions\n  const negate = !!options?.negate;\n  return {\n    type: AstTypes.Assertion,\n    kind,\n    ...(kind === AstAssertionKinds.word_boundary && {negate}),\n  };\n}\n\nfunction createAssertionFromToken({kind}) {\n  return createAssertion(\n    throwIfNot({\n      '^': AstAssertionKinds.line_start,\n      '$': AstAssertionKinds.line_end,\n      '\\\\A': AstAssertionKinds.string_start,\n      '\\\\b': AstAssertionKinds.word_boundary,\n      '\\\\B': AstAssertionKinds.word_boundary,\n      '\\\\G': AstAssertionKinds.search_start,\n      '\\\\z': AstAssertionKinds.string_end,\n      '\\\\Z': AstAssertionKinds.string_end_newline,\n    }[kind], `Unexpected assertion kind \"${kind}\"`),\n    {negate: kind === r`\\B`}\n  );\n}\n\nfunction createBackreference(ref, options) {\n  const orphan = !!options?.orphan;\n  return {\n    type: AstTypes.Backreference,\n    ...(orphan && {orphan}),\n    ref,\n  };\n}\n\nfunction createByGroupKind({flags, kind, name, negate, number}) {\n  switch (kind) {\n    case TokenGroupKinds.absent_repeater:\n      return createAbsentFunction(AstAbsentFunctionKinds.repeater);\n    case TokenGroupKinds.atomic:\n      return createGroup({atomic: true});\n    case TokenGroupKinds.capturing:\n      return createCapturingGroup(number, name);\n    case TokenGroupKinds.group:\n      return createGroup({flags});\n    case TokenGroupKinds.lookahead:\n    case TokenGroupKinds.lookbehind:\n      return createLookaround({\n        behind: kind === TokenGroupKinds.lookbehind,\n        negate,\n      });\n    default:\n      throw new Error(`Unexpected group kind \"${kind}\"`);\n  }\n}\n\nfunction createCapturingGroup(number, name) {\n  const hasName = name !== undefined;\n  if (hasName && !isValidGroupNameOniguruma(name)) {\n    throw new Error(`Group name \"${name}\" invalid in Oniguruma`);\n  }\n  return {\n    type: AstTypes.CapturingGroup,\n    number,\n    ...(hasName && {name}),\n    alternatives: [createAlternative()],\n  };\n}\n\nfunction createCharacter(charCode, options) {\n  const opts = {\n    useLastValid: false,\n    ...options,\n  };\n  if (charCode > 0x10FFFF) {\n    const hex = charCode.toString(16);\n    if (opts.useLastValid) {\n      charCode = 0x10FFFF;\n    } else if (charCode > 0x13FFFF) {\n      throw new Error(`Invalid code point out of range \"\\\\x{${hex}}\"`);\n    } else {\n      throw new Error(`Invalid code point out of range in JS \"\\\\x{${hex}}\"`);\n    }\n  }\n  return {\n    type: AstTypes.Character,\n    value: charCode,\n  };\n}\n\nfunction createCharacterClass(options) {\n  const opts = {\n    baseOnly: false,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: AstTypes.CharacterClass,\n    negate: opts.negate,\n    elements: opts.baseOnly ? [] : [createCharacterClassIntersection()],\n  };\n}\n\nfunction createCharacterClassIntersection() {\n  return {\n    type: AstTypes.CharacterClassIntersection,\n    classes: [createCharacterClass({negate: false, baseOnly: true})],\n  };\n}\n\nfunction createCharacterClassRange(min, max) {\n  if (max.value < min.value) {\n    throw new Error('Character class range out of order');\n  }\n  return {\n    type: AstTypes.CharacterClassRange,\n    min,\n    max,\n  };\n}\n\nfunction createCharacterSet(kind, {negate}) {\n  const node = {\n    type: AstTypes.CharacterSet,\n    kind: throwIfNot(AstCharacterSetKinds[kind], `Unexpected character set kind \"${kind}\"`),\n  };\n  if (\n    kind === TokenCharacterSetKinds.digit ||\n    kind === TokenCharacterSetKinds.hex ||\n    kind === TokenCharacterSetKinds.space ||\n    kind === TokenCharacterSetKinds.word\n  ) {\n    node.negate = negate;\n  }\n  return node;\n}\n\nfunction createDirectiveFromToken({kind, flags}) {\n  const node = {\n    type: AstTypes.Directive,\n    kind: throwIfNot(AstDirectiveKinds[kind], `Unexpected directive kind \"${kind}\"`),\n  };\n  // Can't optimize by simply creating a `Group` with a `flags` prop and wrapping the remainder of\n  // the open group or pattern in it, because the flag modifier's effect might extend across\n  // alternation. Ex: `a(?i)b|c` is equivalent to `a(?i:b)|(?i:c)`, not `a(?i:b|c)`\n  if (kind === TokenDirectiveKinds.flags) {\n    node.flags = flags;\n  }\n  return node;\n}\n\nfunction createFlags({ignoreCase, dotAll, extended, digitIsAscii, spaceIsAscii, wordIsAscii}) {\n  return {\n    type: AstTypes.Flags,\n    ignoreCase,\n    dotAll,\n    extended,\n    digitIsAscii,\n    spaceIsAscii,\n    wordIsAscii,\n  };\n}\n\nfunction createGroup(options) {\n  const atomic = options?.atomic;\n  const flags = options?.flags;\n  return {\n    type: AstTypes.Group,\n    ...(atomic && {atomic}),\n    ...(flags && {flags}),\n    alternatives: [createAlternative()],\n  };\n}\n\nfunction createLookaround(options) {\n  const opts = {\n    behind: false,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: AstTypes.Assertion,\n    kind: opts.behind ? AstAssertionKinds.lookbehind : AstAssertionKinds.lookahead,\n    negate: opts.negate,\n    alternatives: [createAlternative()],\n  };\n}\n\nfunction createPattern() {\n  return {\n    type: AstTypes.Pattern,\n    alternatives: [createAlternative()],\n  };\n}\n\nfunction createQuantifier(element, min, max, greedy = true, possessive = false) {\n  const node = {\n    type: AstTypes.Quantifier,\n    min,\n    max,\n    greedy,\n    possessive,\n    element,\n  };\n  if (max < min) {\n    return {\n      ...node,\n      min: max,\n      max: min,\n      possessive: true,\n    };\n  }\n  return node;\n}\n\nfunction createRegex(pattern, flags) {\n  return {\n    type: AstTypes.Regex,\n    pattern,\n    flags,\n  };\n}\n\nfunction createSubroutine(ref) {\n  return {\n    type: AstTypes.Subroutine,\n    ref,\n  };\n}\n\nfunction createUnicodeProperty(value, options) {\n  const opts = {\n    negate: false,\n    skipPropertyNameValidation: false,\n    ...options,\n  };\n  return {\n    type: AstTypes.CharacterSet,\n    kind: AstCharacterSetKinds.property,\n    value: opts.skipPropertyNameValidation ? value : getJsUnicodePropertyName(value),\n    negate: opts.negate,\n  }\n}\n\nfunction createVariableLengthCharacterSet(kind) {\n  return {\n    type: AstTypes.VariableLengthCharacterSet,\n    kind: throwIfNot({\n      '\\\\R': AstVariableLengthCharacterSetKinds.newline,\n      '\\\\X': AstVariableLengthCharacterSetKinds.grapheme,\n    }[kind], `Unexpected varcharset kind \"${kind}\"`),\n  };\n}\n\n// Unlike Onig, JS Unicode property names are case sensitive, don't ignore spaces, hyphens, and\n// underscores, and require underscores in specific positions\nfunction getJsUnicodePropertyName(value) {\n  const slugged = slug(value);\n  if (JsUnicodePropertiesOfStringsMap.has(slugged)) {\n    // Variable-length properties of strings aren't supported by Onig\n    throw new Error(r`Unicode property \"\\p{${value}}\" unsupported in Oniguruma`);\n  }\n  const jsName = JsUnicodePropertiesMap.get(slugged);\n  if (jsName) {\n    return jsName;\n  }\n  // Assume it's a script name (avoids including heavyweight data for long list of script names);\n  // JS requires formatting `Like_This`, so use best effort to reformat the name (covers a lot, but\n  // isn't able to map for all possible formatting differences)\n  return value.\n    trim().\n    replace(/[- _]+/g, '_').\n    replace(/[A-Z][a-z]+(?=[A-Z])/g, '$&_'). // `PropertyName` to `Property_Name`\n    replace(/[A-Za-z]+/g, m => m[0].toUpperCase() + m.slice(1).toLowerCase());\n}\n\n// If a direct child group is needlessly nested, return it instead (after modifying it)\nfunction getOptimizedGroup(node) {\n  const firstAltFirstEl = node.alternatives[0].elements[0];\n  if (\n    node.type === AstTypes.Group &&\n    hasOnlyChild(node, kid => kid.type === AstTypes.Group) &&\n    !(node.atomic && firstAltFirstEl.flags) &&\n    !(node.flags && (firstAltFirstEl.atomic || firstAltFirstEl.flags))\n  ) {\n    if (node.atomic) {\n      firstAltFirstEl.atomic = true;\n    } else if (node.flags) {\n      firstAltFirstEl.flags = node.flags;\n    }\n    return firstAltFirstEl;\n  }\n  return node;\n}\n\nfunction isValidGroupNameOniguruma(name) {\n  // The more restrictive rules for valid JS group names are captured in `isValidGroupNameJs`. Note\n  // that backrefs and subroutines might contextually use `-` and `+` to indicate relative index or\n  // recursion level\n  return /^[\\p{Alpha}\\p{Pc}][^)]*$/u.test(name);\n}\n\n// For any intersection classes that contain only a class, swap the parent with its (modded) child\nfunction optimizeCharacterClassIntersection(intersection) {\n  for (let i = 0; i < intersection.classes.length; i++) {\n    const cc = intersection.classes[i];\n    const firstChild = cc.elements[0];\n    if (cc.elements.length === 1 && firstChild.type === AstTypes.CharacterClass) {\n      intersection.classes[i] = firstChild;\n      firstChild.negate = cc.negate !== firstChild.negate;\n    }\n  }\n}\n\nfunction throwIfUnclosedCharacterClass(token, firstClassToken) {\n  return throwIfNot(\n    token,\n    // Easier to understand error when applicable\n    `${firstClassToken?.value === 93 ? 'Empty' : 'Unclosed'} character class`\n  );\n}\n\nfunction throwIfUnclosedGroup(token) {\n  return throwIfNot(token, 'Unclosed group');\n}\n\nexport {\n  AstAbsentFunctionKinds,\n  AstAssertionKinds,\n  AstCharacterSetKinds,\n  AstDirectiveKinds,\n  AstTypes,\n  AstVariableLengthCharacterSetKinds,\n  createAbsentFunction,\n  createAlternative,\n  createAssertion,\n  createBackreference,\n  createCapturingGroup,\n  createCharacter,\n  createCharacterClass,\n  createCharacterClassIntersection,\n  createCharacterClassRange,\n  createCharacterSet,\n  createFlags,\n  createGroup,\n  createLookaround,\n  createPattern,\n  createQuantifier,\n  createRegex,\n  createSubroutine,\n  createUnicodeProperty,\n  createVariableLengthCharacterSet,\n  parse,\n};\n", "import {Accuracy, Target} from './options.js';\nimport {AstAssertionKinds, AstCharacterSetKinds, AstDirectiveKinds, AstTypes, AstVariableLengthCharacterSetKinds, createAlternative, createAssertion, createBackreference, createCapturingGroup, createCharacterSet, createGroup, createLookaround, createQuantifier, createUnicodeProperty, parse} from './parse.js';\nimport {tokenize} from './tokenize.js';\nimport {traverse} from './traverse.js';\nimport {JsUnicodeProperties, PosixClassesMap} from './unicode.js';\nimport {cp, getNewCurrentFlags, getOrInsert, isMinTarget, r} from './utils.js';\nimport {hasOnlyChild, isAlwaysNonZeroLength, isAlwaysZeroLength, isConsumptiveGroup, isLookaround} from './utils-ast.js';\nimport emojiRegex from 'emoji-regex-xs';\n\n/**\n@typedef {{\n  type: 'Regex';\n  parent: null;\n  pattern: Object;\n  flags: Object;\n  options: Object;\n  _originMap: Map<Object, Object>;\n  _strategy: string?;\n}} RegexAst\n*/\n/**\nTransforms an Oniguruma AST in-place to a [Regex+](https://github.com/slevithan/regex) AST.\nAssumes target ES2025, expecting the generator to down-convert to the desired JS target version.\n\nRegex+'s syntax and behavior is a strict superset of native JavaScript, so the AST is very close\nto representing native ES2025 `RegExp` but with some added features (atomic groups, possessive\nquantifiers, recursion). The AST doesn't use some of Regex+'s extended features like flag x or\nsubroutines because they follow PCRE behavior and work somewhat differently than in Oniguruma. The\nAST represents what's needed to precisely reproduce Oniguruma behavior using Regex+.\n@param {import('./parse.js').OnigurumaAst} ast\n@param {{\n  accuracy?: keyof Accuracy;\n  asciiWordBoundaries?: boolean;\n  avoidSubclass?: boolean;\n  bestEffortTarget?: keyof Target;\n}} [options]\n@returns {RegexAst}\n*/\nfunction transform(ast, options) {\n  const opts = {\n    // A couple edge cases exist where options `accuracy` and `bestEffortTarget` are used:\n    // - `VariableLengthCharacterSet` kind `grapheme` (`\\X`): An exact representation would require\n    //   heavy Unicode data; a best-effort approximation requires knowing the target.\n    // - `CharacterSet` kind `posix` with values `graph` and `print`: Their complex Unicode-based\n    //   representations would be hard to change to ASCII-based after the fact in the generator\n    //   based on `target`/`accuracy`, so produce the appropriate structure here.\n    accuracy: 'default',\n    asciiWordBoundaries: false,\n    avoidSubclass: false,\n    bestEffortTarget: 'ES2025',\n    ...options,\n  };\n  const firstPassState = {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    flagDirectivesByAlt: new Map(),\n    jsGroupNameMap: new Map(),\n    minTargetEs2024: isMinTarget(opts.bestEffortTarget, 'ES2024'),\n    passedLookbehind: false,\n    strategy: null,\n    // Subroutines can appear before the groups they ref, so collect reffed nodes for a second pass \n    subroutineRefMap: new Map(),\n    supportedGNodes: new Set(),\n    digitIsAscii: ast.flags.digitIsAscii,\n    spaceIsAscii: ast.flags.spaceIsAscii,\n    wordIsAscii: ast.flags.wordIsAscii,\n  };\n  traverse({node: ast}, firstPassState, FirstPassVisitor);\n  // Global flags modified by the first pass\n  const globalFlags = {\n    dotAll: ast.flags.dotAll,\n    ignoreCase: ast.flags.ignoreCase,\n  };\n  // The interplay of subroutines (with Onig's unique rules/behavior for them; see comments in the\n  // parser for details) with backref multiplexing (a unique Onig feature), flag modifiers, and\n  // duplicate group names (which might be indirectly referenced by subroutines even though\n  // subroutines can't directly reference duplicate names) is extremely complicated to emulate in\n  // JS in a way that handles all edge cases, so we need multiple passes to do it\n  const secondPassState = {\n    currentFlags: globalFlags,\n    prevFlags: null,\n    globalFlags,\n    groupOriginByCopy: new Map(),\n    groupsByName: new Map(),\n    multiplexCapturesToLeftByRef: new Map(),\n    openRefs: new Map(),\n    reffedNodesByReferencer: new Map(),\n    subroutineRefMap: firstPassState.subroutineRefMap,\n  };\n  traverse({node: ast}, secondPassState, SecondPassVisitor);\n  const thirdPassState = {\n    groupsByName: secondPassState.groupsByName,\n    highestOrphanBackref: 0,\n    numCapturesToLeft: 0,\n    reffedNodesByReferencer: secondPassState.reffedNodesByReferencer,\n  };\n  traverse({node: ast}, thirdPassState, ThirdPassVisitor);\n  ast._originMap = secondPassState.groupOriginByCopy;\n  ast._strategy = firstPassState.strategy;\n  return ast;\n}\n\nconst FirstPassVisitor = {\n  AbsentFunction({node, replaceWith}) {\n    // Convert absent repeater `(?~…)` to `(?:(?:(?!…)\\p{Any})*)`\n    const group = prepContainer(createGroup(), [\n      adoptAndSwapKids(createLookaround({negate: true}), node.alternatives),\n      createUnicodeProperty('Any'),\n    ]);\n    const quantifier = createQuantifier(group, 0, Infinity);\n    group.parent = quantifier;\n    replaceWith(prepContainer(createGroup(), [quantifier]));\n  },\n\n  Alternative: {\n    enter({node, parent, key}, {flagDirectivesByAlt}) {\n      // Look for own-level flag directives when entering an alternative because after traversing\n      // the directive itself, any subsequent flag directives will no longer be at the same level\n      const flagDirectives = node.elements.filter(el => el.kind === AstDirectiveKinds.flags);\n      for (let i = key + 1; i < parent.alternatives.length; i++) {\n        const forwardSiblingAlt = parent.alternatives[i];\n        getOrInsert(flagDirectivesByAlt, forwardSiblingAlt, []).push(...flagDirectives);\n      }\n    },\n    exit({node}, {flagDirectivesByAlt}) {\n      // Wait until exiting to wrap an alternative's nodes with flag groups that extend flag\n      // directives from prior sibling alternatives because doing this at the end allows inner\n      // nodes to accurately check their level in the tree\n      if (flagDirectivesByAlt.get(node)?.length) {\n        const flags = getCombinedFlagModsFromFlagNodes(flagDirectivesByAlt.get(node));\n        if (flags) {\n          const flagGroup = prepContainer(createGroup({flags}), node.elements);\n          // Manually set the parent since we're not using `replaceWith`\n          flagGroup.parent = node;\n          node.elements = [flagGroup];\n        }\n      }\n    },\n  },\n\n  Assertion({node, key, container, ast, remove, replaceWith}, state) {\n    const {kind, negate} = node;\n    const {asciiWordBoundaries, avoidSubclass, supportedGNodes, wordIsAscii} = state;\n    if (kind === AstAssertionKinds.line_end) {\n      // Onig's only line break char is line feed, unlike JS\n      replaceWith(parseFragment(r`(?=\\z|\\n)`));\n    } else if (kind === AstAssertionKinds.line_start) {\n      // Onig's only line break char is line feed, unlike JS. Onig's `^` doesn't match after a\n      // string-terminating line feed\n      replaceWith(parseFragment(r`(?<=\\A|\\n(?!\\z))`, {skipLookbehindValidation: true}));\n    } else if (kind === AstAssertionKinds.lookbehind) {\n      state.passedLookbehind = true;\n    } else if (kind === AstAssertionKinds.search_start) {\n      if (supportedGNodes.has(node)) {\n        ast.flags.sticky = true;\n        remove();\n      } else {\n        const prev = container[key - 1]; // parent.elements[key - 1]\n        // Not all ways of blocking the `\\G` from matching are covered (ex: a node prior to the\n        // prev node could block), but blocked `\\G` is an edge case so it's okay if some blocked\n        // cases resulting in the standard error for being unsupported\n        if (prev && isAlwaysNonZeroLength(prev)) {\n          replaceWith(prepContainer(createLookaround({negate: true})));\n        } else if (avoidSubclass) {\n          throw new Error(r`Uses \"\\G\" in a way that requires a subclass`);\n        } else {\n          replaceWith(createAssertion(AstAssertionKinds.string_start));\n          state.strategy = 'clip_search';\n        }\n      }\n    } else if (kind === AstAssertionKinds.string_end_newline) {\n      replaceWith(parseFragment(r`(?=\\n?\\z)`));\n    } else if (kind === AstAssertionKinds.word_boundary && !wordIsAscii && !asciiWordBoundaries) {\n      const b = `(?:(?<=${defaultWordChar})(?!${defaultWordChar})|(?<!${defaultWordChar})(?=${defaultWordChar}))`;\n      const B = `(?:(?<=${defaultWordChar})(?=${defaultWordChar})|(?<!${defaultWordChar})(?!${defaultWordChar}))`;\n      replaceWith(parseFragment(negate ? B : b));\n    }\n    // Kinds `string_end` and `string_start` don't need transformation since JS flag m isn't used.\n    // Kinds `lookahead` and `lookbehind` also don't need transformation\n  },\n\n  Backreference({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidGroupNameJs(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n\n  CapturingGroup({node}, {jsGroupNameMap, subroutineRefMap}) {\n    let {name} = node;\n    if (name && !isValidGroupNameJs(name)) {\n      name = getAndStoreJsGroupName(name, jsGroupNameMap);\n      node.name = name;\n    }\n    subroutineRefMap.set(node.number, node);\n    if (name) {\n      subroutineRefMap.set(name, node);\n    }\n  },\n\n  CharacterSet({node, replaceWith}, {accuracy, minTargetEs2024, digitIsAscii, spaceIsAscii, wordIsAscii}) {\n    const {kind, negate, value} = node;\n    // Flag D with `\\d`, `\\p{Digit}`, `[[:digit:]]`\n    if (digitIsAscii && (kind === AstCharacterSetKinds.digit || value === 'digit')) {\n      replaceWith(createCharacterSet(AstCharacterSetKinds.digit, {negate}));\n      return;\n    }\n    // Flag S with `\\s`, `\\p{Space}`, `[[:space:]]`\n    if (spaceIsAscii && (kind === AstCharacterSetKinds.space || value === 'space')) {\n      replaceWith(setNegate(parseFragment(asciiSpaceChar), negate));\n      return;\n    }\n    // Flag W with `\\w`, `\\p{Word}`, `[[:word:]]`\n    if (wordIsAscii && (kind === AstCharacterSetKinds.word || value === 'word')) {\n      replaceWith(createCharacterSet(AstCharacterSetKinds.word, {negate}));\n      return;\n    }\n    if (kind === AstCharacterSetKinds.any) {\n      replaceWith(createUnicodeProperty('Any'));\n    } else if (kind === AstCharacterSetKinds.digit) {\n      replaceWith(createUnicodeProperty('Nd', {negate}));\n    } else if (kind === AstCharacterSetKinds.hex) {\n      replaceWith(createUnicodeProperty('AHex', {negate}));\n    } else if (kind === AstCharacterSetKinds.non_newline) {\n      replaceWith(parseFragment(r`[^\\n]`));\n    } else if (kind === AstCharacterSetKinds.space) {\n      // Can't use JS's Unicode-based `\\s` since unlike Onig it includes `\\uFEFF`, excludes `\\x85`\n      replaceWith(createUnicodeProperty('space', {negate}));\n    } else if (kind === AstCharacterSetKinds.word) {\n      replaceWith(setNegate(parseFragment(defaultWordChar), negate));\n    } else if (kind === AstCharacterSetKinds.property) {\n      if (!JsUnicodeProperties.has(value)) {\n        // Assume it's a script; no error checking is the price for avoiding heavyweight Unicode\n        // data for all script names\n        node.key = 'sc';\n      }\n    } else if (kind === AstCharacterSetKinds.posix) {\n      if (!minTargetEs2024 && (value === 'graph' || value === 'print')) {\n        if (accuracy === 'strict') {\n          throw new Error(`POSIX class \"${value}\" requires min target ES2024 or non-strict accuracy`);\n        }\n        let ascii = {\n          graph: '!-~',\n          print: ' -~',\n        }[value];\n        if (negate) {\n          // POSIX classes are always nested in a char class; manually invert the range rather than\n          // using `[^…]` so it can be unwrapped since ES2018 doesn't support nested classes\n          ascii = `\\0-${cp(ascii.codePointAt(0) - 1)}${cp(ascii.codePointAt(2) + 1)}-\\u{10FFFF}`;\n        }\n        replaceWith(parseFragment(`[${ascii}]`));\n      } else {\n        replaceWith(setNegate(parseFragment(PosixClassesMap.get(value)), negate));\n      }\n    }\n  },\n\n  Directive(path, state) {\n    const {node, parent, ast, remove, replaceWith, removeAllPrevSiblings, removeAllNextSiblings} = path;\n    const {kind, flags} = node;\n    if (kind === AstDirectiveKinds.flags) {\n      if (!flags.enable && !flags.disable) {\n        // Flag directive without flags; ex: `(?-)`, `(?--)`\n        remove();\n      } else {\n        const flagGroup = prepContainer(createGroup({flags}), removeAllNextSiblings());\n        replaceWith(flagGroup);\n        traverseReplacement(flagGroup, path, state, FirstPassVisitor);\n      }\n    } else if (kind === AstDirectiveKinds.keep) {\n      const firstAltFirstEl = ast.pattern.alternatives[0].elements[0];\n      // Supporting a full-pattern wrapper around `\\K` enables use with flag modifiers\n      const hasWrapperGroup =\n        // Not emulatable if within a `CapturingGroup`\n        hasOnlyChild(ast.pattern, kid => kid.type === AstTypes.Group) &&\n        firstAltFirstEl.alternatives.length === 1;\n      const topLevel = hasWrapperGroup ? firstAltFirstEl : ast.pattern;\n      if (parent.parent !== topLevel || topLevel.alternatives.length > 1) {\n        throw new Error(r`Uses \"\\K\" in a way that's unsupported`);\n      }\n      replaceWith(prepContainer(createLookaround({behind: true}), removeAllPrevSiblings()));\n    }\n  },\n\n  Flags({node, parent}) {\n    // Remove Onig flags that aren't available in JS\n    [ 'digitIsAscii', // Flag D\n      'extended', // Flag x\n      'spaceIsAscii', // Flag S\n      'wordIsAscii', // Flag W\n    ].forEach(f => delete node[f]);\n    Object.assign(node, {\n      // JS flag g; no Onig equiv\n      global: false,\n      // JS flag d; no Onig equiv\n      hasIndices: false,\n      // JS flag m; no Onig equiv but its behavior is always on in Onig. Onig's only line break\n      // char is line feed, unlike JS, so this flag isn't used since it would produce inaccurate\n      // results (also allows `^` and `$` to be used in the generator for string start and end)\n      multiline: false,\n      // JS flag y; no Onig equiv, but used for `\\G` emulation\n      sticky: node.sticky ?? false,\n      // Note: Regex+ doesn't allow explicitly adding flags it handles implicitly, so leave out\n      // properties `unicode` (JS flag u) and `unicodeSets` (JS flag v). Keep the existing values\n      // for `ignoreCase` (flag i) and `dotAll` (JS flag s, but Onig flag m)\n    });\n    // Options accepted by Regex+; see <github.com/slevithan/regex#-options>\n    parent.options = {\n      disable: {\n        // Onig uses different rules for flag x than Regex+, so disable the implicit flag\n        x: true,\n        // Onig has no flag to control \"named capture only\" mode but contextually applies its\n        // behavior when named capturing is used, so disable Regex+'s implicit flag for it\n        n: true,\n      },\n      force: {\n        // Always add flag v because we're generating an AST that relies on it (it enables JS\n        // support for Onig features nested classes, set intersection, Unicode properties, etc.).\n        // However, the generator might disable flag v based on its `target` option\n        v: true,\n      },\n    };\n  },\n\n  Group({node}) {\n    if (!node.flags) {\n      return;\n    }\n    const {enable, disable} = node.flags;\n    // Onig's flag x (`extended`) isn't available in JS\n    enable?.extended && delete enable.extended;\n    disable?.extended && delete disable.extended;\n    // JS doesn't support flag groups that enable and disable the same flag; ex: `(?i-i:)`\n    enable?.dotAll && disable?.dotAll && delete enable.dotAll;\n    enable?.ignoreCase && disable?.ignoreCase && delete enable.ignoreCase;\n    // Cleanup\n    enable && !Object.keys(enable).length && delete node.flags.enable;\n    disable && !Object.keys(disable).length && delete node.flags.disable;\n    !node.flags.enable && !node.flags.disable && delete node.flags;\n  },\n\n  Pattern: {\n    enter({node}, {supportedGNodes}) {\n      // For `\\G` to be accurately emulatable using JS flag y, it must be at (and only at) the start\n      // of every top-level alternative (with complex rules for what determines being at the start).\n      // Additional `\\G` error checking in `Assertion` visitor\n      const leadingGs = [];\n      let hasAltWithLeadG = false;\n      let hasAltWithoutLeadG = false;\n      for (const alt of node.alternatives) {\n        if (alt.elements.length === 1 && alt.elements[0].kind === AstAssertionKinds.search_start) {\n          // Remove the `\\G` (leaving behind an empty alternative, and without adding JS flag y)\n          // since a top-level alternative that includes only `\\G` always matches at the start of the\n          // match attempt. Note that this is based on Oniguruma's rules, and is different than other\n          // regex flavors where `\\G` matches at the end of the previous match (a subtle distinction\n          // that's relevant after zero-length matches)\n          alt.elements.pop();\n        } else {\n          const leadingG = getLeadingG(alt.elements);\n          if (leadingG) {\n            hasAltWithLeadG = true;\n            Array.isArray(leadingG) ?\n              leadingGs.push(...leadingG) :\n              leadingGs.push(leadingG);\n          } else {\n            hasAltWithoutLeadG = true;\n          }\n        }\n      }\n      if (hasAltWithLeadG && !hasAltWithoutLeadG) {\n        // Supported `\\G` nodes will be removed (and add flag y) when traversed\n        leadingGs.forEach(g => supportedGNodes.add(g));\n      }\n    },\n    exit(_, {accuracy, passedLookbehind, strategy}) {\n      if (accuracy === 'strict' && passedLookbehind && strategy) {\n        throw new Error(r`Uses \"\\G\" in a way that requires non-strict accuracy`);\n      }\n    },\n  },\n\n  Quantifier({node}) {\n    if (node.element.type === AstTypes.Quantifier) {\n      // Change e.g. `a**` to `(?:a*)*`\n      const group = prepContainer(createGroup(), [node.element]);\n      // Manually set the parent since we're not using `replaceWith`\n      group.parent = node;\n      node.element = group;\n    }\n  },\n\n  Subroutine({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidGroupNameJs(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n\n  VariableLengthCharacterSet({node, replaceWith}, {accuracy, minTargetEs2024}) {\n    const {kind} = node;\n    if (kind === AstVariableLengthCharacterSetKinds.newline) {\n      replaceWith(parseFragment('(?>\\r\\n?|[\\n\\v\\f\\x85\\u2028\\u2029])'));\n    } else if (kind === AstVariableLengthCharacterSetKinds.grapheme) {\n      if (accuracy === 'strict') {\n        throw new Error(r`Use of \"\\X\" requires non-strict accuracy`);\n      }\n      // `emojiRegex` is more permissive than `\\p{RGI_Emoji}` since it allows over/under-qualified\n      // emoji using a general pattern that matches any Unicode sequence following the structure of\n      // a valid emoji. That actually makes it more accurate for matching any grapheme\n      const emoji = minTargetEs2024 ? r`\\p{RGI_Emoji}` : emojiRegex().source.replace(/\\\\u\\{/g, `\\\\x{`);\n      // Close approximation of an extended grapheme cluster. Details: <unicode.org/reports/tr29/>.\n      // Skip name check to allow `RGI_Emoji` through, which Onig doesn't support\n      replaceWith(parseFragment(r`(?>\\r\\n|${emoji}|\\P{M}\\p{M}*)`, {skipPropertyNameValidation: true}));\n    } else {\n      throw new Error(`Unexpected varcharset kind \"${kind}\"`);\n    }\n  },\n};\n\nconst SecondPassVisitor = {\n  Backreference({node}, {multiplexCapturesToLeftByRef, reffedNodesByReferencer}) {\n    const {orphan, ref} = node;\n    if (!orphan) {\n      // Copy the current state for later multiplexing expansion. That's done in a subsequent pass\n      // because backref numbers need to be recalculated after subroutine expansion\n      reffedNodesByReferencer.set(node, [...multiplexCapturesToLeftByRef.get(ref).map(({node}) => node)]);\n    }\n  },\n\n  CapturingGroup: {\n    enter(\n      { node,\n        replaceWith,\n        skip,\n      },\n      { groupOriginByCopy,\n        groupsByName,\n        multiplexCapturesToLeftByRef,\n        openRefs,\n        reffedNodesByReferencer,\n      }\n    ) {\n      // Has value if we're within a subroutine expansion\n      const origin = groupOriginByCopy.get(node);\n\n      // ## Handle recursion; runs after subroutine expansion\n      if (origin && openRefs.has(node.number)) {\n        // Recursion doesn't affect any following backrefs to its `ref` (unlike other subroutines),\n        // so don't wrap with a capture. The reffed group might have its name removed due to later\n        // subroutine expansion\n        const recursion = createRecursion(node.number);\n        reffedNodesByReferencer.set(recursion, openRefs.get(node.number));\n        replaceWith(recursion);\n        // This node's kids have been removed from the tree, so no need to traverse them\n        skip();\n        return;\n      }\n      openRefs.set(node.number, node);\n\n      // ## Track data for backref multiplexing\n      multiplexCapturesToLeftByRef.set(node.number, []);\n      if (node.name) {\n        getOrInsert(multiplexCapturesToLeftByRef, node.name, []);\n      }\n      const multiplexNodes = multiplexCapturesToLeftByRef.get(node.name ?? node.number);\n      for (let i = 0; i < multiplexNodes.length; i++) {\n        // Captures added via subroutine expansion (maybe indirectly because they were descendant\n        // captures of the reffed group or in a nested subroutine expansion) form a set with their\n        // origin group and any other copies of it added via subroutines. Only the most recently\n        // matched within this set is added to backref multiplexing. So search the list of already-\n        // tracked multiplexed nodes for this group name or number to see if there's a node being\n        // replaced by this capture\n        const multiplex = multiplexNodes[i];\n        if (\n          // This group is from subroutine expansion, and there's a multiplex value from either the\n          // origin node or a prior subroutine expansion group with the same origin\n          (origin === multiplex.node || (origin && origin === multiplex.origin)) ||\n          // This group is not from subroutine expansion, and it comes after a subroutine expansion\n          // group that refers to this group\n          node === multiplex.origin\n        ) {\n          multiplexNodes.splice(i, 1);\n          break;\n        }\n      }\n      multiplexCapturesToLeftByRef.get(node.number).push({node, origin});\n      if (node.name) {\n        multiplexCapturesToLeftByRef.get(node.name).push({node, origin});\n      }\n\n      // ## Track data for duplicate names\n      // Pre-ES2025 doesn't allow duplicate names, but ES2025 allows duplicate names that are\n      // unique per mutually exclusive alternation path. However, Oniguruma's handling for named\n      // subpatterns on match results means we can't use this ES2025 feature even when in an ES2025\n      // env. So, if using a duplicate name, remove the name from all but the first instance that\n      // wasn't created by subroutine expansion\n      if (node.name) {\n        const groupsWithSameName = getOrInsert(groupsByName, node.name, new Map());\n        let hasDuplicateNameToRemove = false;\n        if (origin) {\n          // Subroutines and their child captures shouldn't hold duplicate names in the final state\n          hasDuplicateNameToRemove = true;\n        } else {\n          for (const groupInfo of groupsWithSameName.values()) {\n            if (!groupInfo.hasDuplicateNameToRemove) {\n              // Will change to an unnamed capture in a later pass\n              hasDuplicateNameToRemove = true;\n              break;\n            }\n          }\n        }\n        groupsByName.get(node.name).set(node, {node, hasDuplicateNameToRemove});\n      }\n    },\n    exit({node}, {openRefs}) {\n      openRefs.delete(node.number);\n    },\n  },\n\n  Group: {\n    enter({node}, state) {\n      // Flag directives have already been converted to flag groups by the previous pass\n      state.prevFlags = state.currentFlags;\n      if (node.flags) {\n        state.currentFlags = getNewCurrentFlags(state.currentFlags, node.flags);\n      }\n    },\n    exit(_, state) {\n      state.currentFlags = state.prevFlags;\n    },\n  },\n\n  Recursion({node, parent}, {reffedNodesByReferencer}) {\n    // Recursion nodes are created during the current traversal; they're only traversed here if a\n    // recursion node created during traversal is then copied by a subroutine expansion, e.g. with\n    // `(?<a>\\g<a>)\\g<a>`\n    const {ref} = node;\n    // Immediate parent is an alternative or quantifier; can skip\n    let reffed = parent;\n    while ((reffed = reffed.parent)) {\n      if (reffed.type === AstTypes.CapturingGroup && (reffed.name === ref || reffed.number === ref)) {\n        break;\n      }\n    }\n    // Track the referenced node because `ref`s are rewritten in a subsequent pass; capturing group\n    // names and numbers might change due to subroutine expansion and duplicate group names\n    reffedNodesByReferencer.set(node, reffed);\n  },\n\n  Subroutine(path, state) {\n    const {node, replaceWith} = path;\n    const {ref} = node;\n    const reffedGroupNode = state.subroutineRefMap.get(ref);\n    // Other forms of recursion are handled by the `CapturingGroup` visitor\n    const isGlobalRecursion = ref === 0;\n    const expandedSubroutine = isGlobalRecursion ?\n      createRecursion(0) :\n      // The reffed group might itself contain subroutines, which are expanded during sub-traversal\n      cloneCapturingGroup(reffedGroupNode, state.groupOriginByCopy, null);\n    let replacement = expandedSubroutine;\n    if (!isGlobalRecursion) {\n      // Subroutines take their flags from the reffed group, not the flags surrounding themselves\n      const reffedGroupFlagMods = getCombinedFlagModsFromFlagNodes(getAllParents(reffedGroupNode, node => {\n        return node.type === AstTypes.Group && !!node.flags;\n      }));\n      const reffedGroupFlags = reffedGroupFlagMods ?\n        getNewCurrentFlags(state.globalFlags, reffedGroupFlagMods) :\n        state.globalFlags;\n      if (!areFlagsEqual(reffedGroupFlags, state.currentFlags)) {\n        replacement = prepContainer(createGroup({\n          flags: getFlagModsFromFlags(reffedGroupFlags),\n        }), [expandedSubroutine]);\n      }\n    }\n    replaceWith(replacement);\n    if (!isGlobalRecursion) {\n      traverseReplacement(replacement, path, state, SecondPassVisitor);\n    }\n  },\n};\n\nconst ThirdPassVisitor = {\n  Backreference({node, replaceWith}, state) {\n    if (node.orphan) {\n      state.highestOrphanBackref = Math.max(state.highestOrphanBackref, node.ref);\n      // Don't renumber; used with `allowOrphanBackrefs`\n      return;\n    }\n    const reffedNodes = state.reffedNodesByReferencer.get(node);\n    const participants = reffedNodes.filter(reffed => canParticipateWithNode(reffed, node));\n    // For the backref's `ref`, use `number` rather than `name` because group names might have been\n    // removed if they're duplicates within their alternation path, or they might be removed later\n    // by the generator (depending on target) if they're duplicates within the overall pattern.\n    // Backrefs must come after groups they ref, so reffed node `number`s are already recalculated\n    if (!participants.length) {\n      // If no participating capture, convert backref to to `(?!)`; backrefs to nonparticipating\n      // groups can't match in Onig but match the empty string in JS\n      replaceWith(prepContainer(createLookaround({negate: true})));\n    } else if (participants.length > 1) {\n      // Multiplex\n      const alts = participants.map(reffed => adoptAndSwapKids(\n        createAlternative(),\n        [createBackreference(reffed.number)]\n      ));\n      replaceWith(adoptAndSwapKids(createGroup(), alts));\n    } else {\n      node.ref = participants[0].number;\n    }\n  },\n\n  CapturingGroup({node}, state) {\n    // Recalculate the number since the current value might be wrong due to subroutine expansion\n    node.number = ++state.numCapturesToLeft;\n    if (node.name) {\n      // Removing duplicate names here rather than in an earlier pass avoids extra complexity when\n      // handling subroutine expansion and backref multiplexing\n      if (state.groupsByName.get(node.name).get(node).hasDuplicateNameToRemove) {\n        delete node.name;\n      }\n    }\n  },\n\n  Recursion({node}, state) {\n    if (node.ref === 0) {\n      return;\n    }\n    // For the recursion's `ref`, use `number` rather than `name` because group names might have\n    // been removed if they're duplicates within their alternation path, or they might be removed\n    // later by the generator (depending on target) if they're duplicates within the overall\n    // pattern. Since recursion appears within the group it refs, the reffed node's `number` has\n    // already been recalculated\n    node.ref = state.reffedNodesByReferencer.get(node).number;\n  },\n\n  Regex: {\n    exit({node}, state) {\n      // [HACK] Add unnamed captures to the end of the regex if needed to allow orphaned backrefs\n      // to be valid in JS with flag u/v. This is needed to support TextMate grammars, which\n      // replace numbered backrefs in their `end` pattern with values matched by captures in their\n      // `begin` pattern! See <github.com/microsoft/vscode-textmate/blob/7e0ea282f4f25fef12a6c84fa4fa7266f67b58dc/src/rule.ts#L661-L663>\n      // An `end` pattern, prior to this substitution, might have backrefs to a group that doesn't\n      // exist within `end`. This presents a dilemma since both Oniguruma and JS (with flag u/v)\n      // error for backrefs to undefined captures. So adding captures to the end is a solution that\n      // doesn't change what the regex matches, and lets invalid numbered backrefs through. Note:\n      // Orphan backrefs are only allowed if `allowOrphanBackrefs` is enabled\n      const numCapsNeeded = Math.max(state.highestOrphanBackref - state.numCapturesToLeft, 0);\n      for (let i = 0; i < numCapsNeeded; i++) {\n        const emptyCapture = createCapturingGroup();\n        node.pattern.alternatives.at(-1).elements.push(emptyCapture);\n      }\n    },\n  },\n};\n\n// `\\t\\n\\v\\f\\r\\x20`\nconst asciiSpaceChar = '[\\t-\\r ]';\n// Different than `PosixClassesMap`'s `word`\nconst defaultWordChar = r`[\\p{L}\\p{M}\\p{N}\\p{Pc}]`;\n\nfunction adoptAndSwapKids(parent, kids) {\n  kids.forEach(kid => kid.parent = parent);\n  parent[getContainerAccessor(parent)] = kids;\n  return parent;\n}\n\nfunction areFlagsEqual(a, b) {\n  return a.dotAll === b.dotAll && a.ignoreCase === b.ignoreCase;\n}\n\nfunction canParticipateWithNode(capture, node) {\n  // Walks to the left (prev siblings), down (sibling descendants), up (parent), then back down\n  // (parent's prev sibling descendants) the tree in a loop\n  let rightmostPoint = node;\n  do {\n    if (rightmostPoint.type === AstTypes.Pattern) {\n      // End of the line; capture is not in node's alternation path\n      return false;\n    }\n    if (rightmostPoint.type === AstTypes.Alternative) {\n      // Skip past alts to their parent because we don't want to look at the kids of preceding alts\n      continue;\n    }\n    if (rightmostPoint === capture) {\n      // Capture is ancestor of node\n      return false;\n    }\n    const kidsOfParent = getKids(rightmostPoint.parent);\n    for (const kid of kidsOfParent) {\n      if (kid === rightmostPoint) {\n        // Reached rightmost node in sibling list that we want to consider; break to parent loop\n        break;\n      }\n      if (kid === capture) {\n        return true;\n      }\n      if (hasDescendant(kid, capture)) {\n        return true;\n      }\n    }\n  } while ((rightmostPoint = rightmostPoint.parent));\n  throw new Error('Unexpected path');\n}\n\n// Creates a deep copy of the provided node, with special handling:\n// - Make `parent` props point to their parent in the copy\n// - Update the provided `originMap` for each cloned capturing group (outer and nested)\nfunction cloneCapturingGroup(obj, originMap, up, up2) {\n  const store = Array.isArray(obj) ? [] : {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (key === 'parent') {\n      // If the last cloned item was a container array (for holding kids), use the object above it\n      store.parent = Array.isArray(up) ? up2 : up;\n    } else if (value && typeof value === 'object') {\n      store[key] = cloneCapturingGroup(value, originMap, store, up);\n    } else {\n      if (key === 'type' && value === AstTypes.CapturingGroup) {\n        // Key is the copied node, value is the origin node\n        originMap.set(store, originMap.get(obj) ?? obj);\n      }\n      store[key] = value;\n    }\n  }\n  return store;\n}\n\n// `Recursion` nodes are used only by the transformer\nfunction createRecursion(ref) {\n  return {\n    type: AstTypes.Recursion,\n    ref,\n  };\n}\n\nfunction getAllParents(node, filterFn) {\n  const results = [];\n  while ((node = node.parent)) {\n    if (!filterFn || filterFn(node)) {\n      results.push(node);\n    }\n  }\n  return results;\n}\n\n// See also `isValidGroupNameJs`\nfunction getAndStoreJsGroupName(name, map) {\n  if (map.has(name)) {\n    return map.get(name);\n  }\n  // Onig group names can't start with `$`, but JS names can\n  const jsName = `$${map.size}_${name.replace(/^[^$_\\p{IDS}]|[^$\\u200C\\u200D\\p{IDC}]/ug, '_')}`;\n  map.set(name, jsName);\n  return jsName;\n}\n\n// Returns the string key for the container that holds the node's kids\nfunction getContainerAccessor(node) {\n  for (const accessor of ['alternatives', 'classes', 'elements']) {\n    if (node[accessor]) {\n      return accessor;\n    }\n  }\n  return null;\n}\n\nfunction getCombinedFlagModsFromFlagNodes(flagNodes) {\n  const flagProps = ['dotAll', 'ignoreCase'];\n  const combinedFlags = {enable: {}, disable: {}};\n  flagNodes.forEach(({flags}) => {\n    flagProps.forEach(prop => {\n      if (flags.enable?.[prop]) {\n        // Need to remove `disable` since disabled flags take precedence\n        delete combinedFlags.disable[prop];\n        combinedFlags.enable[prop] = true;\n      }\n      if (flags.disable?.[prop]) {\n        combinedFlags.disable[prop] = true;\n      }\n    });\n  });\n  if (!Object.keys(combinedFlags.enable).length) {\n    delete combinedFlags.enable;\n  }\n  if (!Object.keys(combinedFlags.disable).length) {\n    delete combinedFlags.disable;\n  }\n  if (combinedFlags.enable || combinedFlags.disable) {\n    return combinedFlags;\n  }\n  return null;\n}\n\nfunction getFlagModsFromFlags({dotAll, ignoreCase}) {\n  const mods = {};\n  if (dotAll || ignoreCase) {\n    mods.enable = {};\n    dotAll && (mods.enable.dotAll = true);\n    ignoreCase && (mods.enable.ignoreCase = true);\n  }\n  if (!dotAll || !ignoreCase) {\n    mods.disable = {};\n    !dotAll && (mods.disable.dotAll = true);\n    !ignoreCase && (mods.disable.ignoreCase = true);\n  }\n  return mods;\n}\n\nfunction getKids(node) {\n  if (!node) {\n    throw new Error('Node expected');\n  }\n  // [NOTE] Not handling `Regex` kids (`pattern`, `flags`) and `CharacterClassRange` kids (`min`,\n  // `max`) only because not needed by current callers\n  if (node.type === AstTypes.Quantifier) {\n    return [node.element];\n  }\n  const accessor = getContainerAccessor(node);\n  return accessor && node[accessor];\n}\n\nfunction getLeadingG(els) {\n  const firstToConsider = els.find(el => (\n    el.kind === AstAssertionKinds.search_start ||\n    isLoneGLookaround(el, {negate: false}) ||\n    !isAlwaysZeroLength(el)\n  ));\n  if (!firstToConsider) {\n    return null;\n  }\n  if (firstToConsider.kind === AstAssertionKinds.search_start) {\n    return firstToConsider;\n  }\n  if (isLookaround(firstToConsider)) {\n    return firstToConsider.alternatives[0].elements[0];\n  }\n  if (isConsumptiveGroup(firstToConsider)) {\n    const gNodesForGroup = [];\n    // Recursively find `\\G` nodes for all alternatives in the group\n    for (const alt of firstToConsider.alternatives) {\n      const leadingG = getLeadingG(alt.elements);\n      if (!leadingG) {\n        // Don't return `gNodesForGroup` collected so far since this alt didn't qualify\n        return null;\n      }\n      Array.isArray(leadingG) ?\n        gNodesForGroup.push(...leadingG) :\n        gNodesForGroup.push(leadingG);\n    }\n    return gNodesForGroup;\n  }\n  return null;\n}\n\nfunction hasDescendant(node, descendant) {\n  const kids = getKids(node) ?? [];\n  for (const kid of kids) {\n    if (\n      kid === descendant ||\n      hasDescendant(kid, descendant)\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isLoneGLookaround(node, options) {\n  const opts = {\n    negate: null,\n    ...options,\n  };\n  return (\n    isLookaround(node) &&\n    (opts.negate === null || node.negate === opts.negate) &&\n    hasOnlyChild(node, kid => kid.kind === AstAssertionKinds.search_start)\n  );\n}\n\n// See also `getAndStoreJsGroupName`\nfunction isValidGroupNameJs(name) {\n  // JS group names are more restrictive than Onig; see `isValidGroupNameOniguruma` and\n  // <developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#identifiers>\n  return /^[$_\\p{IDS}][$\\u200C\\u200D\\p{IDC}]*$/u.test(name);\n}\n\n// Returns a single node, either the given node or all nodes wrapped in a noncapturing group\nfunction parseFragment(pattern, options) {\n  const ast = parse(tokenize(pattern), options);\n  const alts = ast.pattern.alternatives;\n  if (alts.length > 1 || alts[0].elements.length > 1) {\n    return adoptAndSwapKids(createGroup(), alts);\n  }\n  return alts[0].elements[0];\n}\n\nfunction prepContainer(node, kids) {\n  const accessor = getContainerAccessor(node);\n  // Set the parent for the default container of a new node\n  node[accessor][0].parent = node;\n  if (kids) {\n    adoptAndSwapKids(node[accessor][0], kids);\n  }\n  return node;\n}\n\nfunction setNegate(node, negate) {\n  node.negate = negate;\n  return node;\n}\n\nfunction traverseReplacement(replacement, {parent, key, container}, state, visitor) {\n  traverse({\n    // Don't use the `node` from `path`\n    node: replacement,\n    parent,\n    key,\n    container,\n  }, state, visitor);\n}\n\nexport {\n  transform,\n};\n", "import {getOptions} from './options.js';\nimport {AstAssertionKinds, AstCharacterSetKinds, AstTypes, createCharacter} from './parse.js';\nimport {traverse} from './traverse.js';\nimport {getIgnoreCaseMatchChars, UnicodePropertiesWithSpecificCase} from './unicode.js';\nimport {cp, envFlags, getNewCurrentFlags, getOrInsert, isMinTarget, r} from './utils.js';\nimport {isLookaround} from './utils-ast.js';\n\n/**\nGenerates a Regex+ compatible `pattern`, `flags`, and `options` from a Regex+ AST.\n@param {import('./transform.js').RegexAst} ast\n@param {import('.').ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options: Object;\n  _captureTransfers: Map<number, Array<number>>;\n  _hiddenCaptures: Array<number>;\n}}\n*/\nfunction generate(ast, options) {\n  const opts = getOptions(options);\n  const minTargetEs2024 = isMinTarget(opts.target, 'ES2024');\n  const minTargetEs2025 = isMinTarget(opts.target, 'ES2025');\n  const recursionLimit = opts.rules.recursionLimit;\n  if (!Number.isInteger(recursionLimit) || recursionLimit < 2 || recursionLimit > 20) {\n    throw new Error('Invalid recursionLimit; use 2-20');\n  }\n\n  // If the output can't use flag groups, we need a pre-pass to check for the use of chars with\n  // case in case sensitive/insensitive states. This minimizes the need for case expansions (though\n  // expansions are lossless, even given Unicode case complexities) and allows supporting case\n  // insensitive backrefs in more cases\n  // [TODO] Consider gathering this data in the transformer's final traversal to avoid work here\n  let hasCaseInsensitiveNode = null;\n  let hasCaseSensitiveNode = null;\n  if (!minTargetEs2025) {\n    const iStack = [ast.flags.ignoreCase];\n    traverse({node: ast}, {\n      getCurrentModI: () => iStack.at(-1),\n      popModI() {iStack.pop()},\n      pushModI(isIOn) {iStack.push(isIOn)},\n      setHasCasedChar() {\n        if (iStack.at(-1)) {\n          hasCaseInsensitiveNode = true;\n        } else {\n          hasCaseSensitiveNode = true;\n        }\n      },\n    }, FlagModifierVisitor);\n  }\n\n  const appliedGlobalFlags = {\n    dotAll: ast.flags.dotAll,\n    // - Turn global flag i on if a case insensitive node was used and no case sensitive nodes were\n    //   used (to avoid unnecessary node expansion).\n    // - Turn global flag i off if a case sensitive node was used (since case sensitivity can't be\n    //   forced without the use of ES2025 flag groups)\n    ignoreCase: !!((ast.flags.ignoreCase || hasCaseInsensitiveNode) && !hasCaseSensitiveNode),\n  };\n  let lastNode = null;\n  const state = {\n    accuracy: opts.accuracy,\n    appliedGlobalFlags,\n    captureMap: new Map(),\n    currentFlags: {\n      dotAll: ast.flags.dotAll,\n      ignoreCase: ast.flags.ignoreCase,\n    },\n    inCharClass: false,\n    lastNode,\n    originMap: ast._originMap,\n    recursionLimit,\n    useAppliedIgnoreCase: !!(!minTargetEs2025 && hasCaseInsensitiveNode && hasCaseSensitiveNode),\n    useFlagMods: minTargetEs2025,\n    useFlagV: minTargetEs2024,\n    verbose: opts.verbose,\n  };\n  function gen(node) {\n    state.lastNode = lastNode;\n    lastNode = node;\n    switch (node.type) {\n      case AstTypes.Regex:\n        // Final result is an object; other node types return strings\n        return {\n          pattern: gen(node.pattern),\n          flags: gen(node.flags),\n          options: {...node.options},\n        };\n      case AstTypes.Alternative:\n        return node.elements.map(gen).join('');\n      case AstTypes.Assertion:\n        return genAssertion(node, state, gen);\n      case AstTypes.Backreference:\n        return genBackreference(node, state);\n      case AstTypes.CapturingGroup:\n        return genCapturingGroup(node, state, gen);\n      case AstTypes.Character:\n        return genCharacter(node, state);\n      case AstTypes.CharacterClass:\n        return genCharacterClass(node, state, gen);\n      case AstTypes.CharacterClassIntersection:\n        if (!state.useFlagV) {\n          throw new Error('Use of class intersection requires min target ES2024');\n        }\n        return node.classes.map(gen).join('&&');\n      case AstTypes.CharacterClassRange:\n        return genCharacterClassRange(node, state);\n      case AstTypes.CharacterSet:\n        return genCharacterSet(node, state);\n      case AstTypes.Flags:\n        return genFlags(node, state);\n      case AstTypes.Group:\n        return genGroup(node, state, gen);\n      case AstTypes.Pattern:\n        return node.alternatives.map(gen).join('|');\n      case AstTypes.Quantifier:\n        return gen(node.element) + getQuantifierStr(node);\n      case AstTypes.Recursion:\n        return genRecursion(node, state);\n      default:\n        // Node types `AbsentFunction`, `Directive`, `Subroutine`, and `VariableLengthCharacterSet`\n        // are never included in transformer output\n        throw new Error(`Unexpected node type \"${node.type}\"`);\n    }\n  }\n\n  const result = gen(ast);\n  if (!minTargetEs2024) {\n    // Switch from flag v to u; Regex+ implicitly chooses by default\n    delete result.options.force.v;\n    result.options.disable.v = true;\n    result.options.unicodeSetsPlugin = null;\n  }\n  result._captureTransfers = new Map();\n  result._hiddenCaptures = [];\n  state.captureMap.forEach((value, key) => {\n    if (value.hidden) {\n      result._hiddenCaptures.push(key);\n    }\n    if (value.transferTo) {\n      getOrInsert(result._captureTransfers, value.transferTo, []).push(key);\n    }\n  });\n\n  return result;\n}\n\nconst FlagModifierVisitor = {\n  AnyGroup: {\n    enter({node}, state) {\n      const currentModI = state.getCurrentModI();\n      state.pushModI(\n        node.flags ?\n          getNewCurrentFlags({ignoreCase: currentModI}, node.flags).ignoreCase :\n          currentModI\n      );\n    },\n    exit(_, state) {\n      state.popModI();\n    },\n  },\n  Backreference(_, state) {\n    // Can't know for sure, so assume the backref will include chars with case (best that could be\n    // done is not calling `setHasCasedChar` if the reffed group doesn't contain a char with case\n    // or most kinds of char sets)\n    state.setHasCasedChar();\n  },\n  Character({node}, state) {\n    if (charHasCase(cp(node.value))) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterClassRange({node, skip}, state) {\n    skip();\n    if (getCasesOutsideCharClassRange(node, {firstOnly: true}).length) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterSet({node}, state) {\n    if (\n      node.kind === AstCharacterSetKinds.property &&\n      UnicodePropertiesWithSpecificCase.has(node.value)\n    ) {\n      state.setHasCasedChar();\n    }\n  },\n};\n\nconst BaseEscapeChars = new Set([\n  '$', '(', ')', '*', '+', '.', '?', '[', '\\\\', ']', '^', '{', '|', '}',\n]);\nconst CharClassEscapeChars = new Set([\n  '-', '\\\\', ']', '^',\n  // Literal `[` doesn't require escaping with flag u, but this can help work around regex source\n  // linters and regex syntax processors that expect unescaped `[` to create a nested class\n  '[',\n]);\nconst CharClassEscapeCharsFlagV = new Set([\n  '(', ')', '-', '/', '[', '\\\\', ']', '^', '{', '|', '}',\n  // Double punctuators; also includes already-listed `-` and `^`\n  '!', '#', '$', '%', '&', '*', '+', ',', '.', ':', ';', '<', '=', '>', '?', '@', '`', '~',\n]);\nconst CharCodeEscapeMap = new Map([\n  [ 9, r`\\t`], // horizontal tab\n  [10, r`\\n`], // line feed\n  [11, r`\\v`], // vertical tab\n  [12, r`\\f`], // form feed\n  [13, r`\\r`], // carriage return\n  [0x2028, r`\\u2028`], // line separator\n  [0x2029, r`\\u2029`], // paragraph separator\n  [0xFEFF, r`\\uFEFF`], // ZWNBSP/BOM\n]);\n\nconst casedRe = /^\\p{Cased}$/u;\nfunction charHasCase(char) {\n  return casedRe.test(char);\n}\n\nfunction genAssertion(node, _, gen) {\n  const {kind, negate, alternatives} = node;\n  if (isLookaround(node)) {\n    const prefix = `${kind === AstAssertionKinds.lookahead ? '' : '<'}${negate ? '!' : '='}`;\n    return `(?${prefix}${alternatives.map(gen).join('|')})`;\n  }\n  // Can always use `^` and `$` for string boundaries since JS flag m is never relied on; Onig uses\n  // different line break chars\n  if (kind === AstAssertionKinds.string_end) {\n    return '$';\n  }\n  if (kind === AstAssertionKinds.string_start) {\n    return '^';\n  }\n  // If a word boundary came through the transformer unaltered, that means `wordIsAscii` or\n  // `asciiWordBoundaries` is enabled\n  if (kind === AstAssertionKinds.word_boundary) {\n    return negate ? r`\\B` : r`\\b`;\n  }\n  // Kinds `line_end`, `line_start`, `search_start`, and `string_end_newline` are never included in\n  // transformer output\n  throw new Error(`Unexpected assertion kind \"${kind}\"`);\n}\n\nfunction genBackreference({ref}, state) {\n  if (typeof ref !== 'number') {\n    throw new Error('Unexpected named backref in transformed AST');\n  }\n  if (\n    !state.useFlagMods &&\n    state.accuracy === 'strict' &&\n    state.currentFlags.ignoreCase &&\n    !state.captureMap.get(ref).ignoreCase\n  ) {\n    throw new Error('Use of case-insensitive backref to case-sensitive group requires target ES2025 or non-strict accuracy');\n  }\n  return '\\\\' + ref;\n}\n\nfunction genCapturingGroup(node, state, gen) {\n  const {name, number, alternatives} = node;\n  const data = {ignoreCase: state.currentFlags.ignoreCase};\n  // Has origin if the capture is from an expanded subroutine\n  const origin = state.originMap.get(node);\n  if (origin) {\n    // All captures from/within expanded subroutines are marked as hidden\n    data.hidden = true;\n    // If a subroutine (or descendant capture) occurs after its origin group, it's marked to have\n    // its captured value transferred to the origin's capture slot. `number` and `origin.number`\n    // are the capture numbers *after* subroutine expansion\n    if (number > origin.number) {\n      data.transferTo = origin.number;\n    }\n  }\n  state.captureMap.set(number, data);\n  return `(${name ? `?<${name}>` : ''}${alternatives.map(gen).join('|')})`;\n}\n\nfunction genCharacter({value}, state) {\n  const char = cp(value);\n  const escaped = getCharEscape(value, {\n    isAfterBackref: state.lastNode.type === AstTypes.Backreference,\n    inCharClass: state.inCharClass,\n    useFlagV: state.useFlagV,\n  });\n  if (escaped !== char) {\n    return escaped;\n  }\n  if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase && charHasCase(char)) {\n    const cases = getIgnoreCaseMatchChars(char);\n    return state.inCharClass ?\n      cases.join('') :\n      (cases.length > 1 ? `[${cases.join('')}]` : cases[0]);\n  }\n  return char;\n}\n\nfunction genCharacterClass({negate, parent, elements}, state, gen) {\n  // Work around WebKit parser bug by moving literal hyphens to the end of the class\n  if (envFlags.literalHyphenIncorrectlyCreatesRange && state.useFlagV && elements.some(isLiteralHyphen)) {\n    // Remove all hyphens then add one at the end; can't just sort in case of e.g. `[\\d\\-\\-]`\n    elements = elements.filter(node => !isLiteralHyphen(node));\n    elements.push(createCharacter(45));\n  }\n  const genClass = () => `[${negate ? '^' : ''}${elements.map(gen).join('')}]`;\n  if (!state.inCharClass) {\n    // For the outermost char class, set state\n    state.inCharClass = true;\n    const result = genClass();\n    state.inCharClass = false;\n    return result;\n  }\n  // No first element for implicit class in empty intersection like `[&&]`\n  const firstType = elements[0]?.type;\n  if (\n    !negate &&\n    firstType &&\n    (\n      ( // Allows many nested classes to work with `target` ES2018 which doesn't support nesting\n        (!state.useFlagV || !state.verbose) &&\n        parent.type === AstTypes.CharacterClass &&\n        firstType !== AstTypes.CharacterClassIntersection &&\n        !(envFlags.literalHyphenIncorrectlyCreatesRange && state.useFlagV)\n      ) ||\n      ( !state.verbose &&\n        parent.type === AstTypes.CharacterClassIntersection &&\n        // JS doesn't allow intersection with union or ranges\n        elements.length === 1 &&\n        firstType !== AstTypes.CharacterClass &&\n        firstType !== AstTypes.CharacterClassRange\n      )\n    )\n  ) {\n    // Remove unnecessary nesting; unwrap kids into the parent char class. Some basic char class\n    // optimization for intersection has already been done in the parser\n    return elements.map(gen).join('');\n  }\n  if (!state.useFlagV && parent.type === AstTypes.CharacterClass) {\n    throw new Error('Use of nested character class requires min target ES2024');\n  }\n  return genClass();\n}\n\nfunction genCharacterClassRange(node, state) {\n  const min = node.min.value;\n  const max = node.max.value;\n  const escOpts = {\n    isAfterBackref: false,\n    inCharClass: true,\n    useFlagV: state.useFlagV,\n  };\n  const minStr = getCharEscape(min, escOpts);\n  const maxStr = getCharEscape(max, escOpts);\n  const extraChars = new Set();\n  if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase) {\n    // [TODO] Avoid duplication by considering other chars in the parent char class when expanding\n    const charsOutsideRange = getCasesOutsideCharClassRange(node);\n    const ranges = getCodePointRangesFromChars(charsOutsideRange);\n    ranges.forEach(value => {\n      extraChars.add(\n        Array.isArray(value) ?\n          `${getCharEscape(value[0], escOpts)}-${getCharEscape(value[1], escOpts)}` :\n          getCharEscape(value, escOpts)\n      );\n    });\n  }\n  // Create the range without calling `gen` on the `min`/`max` kids\n  return `${minStr}-${maxStr}${[...extraChars].join('')}`;\n}\n\nfunction genCharacterSet({kind, negate, value, key}, state) {\n  if (kind === AstCharacterSetKinds.dot) {\n    return state.currentFlags.dotAll ?\n      ((state.appliedGlobalFlags.dotAll || state.useFlagMods) ? '.' : '[^]') :\n      // Onig's only line break char is line feed, unlike JS\n      r`[^\\n]`;\n  }\n  if (kind === AstCharacterSetKinds.digit) {\n    return negate ? r`\\D` : r`\\d`;\n  }\n  if (kind === AstCharacterSetKinds.property) {\n    if (\n      state.useAppliedIgnoreCase &&\n      state.currentFlags.ignoreCase &&\n      UnicodePropertiesWithSpecificCase.has(value)\n    ) {\n      // Support for this would require heavy Unicode data. Could change e.g. `\\p{Lu}` to `\\p{LC}`\n      // if not using strict `accuracy` (since it's close but not 100%), but this wouldn't work\n      // for e.g. `\\p{Lt}`, and in any case, it's probably user error if using these case-specific\n      // props case-insensitively\n      throw new Error(`Unicode property \"${value}\" can't be case-insensitive when other chars have specific case`);\n    }\n    return `${negate ? r`\\P` : r`\\p`}{${key ? `${key}=` : ''}${value}}`;\n  }\n  if (kind === AstCharacterSetKinds.word) {\n    return negate ? r`\\W` : r`\\w`;\n  }\n  // Kinds `hex`, `posix`, and `space` are never included in transformer output\n  throw new Error(`Unexpected character set kind \"${kind}\"`);\n}\n\nfunction genFlags(node, state) {\n  return (\n    // The transformer should never turn on the properties for flags d, g, and m since Onig doesn't\n    // have equivs. Flag m is never relied on since Onig uses different line break chars than JS\n    // (node.hasIndices ? 'd' : '') +\n    // (node.global ? 'g' : '') +\n    // (node.multiline ? 'm' : '') +\n    (state.appliedGlobalFlags.ignoreCase ? 'i' : '') +\n    (node.dotAll ? 's' : '') +\n    (node.sticky ? 'y' : '')\n    // Regex+ doesn't allow explicitly adding flags it handles implicitly, so there are no\n    // `unicode` (flag u) or `unicodeSets` (flag v) props; those flags are added separately\n  );\n}\n\nfunction genGroup({atomic, flags, parent, alternatives}, state, gen) {\n  const currentFlags = state.currentFlags;\n  if (flags) {\n    state.currentFlags = getNewCurrentFlags(currentFlags, flags);\n  }\n  const contents = alternatives.map(gen).join('|');\n  const result = (\n    !state.verbose &&\n    alternatives.length === 1 &&\n    parent.type !== AstTypes.Quantifier &&\n    !atomic &&\n    (!state.useFlagMods || !flags)\n   ) ? contents : `(?${getGroupPrefix(atomic, flags, state.useFlagMods)}${contents})`;\n  state.currentFlags = currentFlags;\n  return result;\n}\n\nfunction genRecursion({ref}, state) {\n  const limit = state.recursionLimit;\n  // Using the syntax supported by `regex-recursion`\n  return ref === 0 ? `(?R=${limit})` : r`\\g<${ref}&R=${limit}>`;\n}\n\n/**\nGiven a `CharacterClassRange` node, returns an array of chars that are a case variant of a char in\nthe range, and aren't already in the range.\n*/\nfunction getCasesOutsideCharClassRange(node, options) {\n  const firstOnly = !!options?.firstOnly;\n  const min = node.min.value;\n  const max = node.max.value;\n  const found = [];\n  // Avoid unneeded work. Assumptions (per Unicode 16):\n  // - No case variants cross the Basic Multilingual Plane boundary\n  // - No cased chars appear beyond the Supplementary Multilingual Plane\n  if ((min < 65 && (max === 0xFFFF || max >= 0x1FFFF)) || (min === 0x10000 && max >= 0x1FFFF)) {\n    return found;\n  }\n  for (let i = min; i <= max; i++) {\n    const char = cp(i);\n    if (!charHasCase(char)) {\n      continue;\n    }\n    const charsOutsideRange = getIgnoreCaseMatchChars(char).filter(caseOfChar => {\n      const num = caseOfChar.codePointAt(0);\n      return num < min || num > max;\n    });\n    if (charsOutsideRange.length) {\n      found.push(...charsOutsideRange);\n      if (firstOnly) {\n        break;\n      }\n    }\n  }\n  return found;\n}\n\n// This shouldn't modifiy any char that has case\nfunction getCharEscape(codePoint, {isAfterBackref, inCharClass, useFlagV}) {\n  if (CharCodeEscapeMap.has(codePoint)) {\n    return CharCodeEscapeMap.get(codePoint);\n  }\n  if (\n    // Control chars, etc.; condition modeled on the Chrome developer console's display for strings\n    codePoint < 32 || (codePoint > 126 && codePoint < 160) ||\n    // Unicode planes 4-16; unassigned, special purpose, and private use area\n    codePoint > 0x3FFFF ||\n    // Avoid corrupting a preceding backref by immediately following it with a literal digit\n    (isAfterBackref && isDigitCharCode(codePoint))\n  ) {\n    // Don't convert codePoint `0` to `\\0` since that's corruptible by following literal digits\n    return codePoint > 0xFF ?\n      `\\\\u{${codePoint.toString(16).toUpperCase()}}` :\n      `\\\\x${codePoint.toString(16).toUpperCase().padStart(2, '0')}`;\n  }\n  const escapeChars = inCharClass ?\n    (useFlagV ? CharClassEscapeCharsFlagV : CharClassEscapeChars) :\n    BaseEscapeChars;\n  const char = cp(codePoint);\n  return (escapeChars.has(char) ? '\\\\' : '') + char;\n}\n\nfunction getCodePointRangesFromChars(chars) {\n  const codePoints = chars.map(char => char.codePointAt(0)).sort((a, b) => a - b);\n  const values = [];\n  let start = null;\n  for (let i = 0; i < codePoints.length; i++) {\n    if (codePoints[i + 1] === codePoints[i] + 1) {\n      start ??= codePoints[i];\n    } else if (start === null) {\n      values.push(codePoints[i]);\n    } else {\n      values.push([start, codePoints[i]]);\n      start = null;\n    }\n  }\n  return values;\n}\n\nfunction getGroupPrefix(atomic, flagMods, useFlagMods) {\n  if (atomic) {\n    return '>';\n  }\n  let mods = '';\n  if (flagMods && useFlagMods) {\n    const {enable, disable} = flagMods;\n    mods =\n      (enable?.ignoreCase ? 'i' : '') +\n      (enable?.dotAll ? 's' : '') +\n      (disable ? '-' : '') +\n      (disable?.ignoreCase ? 'i' : '') +\n      (disable?.dotAll ? 's' : '');\n  }\n  return `${mods}:`;\n}\n\nfunction getQuantifierStr({min, max, greedy, possessive}) {\n  let base;\n  if (!min && max === 1) {\n    base = '?';\n  } else if (!min && max === Infinity) {\n    base = '*';\n  } else if (min === 1 && max === Infinity) {\n    base = '+';\n  } else if (min === max) {\n    base = `{${min}}`;\n  } else {\n    base = `{${min},${max === Infinity ? '' : max}}`;\n  }\n  return base + (possessive ? '+' : (greedy ? '' : '?'));\n}\n\nfunction isDigitCharCode(value) {\n  return value > 47 && value < 58;\n}\n\nfunction isLiteralHyphen({type, value}) {\n  return type === AstTypes.Character && value === 45;\n}\n\nexport {\n  generate,\n};\n", "import {getOrInsert, throwIfNot} from './utils.js';\n\n/**\n@typedef {{\n  hiddenCaptures?: Array<number>;\n  lazyCompile?: boolean;\n  strategy?: string?;\n  transfers?: Array<[number, Array<number>]>;\n}} EmulatedRegExpOptions\n*/\n\n/**\nWorks the same as JavaScript's native `RegExp` constructor in all contexts, but can be given\nresults from `toRegExpDetails` to produce the same result as `toRegExp`.\n*/\nclass EmulatedRegExp extends RegExp {\n  /**\n  @type {Map<number, {\n    hidden?: true;\n    transferTo?: number;\n  }>}\n  */\n  #captureMap = new Map();\n\n  /**\n  @type {RegExp | EmulatedRegExp | null}\n  */\n  #compiled = null;\n\n  /**\n  @type {string}\n  */\n  #pattern;\n\n  /**\n  @type {Map<number, string>?}\n  */\n  #nameMap = null;\n\n  /**\n  @type {string?}\n  */\n  #strategy = null;\n\n  /**\n  Can be used to serialize the instance.\n  @type {EmulatedRegExpOptions}\n  */\n  rawOptions = {};\n\n  // Override the getter with one that works with lazy-compiled regexes\n  get source() {\n    return this.#pattern || '(?:)';\n  }\n\n  /**\n  @overload\n  @param {string} pattern\n  @param {string} [flags]\n  @param {EmulatedRegExpOptions} [options]\n  */\n  /**\n  @overload\n  @param {EmulatedRegExp} pattern\n  @param {string} [flags]\n  */\n  constructor(pattern, flags, options) {\n    const lazyCompile = !!options?.lazyCompile;\n    if (pattern instanceof RegExp) {\n      // Argument `options` isn't provided when regexes are copied, including as part of the\n      // internal handling of string methods `matchAll` and `split`\n      if (options) {\n        throw new Error('Cannot provide options when copying a regexp');\n      }\n      const re = pattern; // Alias for readability\n      super(re, flags);\n      this.#pattern = re.source;\n      if (re instanceof EmulatedRegExp) {\n        this.#captureMap = re.#captureMap;\n        this.#nameMap = re.#nameMap;\n        this.#strategy = re.#strategy;\n        this.rawOptions = re.rawOptions;\n      }\n    } else {\n      const opts = {\n        hiddenCaptures: [],\n        strategy: null,\n        transfers: [],\n        ...options,\n      };\n      super(lazyCompile ? '' : pattern, flags);\n      this.#pattern = pattern;\n      this.#captureMap = createCaptureMap(opts.hiddenCaptures, opts.transfers);\n      this.#strategy = opts.strategy;\n      // Don't add default values from `opts` since this gets serialized\n      this.rawOptions = options ?? {};\n    }\n    if (!lazyCompile) {\n      this.#compiled = this;\n    }\n  }\n\n  /**\n  Called internally by all String/RegExp methods that use regexes.\n  @override\n  @param {string} str\n  @returns {RegExpExecArray?}\n  */\n  exec(str) {\n    // Lazy compilation\n    if (!this.#compiled) {\n      const {lazyCompile, ...rest} = this.rawOptions;\n      this.#compiled = new EmulatedRegExp(this.#pattern, this.flags, rest);\n    }\n\n    const useLastIndex = this.global || this.sticky;\n    const pos = this.lastIndex;\n\n    if (this.#strategy === 'clip_search' && useLastIndex && pos) {\n      // Reset since this tests on a sliced string that we want to match at the start of\n      this.lastIndex = 0;\n      // Slicing the string can lead to mismatches when three edge cases are stacked on each other:\n      // 1. An uncommon use of `\\G` that relies on `clip_search`, combined with...\n      // 2. Lookbehind that searches behind the search start (not match start) position...\n      // 3. During a search when the regex's `lastIndex` isn't `0`.\n      // The `clip_search` strategy is therefore only allowed when lookbehind isn't present, if\n      // using strict `accuracy`\n      const match = this.#execCore(str.slice(pos));\n      if (match) {\n        adjustMatchDetailsForOffset(match, pos, str, this.hasIndices);\n        this.lastIndex += pos;\n      }\n      return match;\n    }\n\n    return this.#execCore(str);\n  }\n\n  /**\n  Adds support for hidden and transfer captures.\n  @param {string} str\n  @returns\n  */\n  #execCore(str) {\n    // Support lazy compilation\n    this.#compiled.lastIndex = this.lastIndex;\n    const match = super.exec.call(this.#compiled, str);\n    this.lastIndex = this.#compiled.lastIndex;\n\n    if (!match || !this.#captureMap.size) {\n      return match;\n    }\n\n    const matchCopy = [...match];\n    // Empty all but the first value of the array while preserving its other properties\n    match.length = 1;\n    let indicesCopy;\n    if (this.hasIndices) {\n      indicesCopy = [...match.indices];\n      match.indices.length = 1;\n    }\n    const mappedNums = [0];\n    for (let i = 1; i < matchCopy.length; i++) {\n      const {hidden, transferTo} = this.#captureMap.get(i) ?? {};\n      if (hidden) {\n        mappedNums.push(null);\n      } else {\n        mappedNums.push(match.length);\n        match.push(matchCopy[i]);\n        if (this.hasIndices) {\n          match.indices.push(indicesCopy[i]);\n        }\n      }\n\n      // Only transfer if the capture participated in the match\n      if (transferTo && matchCopy[i] !== undefined) {\n        const to = throwIfNot(mappedNums[transferTo]);\n        match[to] = matchCopy[i];\n        if (this.hasIndices) {\n          match.indices[to] = indicesCopy[i];\n        }\n        if (match.groups) {\n          if (!this.#nameMap) {\n            // Generate and cache the first time it's needed\n            this.#nameMap = createNameMap(this.source);\n          }\n          const name = this.#nameMap.get(transferTo);\n          if (name) {\n            match.groups[name] = matchCopy[i];\n            if (this.hasIndices) {\n              match.indices.groups[name] = indicesCopy[i];\n            }\n          }\n        }\n      }\n    }\n\n    return match;\n  }\n}\n\nfunction adjustMatchDetailsForOffset(match, offset, input, hasIndices) {\n  match.index += offset;\n  match.input = input;\n  if (hasIndices) {\n    const indices = match.indices;\n    for (let i = 0; i < indices.length; i++) {\n      const arr = indices[i];\n      if (arr) {\n        // Replace the array rather than updating values since the keys of `match.indices` and\n        // `match.indices.groups` share their value arrays by reference. Need to be precise in case\n        // they were previously altered separately\n        indices[i] = [arr[0] + offset, arr[1] + offset];\n      }\n    }\n    const groupIndices = indices.groups;\n    if (groupIndices) {\n      Object.keys(groupIndices).forEach(key => {\n        const arr = groupIndices[key];\n        if (arr) {\n          groupIndices[key] = [arr[0] + offset, arr[1] + offset];\n        }\n      });\n    }\n  }\n}\n\n/**\nBuild the capturing group map, with hidden/transfer groups marked to indicate their submatches\nshould get special handling in match results.\n@param {Array<number>} hiddenCaptures\n@param {Array<[number, Array<number>]>} transfers\n@returns {Map<number, {\n  hidden?: true;\n  transferTo?: number;\n}>}\n*/\nfunction createCaptureMap(hiddenCaptures, transfers) {\n  const captureMap = new Map();\n  for (const num of hiddenCaptures) {\n    captureMap.set(num, {\n      hidden: true,\n    });\n  }\n  for (const [to, from] of transfers) {\n    for (const num of from) {\n      getOrInsert(captureMap, num, {}).transferTo = to;\n    }\n  }\n  return captureMap;\n}\n\n/**\n@param {string} pattern\n@returns {Map<number, string>}\n*/\nfunction createNameMap(pattern) {\n  const re = /(?<capture>\\((?:\\?<(?![=!])(?<name>[^>]+)>|(?!\\?)))|\\\\?./gsu;\n  const map = new Map();\n  let numCharClassesOpen = 0;\n  let numCaptures = 0;\n  let match;\n  while ((match = re.exec(pattern))) {\n    const {0: m, groups: {capture, name}} = match;\n    // Relies on no unescaped literal `[` in char classes (valid in JS if not using flag v), but\n    // this library's generator never produces unescaped literal `[` even with `target` ES2018 (see\n    // `CharClassEscapeChars`)\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n      if (capture) {\n        numCaptures++;\n        if (name) {\n          map.set(numCaptures, name);\n        }\n      }\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n  return map;\n}\n\nexport {\n  EmulatedRegExp,\n};\n", "import {transform} from './transform.js';\nimport {generate} from './generate.js';\nimport {Accuracy, getOptions, Target} from './options.js';\nimport {parse} from './parse.js';\nimport {EmulatedRegExp} from './subclass.js';\nimport {tokenize} from './tokenize.js';\nimport {atomic, possessive} from 'regex/internals';\nimport {recursion} from 'regex-recursion';\n\n// The transformation and error checking for Oniguruma's unique syntax and behavior differences\n// compared to native JS RegExp is layered into all steps of the compilation process:\n// 1. Tokenizer: Understands Oniguruma syntax, with many large and small differences from JS.\n// 2. Parser: Builds an Oniguruma AST from the tokens, with understanding of Oniguruma differences.\n// 3. Transformer: Converts the Oniguruma AST to a Regex+ AST that preserves all Oniguruma\n//    behavior. This is true even in cases of non-native-JS features that are supported by both\n//    Regex+ and Oniguruma but with subtly different behavior in each (subroutines, flag x).\n// 4. Generator: Converts the Regex+ AST to a Regex+ pattern, flags, and options.\n// 5. Compiler: Components of the Regex+ libray are used to transpile several remaining features\n//    that aren't native to JS (atomic groups, possessive quantifiers, recursion). Regex+ uses a\n//    strict superset of JS RegExp syntax, so using it allows this library to benefit from not\n//    reinventing the wheel for complex features that Regex+ already knows how to transpile to JS.\n\n/**\nReturns an Oniguruma AST generated from an Oniguruma pattern.\n@param {string} pattern Oniguruma regex pattern.\n@param {{\n  flags?: string;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n}} [options]\n@returns {import('./parse.js').OnigurumaAst}\n*/\nfunction toOnigurumaAst(pattern, options) {\n  const opts = {\n    flags: options?.flags ?? '',\n    rules: {\n      captureGroup: false,\n      singleline: false,\n      ...(options?.rules),\n    },\n  };\n  return parse(tokenize(pattern, opts.flags, opts.rules));\n}\n\n/**\n@typedef {{\n  accuracy?: keyof Accuracy;\n  avoidSubclass?: boolean;\n  flags?: string;\n  global?: boolean;\n  hasIndices?: boolean;\n  lazyCompileLength?: number;\n  rules?: {\n    allowOrphanBackrefs?: boolean;\n    asciiWordBoundaries?: boolean;\n    captureGroup?: boolean;\n    recursionLimit?: number;\n    singleline?: boolean;\n  };\n  target?: keyof Target;\n  verbose?: boolean;\n}} ToRegExpOptions\n*/\n\n/**\nAccepts an Oniguruma pattern and returns an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {RegExp | EmulatedRegExp}\n*/\nfunction toRegExp(pattern, options) {\n  const d = toRegExpDetails(pattern, options);\n  if (d.options) {\n    return new EmulatedRegExp(d.pattern, d.flags, d.options);\n  }\n  return new RegExp(d.pattern, d.flags);\n}\n\n/**\nAccepts an Oniguruma pattern and returns the details for an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options?: import('./subclass.js').EmulatedRegExpOptions;\n}}\n*/\nfunction toRegExpDetails(pattern, options) {\n  const opts = getOptions(options);\n  const tokenized = tokenize(pattern, opts.flags, {\n    captureGroup: opts.rules.captureGroup,\n    singleline: opts.rules.singleline,\n  });\n  const onigurumaAst = parse(tokenized, {\n    skipBackrefValidation: opts.rules.allowOrphanBackrefs,\n    verbose: opts.verbose,\n  });\n  const regexAst = transform(onigurumaAst, {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.rules.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    bestEffortTarget: opts.target,\n  });\n  const generated = generate(regexAst, opts);\n  const recursionResult = recursion(generated.pattern, {\n    captureTransfers: generated._captureTransfers,\n    hiddenCaptures: generated._hiddenCaptures,\n    mode: 'external',\n  });\n  const possessiveResult = possessive(recursionResult.pattern);\n  const atomicResult = atomic(possessiveResult.pattern, {\n    captureTransfers: recursionResult.captureTransfers,\n    hiddenCaptures: recursionResult.hiddenCaptures,\n  });\n  const details = {\n    pattern: atomicResult.pattern,\n    flags: `${opts.hasIndices ? 'd' : ''}${opts.global ? 'g' : ''}${generated.flags}${generated.options.disable.v ? 'u' : 'v'}`,\n  };\n  if (opts.avoidSubclass) {\n    if (opts.lazyCompileLength !== Infinity) {\n      throw new Error('Lazy compilation requires subclass');\n    }\n  } else {\n    // Sort isn't required; only for readability when serialized\n    const hiddenCaptures = atomicResult.hiddenCaptures.sort((a, b) => a - b);\n    // Change the map to the `EmulatedRegExp` format, serializable as JSON\n    const transfers = Array.from(atomicResult.captureTransfers);\n    const strategy = regexAst._strategy;\n    const lazyCompile = details.pattern.length >= opts.lazyCompileLength;\n    if (hiddenCaptures.length || transfers.length || strategy || lazyCompile) {\n      details.options = {\n        ...(hiddenCaptures.length && {hiddenCaptures}),\n        ...(transfers.length && {transfers}),\n        ...(strategy && {strategy}),\n        ...(lazyCompile && {lazyCompile}),\n      };\n    }\n  }\n  return details;\n}\n\n// // Returns a Regex+ AST generated from an Oniguruma pattern\n// function toRegexAst(pattern, options) {\n//   return transform(toOnigurumaAst(pattern, options));\n// }\n\nexport {\n  EmulatedRegExp,\n  toOnigurumaAst,\n  toRegExp,\n  toRegExpDetails,\n  // toRegexAst,\n};\n"], "mappings": ";AAEA,IAAM,KAAK,OAAO;AAClB,IAAM,IAAI,OAAO;AAEjB,IAAM,WAAW;AAAA,EACf,aAAa,MAAM;AACjB,QAAI;AACF,UAAI,OAAO,OAAO;AAAA,IACpB,QAAQ;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG;AAAA,EACH,cAAc,MAAM;AAClB,QAAI;AACF,UAAI,OAAO,IAAI,GAAG;AAAA,IACpB,QAAQ;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG;AACL;AAEA,SAAS,wCAAwC,MAAM;AACrD,MAAI,CAAC,SAAS,aAAa;AACzB,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,OAAO,YAAY,GAAG;AAAA,EAC5B,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAAG;AAEH,SAAS,mBAAmB,SAAS,EAAC,QAAQ,QAAO,GAAG;AACtD,SAAO;AAAA,IACL,QAAQ,CAAC,SAAS,UAAU,CAAC,EAAE,QAAQ,UAAU,QAAQ;AAAA,IACzD,YAAY,CAAC,SAAS,cAAc,CAAC,EAAE,QAAQ,cAAc,QAAQ;AAAA,EACvE;AACF;AAEA,SAAS,YAAY,KAAK,KAAK,cAAc;AAC3C,MAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB,QAAI,IAAI,KAAK,YAAY;AAAA,EAC3B;AACA,SAAO,IAAI,IAAI,GAAG;AACpB;AAOA,SAAS,YAAY,QAAQ,KAAK;AAChC,SAAO,UAAU,MAAM,KAAK,UAAU,GAAG;AAC3C;AAEA,SAAS,WAAW,OAAO,KAAK;AAC9B,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,OAAO,gBAAgB;AAAA,EACzC;AACA,SAAO;AACT;;;ACzDA,IAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAEA,IAAM;AAAA;AAAA,EAA+B;AAAA,IACnC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA;AAOA,SAAS,WAAW,SAAS;AAC3B,MAAI,SAAS,WAAW,UAAa,CAAC,OAAO,QAAQ,MAAM,GAAG;AAC5D,UAAM,IAAI,MAAM,sBAAsB,QAAQ,MAAM,GAAG;AAAA,EACzD;AAEA,QAAM,OAAO;AAAA;AAAA,IAEX,UAAU;AAAA;AAAA;AAAA,IAGV,eAAe;AAAA;AAAA;AAAA,IAGf,OAAO;AAAA;AAAA,IAEP,QAAQ;AAAA;AAAA,IAER,YAAY;AAAA;AAAA,IAEZ,mBAAmB;AAAA;AAAA;AAAA;AAAA,IAInB,QAAQ;AAAA;AAAA,IAER,SAAS;AAAA,IACT,GAAG;AAAA;AAAA,IAEH,OAAO;AAAA;AAAA,MAEL,qBAAqB;AAAA;AAAA,MAErB,qBAAqB;AAAA;AAAA;AAAA;AAAA,MAIrB,cAAc;AAAA;AAAA,MAEd,gBAAgB;AAAA;AAAA;AAAA,MAGhB,YAAY;AAAA,MACZ,GAAI,SAAS;AAAA,IACf;AAAA,EACF;AACA,MAAI,KAAK,WAAW,QAAQ;AAC1B,SAAK,SAAS,SAAS,aAAa,WAAY,SAAS,cAAc,WAAW;AAAA,EACpF;AACA,SAAO;AACT;;;ACxEA,IAAM,kCAAkC,oBAAI,IAAI;AAAA,EAC9C,GAAG,GAAK;AAAA;AAAA,EACR,GAAG,GAAK;AAAA;AACV,CAAC;AAED,SAAS,wBAAwB,MAAM;AAErC,MAAI,gCAAgC,IAAI,IAAI,GAAG;AAC7C,WAAO,CAAC,IAAI;AAAA,EACd;AACA,QAAM,MAAM,oBAAI,IAAI;AACpB,QAAM,QAAQ,KAAK,YAAY;AAE/B,QAAM,QAAQ,MAAM,YAAY;AAChC,QAAM,QAAQ,oBAAoB,IAAI,KAAK;AAC3C,QAAM,WAAW,+BAA+B,IAAI,KAAK;AACzD,QAAM,WAAW,+BAA+B,IAAI,KAAK;AAIzD,MAAI,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG;AAC3B,QAAI,IAAI,KAAK;AAAA,EACf;AACA,cAAY,IAAI,IAAI,QAAQ;AAC5B,WAAS,IAAI,IAAI,KAAK;AAEtB,MAAI,IAAI,KAAK;AACb,cAAY,IAAI,IAAI,QAAQ;AAC5B,SAAO,CAAC,GAAG,GAAG;AAChB;AAeA,IAAM,sBAAsB,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBA0FgB,MAAM,IAAI;AAC1B;AAEA,IAAM,yBAAyB,oBAAI,IAAI;AACvC,WAAW,KAAK,qBAAqB;AACnC,yBAAuB,IAAI,KAAK,CAAC,GAAG,CAAC;AACvC;AAEA,IAAM,+BAA+B,oBAAI,IAAI;AAAA;AAAA,EAE3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,kCAAkC,oBAAI,IAAI;AAChD,WAAW,KAAK,8BAA8B;AAC5C,kCAAgC,IAAI,KAAK,CAAC,GAAG,CAAC;AAChD;AAEA,IAAM,iCAAiC,oBAAI,IAAI;AAAA,EAC7C,CAAC,KAAK,GAAG,GAAK,CAAC;AAAA;AAAA,EACf,CAAC,GAAG,GAAK,GAAG,GAAG;AAAA;AACjB,CAAC;AAED,IAAM,iCAAiC,oBAAI,IAAI;AAAA,EAC7C,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAK,GAAG,GAAG,IAAM,CAAC;AAAA;AACxB,CAAC;AAGD,IAAM,sBAAsB,IAAI,IAAI;AAAA,EAClC,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,WAAW,IAAM;AAAA,EACjB,WAAW,IAAM;AAAA,EACjB,WAAW,IAAM;AACnB,CAAC;AAQD,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B,CAAC,SAAS,oBAAoB;AAAA,EAC9B,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,aAAa;AAAA,EACvB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,SAAS;AAAA,EACnB,CAAC,SAAS,yCAAyC;AAAA,EACnD,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,iDAAiD;AAAA,EAC3D,CAAC,SAAS,eAAe;AAAA;AAAA,EACzB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,QAAQ,+BAA+B;AAAA,EACxC,CAAC,UAAU,WAAW;AACxB,CAAC;AAKD,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWF,CAAC;AAED,SAAS,MAAM,OAAO,KAAK;AAGzB,QAAMA,SAAQ,CAAC;AACf,WAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AACjC,IAAAA,OAAM,KAAK,CAAC;AAAA,EACd;AACA,SAAOA;AACT;AAGA,SAAS,KAAK,MAAM;AAClB,SAAO,KAAK,QAAQ,WAAW,EAAE,EAAE,YAAY;AACjD;AAEA,SAAS,WAAW,WAAW;AAC7B,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,CAAC,KAAK,YAAY,GAAG,IAAI;AAClC;AAEA,SAAS,WAAW,OAAO,KAAK;AAC9B,SAAO,MAAM,OAAO,GAAG,EAAE,IAAI,eAAa,WAAW,SAAS,CAAC;AACjE;AAEA,IAAM,oCAAoC,oBAAI,IAAI;AAAA,EAChD;AAAA,EAAS;AAAA,EACT;AAAA,EAAS;AAAA,EACT;AAAA,EAAM;AAAA,EACN;AAAA,EAAM;AAAA,EACN;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAKR,CAAC;;;ACvQD,IAAM;AAAA;AAAA,EAAmC;AAAA,IACvC,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAEZ,4BAA4B;AAAA;AAAA,IAE5B,eAAe;AAAA,EACjB;AAAA;AAEA,IAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,aAAa;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM;AACR;AAEA,IAAM,sBAAsB;AAAA,EAC1B,OAAO;AAAA,EACP,MAAM;AACR;AAEA,IAAM,kBAAkB;AAAA,EACtB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AACd;AAEA,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B,CAAC,KAAM,CAAC;AAAA;AAAA,EACR,CAAC,KAAM,CAAC;AAAA;AAAA,EACR,CAAC,KAAK,EAAE;AAAA;AAAA,EACR,CAAC,KAAK,EAAE;AAAA;AAAA,EACR,CAAC,KAAK,EAAE;AAAA;AAAA,EACR,CAAC,KAAK,EAAE;AAAA;AAAA,EACR,CAAC,KAAM,CAAC;AAAA;AAAA,EACR,CAAC,KAAK,EAAE;AAAA;AACV,CAAC;AAED,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAE3B,iBACF;AAAA;AAGE,8CACF;AAEE,gDACF;AAEE,oDACF;AAEE,eACF;AAEE,UACF;AAGA,IAAM,eAAe;AACrB,IAAM,UAAU,IAAI,OAAO;AAAA;AAAA,MAErB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBpB,aAAa,MAAM;AAAA,MACnB,oBAAoB;AAAA;AAAA,EAExB,QAAQ,QAAQ,EAAE,GAAG,KAAK;AAC5B,IAAM,mBAAmB,IAAI,OAAO;AAAA;AAAA,MAE9B,oBAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,oBAAoB;AAAA;AAAA;AAAA,EAGxB,QAAQ,QAAQ,EAAE,GAAG,KAAK;AA8B5B,SAAS,SAAS,SAAS,QAAQ,IAAI,OAAO;AAC5C,UAAQ;AAAA;AAAA,IAEN,cAAc;AAAA;AAAA,IAEd,YAAY;AAAA,IACZ,GAAG;AAAA,EACL;AACA,MAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,MAAI,CAAC,cAAc,KAAK,KAAK,GAAG;AAC9B,UAAM,IAAI,MAAM,UAAU,KAAK,8BAA8B;AAAA,EAC/D;AACA,QAAM,WAAW,MAAM,SAAS,GAAG;AACnC,QAAM,SAAS,CAAC,QAAQ;AACxB,QAAM,UAAU;AAAA,IACd,cAAc,MAAM;AAAA,IACpB,gBAAgB,MAAM,OAAO,GAAG,EAAE;AAAA,IAClC,eAAe;AAAA,IACf,UAAU;AAAC,aAAO,IAAI;AAAA,IAAC;AAAA,IACvB,SAAS,OAAO;AAAC,aAAO,KAAK,KAAK;AAAA,IAAC;AAAA,IACnC,mBAAmB,OAAO;AAAC,aAAO,OAAO,SAAS,CAAC,IAAI;AAAA,IAAK;AAAA,IAC5D,YAAY,MAAM;AAAA,EACpB;AACA,MAAI,SAAS,CAAC;AACd,MAAI;AACJ,UAAQ,YAAY;AACpB,SAAQ,QAAQ,QAAQ,KAAK,OAAO,GAAI;AACtC,UAAM,SAAS,oBAAoB,SAAS,SAAS,MAAM,CAAC,GAAG,QAAQ,SAAS;AAChF,QAAI,OAAO,QAAQ;AACjB,aAAO,KAAK,GAAG,OAAO,MAAM;AAAA,IAC9B,WAAW,OAAO,OAAO;AACvB,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,QAAW;AAClC,cAAQ,YAAY,OAAO;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,gCAAgC,CAAC;AACvC,MAAI,kCAAkC;AACtC,SAAO,QAAQ,OAAK;AAClB,QAAI,EAAE,SAAS,WAAW,WAAW;AACnC,UAAI,EAAE,SAAS,gBAAgB,WAAW;AACxC,UAAE,SAAS,EAAE;AAAA,MACf,WAAW,EAAE,QAAQ,KAAK;AACxB,sCAA8B,KAAK,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,CAAC,iCAAiC;AACpC,kCAA8B,QAAQ,CAAC,GAAG,MAAM;AAC9C,QAAE,OAAO,gBAAgB;AACzB,QAAE,SAAS,IAAI;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,mCAAmC,8BAA8B;AAErF,WAAS,OAAO;AAAA,IACd,OAAK,EAAE,SAAS,WAAW,gBAAgB,qBAAqB,GAAG,WAAW,IAAI;AAAA,EACpF,EAAE,KAAK;AAEP,SAAO;AAAA,IACL;AAAA,IACA,OAAO;AAAA,MACL,YAAY,MAAM,SAAS,GAAG;AAAA;AAAA;AAAA,MAG9B,QAAQ,MAAM,SAAS,GAAG;AAAA;AAAA,MAE1B;AAAA;AAAA,MAEA,cAAc,MAAM,SAAS,GAAG;AAAA,MAChC,cAAc,MAAM,SAAS,GAAG;AAAA,MAChC,aAAa,MAAM,SAAS,GAAG;AAAA,IACjC;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,SAAS,SAAS,GAAG,WAAW;AAC3D,QAAM,CAAC,IAAI,EAAE,IAAI;AAEjB,MAAI,OAAO,KAAK;AACd,UAAM,SAAS,yBAAyB,SAAS,GAAG,SAAS;AAC7D,WAAO;AAAA;AAAA,MAEL,QAAQ,OAAO;AAAA;AAAA,MAEf,WAAW,OAAO;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,OAAO,MAAM;AACf,QAAI,SAAS,SAAS,EAAE,GAAG;AACzB,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,UAC1C,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,WAAW,KAAK,CAAC,GAAG;AACtB,UAAI,CAAC,2BAA2B,KAAK,CAAC,GAAG;AACvC,cAAM,IAAI,MAAM,uBAAuB,CAAC,GAAG;AAAA,MAC7C;AACA,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,YAAY,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,QAAI,WAAW,KAAK,CAAC,GAAG;AACtB,UAAI,CAAC,2BAA2B,KAAK,CAAC,GAAG;AACvC,cAAM,IAAI,MAAM,uBAAuB,CAAC,GAAG;AAAA,MAC7C;AACA,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,eAAe,CAAC;AAAA,MAChD;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AACd,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,UAC1C,MAAM,oBAAoB;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AACd,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,cAAc,GAAG;AAAA,UAC7C,MAAM,uBAAuB;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AACd,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,cAAc,GAAG;AAAA,UAC7C,MAAM,uBAAuB;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,KAAK,SAAS,EAAE,GAAG;AACrB,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,4BAA4B,GAAG;AAAA,UAC3D,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,EAAE,GAAG;AACrB,YAAM,IAAI,MAAM,kCAAkC,CAAC,GAAG;AAAA,IACxD;AAEA,UAAM,SAAS,2BAA2B,GAAG,EAAC,aAAa,MAAK,CAAC;AACjE,WAAO,MAAM,QAAQ,MAAM,IAAI,EAAC,QAAQ,OAAM,IAAI,EAAC,OAAO,OAAM;AAAA,EAClE;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,MAAM,MAAM;AACd,YAAM,IAAI,MAAM,8BAA8B,CAAC,GAAG;AAAA,IACpD;AACA,QAAI,MAAM,OAAO;AACf,YAAM,IAAI,MAAM,wBAAwB,CAAC,GAAG;AAAA,IAC9C;AAEA,QAAI,MAAM,OAAO;AAEf,UAAI,QAAQ,SAAS,MAAM,KAAK;AAC9B,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,aAAO;AAAA,QACL,WAAW,YAAY;AAAA,MACzB;AAAA,IACF;AAEA,QAAI,oBAAoB,KAAK,CAAC,GAAG;AAC/B,aAAO;AAAA,QACL,OAAO,sBAAsB,GAAG,OAAO;AAAA,MACzC;AAAA,IACF;AAEA,YAAQ,SAAS,QAAQ,eAAe,CAAC;AACzC,YAAQ;AACR;AAAA;AAAA;AAAA,MAGG,MAAM,OAAO,CAAC,QAAQ;AAAA,MAEvB,MAAM;AAAA,MACN;AACA,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA;AAAA,UAE1C,MAAM,gBAAgB;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,MAAM,OAAO;AACf,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,UAC1C,MAAM,gBAAgB;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,MAAM,SAAS,MAAM,UAAU,MAAM,QAAQ;AAC9D,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,UAC1C,MAAM,EAAE,CAAC,MAAM,MAAM,gBAAgB,aAAa,gBAAgB;AAAA,UAClE,QAAQ,EAAE,SAAS,GAAG;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAGA,QACG,MAAM,OAAO,QAAQ,gBACrB,EAAE,WAAW,KAAK,KAAK,EAAE,SAAS,GAAG,KACrC,EAAE,WAAW,KAAK,KAAK,EAAE,SAAS,GAAG,GACtC;AACA,YAAM,QAAQ,YAAY,WAAW,WAAW,GAAG;AAAA,QACjD,MAAM,gBAAgB;AAAA;AAAA,MAExB,CAAC;AACD,UAAI,MAAM,KAAK;AACb,cAAM,OAAO,EAAE,MAAM,GAAG,EAAE;AAAA,MAC5B;AACA,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,WAAW,KAAK,GAAG;AACvB,UAAI,MAAM,QAAQ;AAChB,cAAM,IAAI,MAAM,qCAAqC,CAAC,GAAG;AAAA,MAC3D;AACA,aAAO;AAAA,QACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,UAC1C,MAAM,gBAAgB;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,MAAM,OAAO;AAEf,YAAM,IAAI,MAAM,4BAA4B,CAAC,GAAG;AAAA,IAClD;AACA,UAAM,IAAI,MAAM,wCAAwC,CAAC,GAAG;AAAA,EAC9D;AACA,MAAI,MAAM,KAAK;AACb,YAAQ,QAAQ;AAChB,YAAQ;AACR,QAAI,QAAQ,gBAAgB,GAAG;AAC7B,YAAM,IAAI,MAAM,eAAe;AAAA,IACjC;AACA,WAAO;AAAA,MACL,OAAO,YAAY,WAAW,YAAY,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,MAAI,MAAM,OAAO,QAAQ,eAAe,GAAG;AAEzC,UAAM,MAAM,QAAQ,QAAQ,MAAM,SAAS;AAC3C,WAAO;AAAA;AAAA,MAEL,WAAW,QAAQ,KAAK,QAAQ,SAAS;AAAA,IAC3C;AAAA,EACF;AACA,MAAI,OAAO,KAAK,CAAC,KAAK,QAAQ,eAAe,GAAG;AAC9C,UAAM,KAAK;AACX,OAAG,YAAY;AACf,UAAM,OAAO,GAAG,KAAK,OAAO;AAC5B,WAAO;AAAA;AAAA,MAEL,WAAW,OAAO,GAAG,YAAY;AAAA,IACnC;AAAA,EACF;AAEA,MAAI,MAAM,KAAK;AACb,WAAO;AAAA,MACL,OAAO,YAAY,WAAW,cAAc,GAAG;AAAA,QAC7C,MAAM,uBAAuB;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,UAAM,OAAO,QAAQ,aAAa;AAAA,MAChC,KAAK;AAAA,MACL,KAAK;AAAA,IACP,EAAE,CAAC,IAAI;AACP,WAAO;AAAA,MACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,MAAM,KAAK;AACb,WAAO;AAAA,MACL,OAAO,YAAY,WAAW,YAAY,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,MAAI,aAAa,KAAK,CAAC,GAAG;AACxB,WAAO;AAAA,MACL,OAAO,yBAAyB,CAAC;AAAA,IACnC;AAAA,EACF;AAEA,wBAAsB,CAAC;AACvB,SAAO;AAAA,IACL,OAAO,YAAY,WAAW,WAAW,GAAG;AAAA,MAC1C,OAAO,EAAE,YAAY,CAAC;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAEA,SAAS,yBAAyB,SAAS,QAAQ,WAAW;AAC5D,QAAM,SAAS,CAAC,YAAY,WAAW,oBAAoB,QAAQ;AAAA,IACjE,QAAQ,OAAO,CAAC,MAAM;AAAA,EACxB,CAAC,CAAC;AACF,MAAI,qBAAqB;AACzB,MAAI;AACJ,mBAAiB,YAAY;AAC7B,SAAQ,QAAQ,iBAAiB,KAAK,OAAO,GAAI;AAC/C,UAAM,IAAI,MAAM,CAAC;AAGjB,QAAI,EAAE,CAAC,MAAM,OAAO,EAAE,CAAC,MAAM,KAAK;AAChC;AACA,aAAO,KAAK,YAAY,WAAW,oBAAoB,GAAG;AAAA,QACxD,QAAQ,EAAE,CAAC,MAAM;AAAA,MACnB,CAAC,CAAC;AAAA,IACJ,WAAW,MAAM,KAAK;AACpB,UAAI,OAAO,GAAG,EAAE,EAAE,SAAS,WAAW,oBAAoB;AAExD,eAAO,KAAK,YAAY,WAAW,WAAW,GAAG;AAAA,UAC/C,OAAO;AAAA,QACT,CAAC,CAAC;AAAA,MACJ,OAAO;AACL;AACA,eAAO,KAAK,YAAY,WAAW,qBAAqB,CAAC,CAAC;AAC1D,YAAI,CAAC,oBAAoB;AACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,SAAS,sCAAsC,CAAC;AACtD,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAO,KAAK,GAAG,MAAM;AAAA,MACvB,OAAO;AACL,eAAO,KAAK,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,WAAW,iBAAiB,aAAa,QAAQ;AAAA,EACnD;AACF;AAEA,SAAS,sCAAsC,KAAK;AAClD,MAAI,IAAI,CAAC,MAAM,MAAM;AAEnB,WAAO,2BAA2B,KAAK,EAAC,aAAa,KAAI,CAAC;AAAA,EAC5D;AAEA,MAAI,IAAI,CAAC,MAAM,KAAK;AAClB,UAAM,QAAQ,sCAAsC,KAAK,GAAG;AAC5D,QAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,OAAO,IAAI,GAAG;AACrD,YAAM,IAAI,MAAM,wBAAwB,GAAG,GAAG;AAAA,IAChD;AACA,WAAO,YAAY,WAAW,cAAc,KAAK;AAAA,MAC/C,MAAM,uBAAuB;AAAA,MAC7B,QAAQ,CAAC,CAAC,MAAM,OAAO;AAAA,MACvB,OAAO,MAAM,OAAO;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ,KAAK;AACf,WAAO,YAAY,WAAW,sBAAsB,GAAG;AAAA,EACzD;AACA,MAAI,QAAQ,MAAM;AAChB,WAAO,YAAY,WAAW,2BAA2B,GAAG;AAAA,EAC9D;AACA,wBAAsB,GAAG;AACzB,SAAO,YAAY,WAAW,WAAW,KAAK;AAAA,IAC5C,OAAO,IAAI,YAAY,CAAC;AAAA,EAC1B,CAAC;AACH;AAGA,SAAS,2BAA2B,KAAK,EAAC,YAAW,GAAG;AACtD,QAAM,QAAQ,IAAI,CAAC;AACnB,MAAI,UAAU,OAAO,UAAU,KAAK;AAClC,WAAO,0BAA0B,GAAG;AAAA,EACtC;AACA,MAAI,WAAW,SAAS,KAAK,GAAG;AAC9B,WAAO,iCAAiC,GAAG;AAAA,EAC7C;AACA,MAAI,IAAI,WAAW,MAAM,GAAG;AAC1B,UAAM,IAAI,MAAM,yDAAyD,GAAG,GAAG;AAAA,EACjF;AACA,MAAI,YAAY,KAAK,GAAG,GAAG;AACzB,QAAI,IAAI,WAAW,GAAG;AACpB,YAAM,IAAI,MAAM,2CAA2C,GAAG,GAAG;AAAA,IACnE;AACA,WAAO,8BAA8B,GAAG;AAAA,EAC1C;AAEA,MAAI,0BAA0B,KAAK,GAAG,GAAG;AACvC,QAAI;AACF,YAAM,QAAQ,IAAI,MAAM,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,SAAO,SAAS,KAAK,EAAE,CAAC;AACpE,YAAM,UAAU,IAAI,YAAY,SAAS;AAAA,QACvC,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC,EAAE,OAAO,IAAI,WAAW,KAAK,CAAC;AAC/B,YAAM,UAAU,IAAI,YAAY;AAChC,YAAM,SAAS,CAAC,GAAG,OAAO,EAAE,IAAI,UAAQ;AAEtC,cAAMC,OAAM,CAAC,GAAG,QAAQ,OAAO,IAAI,CAAC,EAAE,IAAI,UAAQ,MAAM,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE;AACpF,eAAO,YAAY,WAAW,WAAWA,MAAK;AAAA,UAC5C,OAAO,KAAK,YAAY,CAAC;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT,QAAQ;AACN,YAAM,IAAI,MAAM,mBAAmB,GAAG,sCAAsC;AAAA,IAC9E;AAAA,EACF;AACA,MAAI,UAAU,OAAO,UAAU,KAAK;AAClC,WAAO,YAAY,WAAW,WAAW,KAAK;AAAA,MAC5C,OAAO,wBAAwB,GAAG;AAAA,IACpC,CAAC;AAAA,EACH;AACA,MAAI,gBAAgB,IAAI,KAAK,GAAG;AAC9B,WAAO,YAAY,WAAW,WAAW,KAAK;AAAA,MAC5C,OAAO,gBAAgB,IAAI,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AAGA,MAAI,KAAK,KAAK,KAAK,GAAG;AACpB,WAAO,YAAY,WAAW,eAAe,KAAK;AAAA,MAChD;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AAEA,MAAI,UAAU,KAAK;AAEjB,UAAM,IAAI,MAAM,qBAAqB,GAAG,GAAG;AAAA,EAC7C;AAEA,MAAI,CAAC,GAAG,GAAG,EAAE,WAAW,GAAG;AACzB,WAAO,YAAY,WAAW,WAAW,KAAK;AAAA,MAC5C,OAAO,IAAI,YAAY,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,IAAI,MAAM,sBAAsB,GAAG,GAAG;AAC9C;AAQA,SAAS,YAAY,MAAM,KAAK,MAAM;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAGA,SAAS,0BAA0B,KAAK;AACtC,QAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC;AAC5C,MAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,GAAG;AAGnC,UAAM,IAAI,MAAM,kCAAkC,GAAG,GAAG;AAAA,EAC1D;AACA,SAAO,YAAY,WAAW,WAAW,KAAK;AAAA,IAC5C,OAAO,KAAK,YAAY,EAAE,YAAY,CAAC,IAAI;AAAA,EAC7C,CAAC;AACH;AAEA,SAAS,sBAAsB,KAAK,SAAS;AAE3C,MAAI,EAAC,IAAI,IAAG,IAAI,0CAA0C,KAAK,GAAG,EAAE;AACpE,UAAQ;AAER,QAAM,SAAS,QAAQ,eAAe,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,SAAS,GAAG;AACjF,QAAM,eAAe,qBAAqB,EAAE;AAC5C,QAAM,gBAAgB,qBAAqB,GAAG;AAC9C,QAAM,cAAc,CAAC;AACrB,mBAAiB,YAAY,SAAS;AACtC,oBAAkB,YAAY,UAAU;AAExC,MAAI,IAAI,SAAS,GAAG,GAAG;AAErB,YAAQ,mBAAmB,KAAK;AAEhC,WAAO,YAAY,WAAW,WAAW,KAAK;AAAA,MAC5C,MAAM,oBAAoB;AAAA,MAC1B,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI,IAAI,SAAS,GAAG,GAAG;AACrB,YAAQ,SAAS,KAAK;AACtB,YAAQ;AACR,UAAM,QAAQ,YAAY,WAAW,WAAW,KAAK;AAAA,MACnD,MAAM,gBAAgB;AAAA,IACxB,CAAC;AACD,QAAI,gBAAgB,eAAe;AACjC,YAAM,QAAQ;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,6BAA6B,GAAG,GAAG;AACrD;AAEA,SAAS,yBAAyB,KAAK;AACrC,QAAM,OAAO,CAAC;AACd,MAAI,IAAI,CAAC,MAAM,KAAK;AAClB,UAAM,EAAC,KAAK,IAAG,IAAI,kCAAkC,KAAK,GAAG,EAAE;AAC/D,UAAM,QAAQ;AACd,QAAI,CAAC,MAAM,SAAS,CAAC,MAAM,OAAO;AAChC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,SAAK,MAAM,CAAC;AACZ,SAAK,MAAM,QAAQ,SAAY,CAAC,MAAO,QAAQ,KAAK,WAAW,CAAC;AAChE,SAAK,SAAS,CAAC,IAAI,SAAS,GAAG;AAE/B,SAAK,aAAa;AAAA,EACpB,OAAO;AACL,SAAK,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI;AAChC,SAAK,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI;AAChC,SAAK,SAAS,IAAI,CAAC,MAAM;AACzB,SAAK,aAAa,IAAI,CAAC,MAAM;AAAA,EAC/B;AACA,SAAO,YAAY,WAAW,YAAY,KAAK,IAAI;AACrD;AAEA,SAAS,iCAAiC,KAAK;AAC7C,QAAM,QAAQ,IAAI,CAAC,EAAE,YAAY;AACjC,SAAO,YAAY,WAAW,cAAc,KAAK;AAAA,IAC/C,MAAM;AAAA,MACJ,KAAK,uBAAuB;AAAA,MAC5B,KAAK,uBAAuB;AAAA;AAAA,MAC5B,KAAK,uBAAuB;AAAA;AAAA,MAC5B,KAAK,uBAAuB;AAAA,IAC9B,EAAE,KAAK;AAAA,IACP,QAAQ,IAAI,CAAC,MAAM;AAAA,EACrB,CAAC;AACH;AAEA,SAAS,8BAA8B,KAAK;AAC1C,QAAM,EAAC,GAAG,KAAK,MAAK,IAAI,4CAA4C,KAAK,GAAG,EAAE;AAC9E,QAAM,SAAU,MAAM,OAAO,CAAC,OAAS,MAAM,OAAO,CAAC,CAAC;AACtD,SAAO,YAAY,WAAW,cAAc,KAAK;AAAA,IAC/C,MAAM,uBAAuB;AAAA,IAC7B;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,qBAAqB,OAAO;AAEnC,QAAM,MAAM,CAAC;AACb,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,QAAI,aAAa;AAAA,EACnB;AACA,MAAI,MAAM,SAAS,GAAG,GAAG;AAEvB,QAAI,SAAS;AAAA,EACf;AACA,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,QAAI,WAAW;AAAA,EACjB;AACA,SAAO,OAAO,KAAK,GAAG,EAAE,SAAS,MAAM;AACzC;AAIA,SAAS,wBAAwB,KAAK;AAQpC,MAAI,kEAAkE,KAAK,GAAG,GAAG;AAC/E,UAAM,IAAI,MAAM,iCAAiC,GAAG,GAAG;AAAA,EACzD;AAEA,QAAM,MAAM,IAAI,CAAC,MAAM,MACrB,8BAA8B,KAAK,GAAG,EAAE,OAAO,MAC/C,IAAI,MAAM,CAAC;AACb,QAAM,MAAM,SAAS,KAAK,EAAE;AAC5B,SAAO;AACT;AAIA,SAAS,qBAAqB,OAAO,aAAa;AAChD,QAAM,EAAC,KAAK,YAAW,IAAI;AAE3B,QAAM,QAAQ,IAAI,MAAM,CAAC;AAEzB,MACE,CAAC;AAAA,GAEE,UAAU,OAAO,MAAM,WAAW;AAAA,EAElC,MAAM,CAAC,MAAM,OAAO,CAAC,SAAS,cAEjC;AACA,WAAO,CAAC,YAAY,WAAW,eAAe,GAAG,CAAC;AAAA,EACpD;AACA,QAAM,SAAS,CAAC;AAEhB,QAAM,UAAU,MAAM,MAAM,aAAa;AACzC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,IAAI,QAAQ,CAAC;AACnB,QAAIC;AAEJ,QAAI,MAAM,KAAK,MAAM,OAAO,MAAM,KAAK;AACrC,MAAAA,SAAQ,SAAS,GAAG,CAAC;AACrB,UAAIA,SAAQ,KAAO;AAEjB,cAAM,IAAI,MAAM,8CAA8C,GAAG,GAAG;AAAA,MACtE;AAAA,IACF,OAAO;AACL,MAAAA,SAAQ,EAAE,YAAY,CAAC;AAAA,IACzB;AACA,WAAO,KAAK,YAAY,WAAW,YAAY,MAAM,IAAI,OAAO,MAAM,GAAG;AAAA,MACvE,OAAAA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,KAAK;AAClC,MAAI,CAAC,GAAG,GAAG,EAAE,WAAW,GAAG;AACzB,UAAM,IAAI,MAAM,aAAa,GAAG,6BAA6B;AAAA,EAC/D;AACF;;;AC7xBA,SAAS,aAAa,EAAC,aAAY,GAAG,OAAO;AAC3C,SACE,aAAa,WAAW,KACxB,aAAa,CAAC,EAAE,SAAS,WAAW,MACnC,CAAC,SAAS,MAAM,aAAa,CAAC,EAAE,SAAS,CAAC,CAAC;AAEhD;AAEA,SAAS,mBAAmB,EAAC,KAAI,GAAG;AAClC,SAAO,SAAS,SAAS,aAAa,SAAS,SAAS;AAC1D;AAEA,SAAS,sBAAsB,MAAM;AACnC,QAAM,QAAQ;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACA,SAAO,MAAM,SAAS,KAAK,IAAI,KAC7B,KAAK,SAAS,SAAS,cACvB,KAAK,OACL,MAAM,SAAS,KAAK,QAAQ,IAAI;AAEpC;AAQA,SAAS,mBAAmB,EAAC,KAAI,GAAG;AAClC,SAAO,SAAS,SAAS,kBAAkB,SAAS,SAAS;AAC/D;AAEA,SAAS,aAAa,EAAC,MAAM,KAAI,GAAG;AAClC,SACE,SAAS,SAAS,cACjB,SAAS,kBAAkB,aAAa,SAAS,kBAAkB;AAExE;;;ACtCA,SAAS,SAAS,MAAM,OAAO,SAAS;AACtC,MAAI,MAAM,KAAK;AACf,SAAO,IAAI,QAAQ;AACjB,UAAM,IAAI;AAAA,EACZ;AACA,WAAS,cAAc,OAAO,QAAQ;AACpC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,WAAW,aAAa,MAAM,CAAC,GAAG,QAAQ,GAAG,KAAK;AACxD,UAAI,KAAK,IAAI,IAAI,IAAI,QAAQ;AAAA,IAC/B;AAAA,EACF;AACA,WAAS,aAAa,MAAM,SAAS,MAAM,MAAM,MAAM,YAAY,MAAM;AACvE,QAAI,WAAW;AACf,QAAI,2BAA2B;AAC/B,UAAMC,QAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AACP,mBAAW,WAAW,oBAAoB,EAAE,OAAO,KAAK,IAAI,GAAG,MAAM,QAAQ,GAAG,CAAC;AACjF,oBAAY;AAAA,MACd;AAAA,MACA,wBAAwB;AACtB,eAAO,WAAW,WAAW,oBAAoB,EAAE,OAAO,MAAM,CAAC;AAAA,MACnE;AAAA,MACA,wBAAwB;AACtB,cAAM,UAAU,MAAM;AACtB,oBAAY;AACZ,eAAO,WAAW,WAAW,oBAAoB,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,MACnF;AAAA,MACA,YAAY,SAAS;AACnB,kBAAU,SAAS,MAAM;AACzB,YAAI,WAAW;AACb,oBAAU,KAAK,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI;AAAA,QAC3C,OAAO;AACL,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,MACA,OAAO;AACL,mCAA2B;AAAA,MAC7B;AAAA,IACF;AACA,UAAM,aAAa,kBAAkB,IAAI,EAAE,KAAK,CAAAC,SAAO,CAAC,CAAC,QAAQA,IAAG,CAAC;AACrE,UAAM,UAAU,cAAc,QAAQ,UAAU;AAChD,UAAM,UAAU,OAAO,YAAY,aAAa,UAAU,SAAS;AACnE,UAAM,SAAS,SAAS;AACxB,cAAUD,OAAM,KAAK;AACrB,QAAI,CAAC,0BAA0B;AAC7B,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK,SAAS;AACZ,uBAAa,KAAK,SAAS,MAAM,SAAS;AAC1C,uBAAa,KAAK,OAAO,MAAM,OAAO;AACtC;AAAA,QACF,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AACZ,wBAAc,KAAK,UAAU,IAAI;AACjC;AAAA,QACF,KAAK,SAAS;AACZ,cAAI,aAAa,IAAI,GAAG;AACtB,0BAAc,KAAK,cAAc,IAAI;AAAA,UACvC;AACA;AAAA,QACF,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AACZ;AAAA,QACF,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AACZ,wBAAc,KAAK,cAAc,IAAI;AACrC;AAAA,QACF,KAAK,SAAS;AACZ,wBAAc,KAAK,SAAS,IAAI;AAChC;AAAA,QACF,KAAK,SAAS;AACZ,uBAAa,KAAK,KAAK,MAAM,KAAK;AAClC,uBAAa,KAAK,KAAK,MAAM,KAAK;AAClC;AAAA,QACF,KAAK,SAAS;AACZ,uBAAa,KAAK,SAAS,MAAM,SAAS;AAC1C;AAAA,QACF;AACE,gBAAM,IAAI,MAAM,yBAAyB,KAAK,IAAI,GAAG;AAAA,MACzD;AAAA,IACF;AACA,aAASA,OAAM,KAAK;AACpB,WAAO;AAAA,EACT;AACA,eAAa,KAAK,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS;AAC/D;AAEA,IAAM,iBAAiB;AAAA,EACrB,UAAU;AAAA,EACV,SAAS;AACX;AAEA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,QAAQ,CAAC,eAAe,OAAO;AACrC,MAAI,mBAAmB,IAAI,KAAK,aAAa,IAAI,GAAG;AAClD,UAAM,KAAK,eAAe,QAAQ;AAAA,EACpC;AACA,QAAM,KAAK,KAAK,IAAI;AACpB,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,QAAQ;AAG/B,MAAI,YAAY,QAAQ;AACtB,SAAK,SAAS;AAAA,EAChB;AACF;;;ACrHA,IAAM,WAAW;AAAA,EACf,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,4BAA4B;AAAA;AAAA,EAE5B,WAAW;AACb;AAEA,IAAM,yBAAyB;AAAA;AAAA,EAE7B,UAAU;AACZ;AAEA,IAAM,oBAAoB;AAAA,EACxB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,eAAe;AACjB;AAGA,IAAM,uBAAuB;AAC7B,IAAM,oBAAoB;AAE1B,IAAM,qCAAqC;AAAA,EACzC,UAAU;AAAA,EACV,SAAS;AACX;AAmBA,SAAS,MAAM,EAAC,QAAQ,OAAO,MAAK,GAAG,SAAS;AAC9C,QAAM,OAAO;AAAA,IACX,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,SAAS;AAAA,IACT,GAAG;AAAA,EACL;AACA,QAAM,UAAU;AAAA,IACd,iBAAiB,CAAC;AAAA,IAClB,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,mBAAmB,oBAAI,IAAI;AAAA,IAC3B,QAAQ;AAAA,IACR,uBAAuB,KAAK;AAAA,IAC5B,0BAA0B,KAAK;AAAA,IAC/B,4BAA4B,KAAK;AAAA,IACjC,aAAa,CAAC;AAAA,IACd,OAAO;AAAA,IACP;AAAA,IACA,SAAS,KAAK;AAAA,IACd;AAAA,EACF;AACA,WAAS,KAAK,QAAQ,OAAO;AAC3B,UAAM,QAAQ,OAAO,QAAQ,OAAO;AACpC,YAAQ,SAAS;AACjB,YAAQ,QAAQ;AAEhB,YAAQ;AACR,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,WAAW;AAEd,eAAO,kBAAkB;AAAA,MAC3B,KAAK,WAAW;AACd,eAAO,yBAAyB,KAAK;AAAA,MACvC,KAAK,WAAW;AACd,eAAO,mBAAmB,OAAO;AAAA,MACnC,KAAK,WAAW;AACd,eAAO,gBAAgB,MAAM,OAAO,EAAC,cAAc,CAAC,CAAC,MAAM,mBAAkB,CAAC;AAAA,MAChF,KAAK,WAAW;AACd,eAAO,0BAA0B,SAAS,KAAK;AAAA,MACjD,KAAK,WAAW;AACd,eAAO,wBAAwB,SAAS,KAAK;AAAA,MAC/C,KAAK,WAAW;AACd,eAAO,kBAAkB,OAAO;AAAA,MAClC,KAAK,WAAW;AACd,eAAO,yBAAyB,KAAK;AAAA,MACvC,KAAK,WAAW;AACd,eAAO,eAAe,SAAS,KAAK;AAAA,MACtC,KAAK,WAAW;AACd,eAAO,gBAAgB,OAAO;AAAA,MAChC,KAAK,WAAW;AACd,eAAO,gBAAgB,OAAO;AAAA,MAChC,KAAK,WAAW;AACd,eAAO,iCAAiC,MAAM,IAAI;AAAA,MACpD;AACE,cAAM,IAAI,MAAM,0BAA0B,MAAM,IAAI,GAAG;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,MAAM,YAAY,cAAc,GAAG,YAAY,KAAK,CAAC;AAC3D,MAAI,MAAM,IAAI,QAAQ,aAAa,CAAC;AACpC,SAAO,QAAQ,UAAU,OAAO,QAAQ;AACtC,UAAM,OAAO,KAAK,KAAK,CAAC,CAAC;AACzB,QAAI,KAAK,SAAS,SAAS,aAAa;AACtC,UAAI,QAAQ,aAAa,KAAK,IAAI;AAClC,YAAM;AAAA,IACR,OAAO;AACL,UAAI,SAAS,KAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAEA,QAAM,EAAC,iBAAiB,gBAAgB,mBAAmB,YAAW,IAAI;AAE1E,MAAI,kBAAkB,kBAAkB,QAAQ,CAAC,MAAM,cAAc;AACnE,UAAM,IAAI,MAAM,kEAAkE;AAAA,EACpF;AACA,aAAW,EAAC,IAAG,KAAK,aAAa;AAC/B,QAAI,OAAO,QAAQ,UAAU;AAE3B,UAAI,MAAM,gBAAgB,QAAQ;AAChC,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAAA,IACF,WAAW,CAAC,kBAAkB,IAAI,GAAG,GAAG;AACtC,YAAM,IAAI,MAAM,wDAAwD,GAAG,IAAI;AAAA,IACjF,WAAW,kBAAkB,IAAI,GAAG,EAAE,SAAS,GAAG;AAChD,YAAM,IAAI,MAAM,+CAA+C,GAAG,IAAI;AAAA,IACxE;AAAA,EACF;AAEA,WAAS,EAAC,MAAM,IAAG,GAAG,MAAM;AAAA,IAC1B,QAAQ,EAAC,MAAM,OAAM,GAAG;AACtB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAgBA,SAAS,mBAAmB,SAAS;AACnC,QAAM,EAAC,IAAG,IAAI,QAAQ;AACtB,QAAM,cAAc,WAAW,KAAK,GAAG;AACvC,QAAM,MAAM,cAAc,IAAI,MAAM,GAAG,EAAE,IAAI,IAAI,MAAM,CAAC;AACxD,QAAM,UAAU,CAAC,KAAK,aAAa,UAAU;AAC3C,UAAM,oBAAoB,QAAQ,gBAAgB;AAClD,QAAI,SAAS;AAab,QAAI,MAAM,mBAAmB;AAI3B,UAAI,QAAQ,uBAAuB;AACjC,iBAAS;AAAA,MACX,OAAO;AACL,cAAM,IAAI,MAAM,oDAAoD,GAAG,GAAG;AAAA,MAC5E;AAAA,IACF;AACA,YAAQ,iBAAiB;AACzB,WAAO,oBAAoB,aAAa,oBAAoB,IAAI,MAAM,KAAK,EAAC,OAAM,CAAC;AAAA,EACrF;AACA,MAAI,aAAa;AACf,UAAM,cAAc,kCAAkC,KAAK,GAAG;AAC9D,QAAI,aAAa;AACf,aAAO,QAAQ,CAAC,YAAY,OAAO,KAAK,CAAC,CAAC,YAAY,OAAO,IAAI;AAAA,IACnE;AAEA,QAAI,OAAO,KAAK,GAAG,GAAG;AACpB,YAAM,IAAI,MAAM,yBAAyB,GAAG,GAAG;AAAA,IACjD;AACA,QAAI,CAAC,QAAQ,kBAAkB,IAAI,GAAG,GAAG;AACvC,YAAM,IAAI,MAAM,uCAAuC,GAAG,GAAG;AAAA,IAC/D;AACA,WAAO,oBAAoB,GAAG;AAAA,EAChC;AACA,SAAO,QAAQ,CAAC,GAAG;AACrB;AAEA,SAAS,0BAA0B,SAAS,OAAO;AACjD,QAAM,EAAC,QAAQ,QAAQ,KAAI,IAAI;AAC/B,QAAM,kBAAkB,OAAO,SAAS,GAAG,EAAE;AAC7C,QAAM,YAAY,OAAO,QAAQ,OAAO;AACxC,MACE,CAAC,MAAM,sBACP,mBACA,gBAAgB,SAAS,SAAS,kBAClC,gBAAgB,SAAS,SAAS,uBAClC,aACA,UAAU,SAAS,WAAW,sBAC9B,UAAU,SAAS,WAAW,uBAC9B,UAAU,SAAS,WAAW,2BAC9B;AACA,UAAM,WAAW,KAAK,QAAQ;AAAA,MAC5B,GAAG;AAAA,MACH,oBAAoB;AAAA,IACtB,CAAC;AACD,QAAI,gBAAgB,SAAS,SAAS,aAAa,SAAS,SAAS,SAAS,WAAW;AACvF,aAAO,SAAS,IAAI;AACpB,aAAO,0BAA0B,iBAAiB,QAAQ;AAAA,IAC5D;AACA,UAAM,IAAI,MAAM,+BAA+B;AAAA,EACjD;AAEA,SAAO,gBAAgB,EAAE;AAC3B;AAEA,SAAS,wBAAwB,SAAS,OAAO;AAC/C,QAAM,EAAC,OAAO,QAAQ,SAAS,KAAI,IAAI;AACvC,QAAM,kBAAkB,OAAO,QAAQ,OAAO;AAC9C,MAAI,OAAO,qBAAqB,EAAC,QAAQ,MAAM,OAAM,CAAC;AACtD,QAAM,eAAe,KAAK,SAAS,CAAC;AACpC,MAAI,YAAY,8BAA8B,eAAe;AAC7D,SAAO,UAAU,SAAS,WAAW,qBAAqB;AACxD,QAAI,UAAU,SAAS,WAAW,2BAA2B;AAC3D,mBAAa,QAAQ,KAAK,qBAAqB,EAAC,QAAQ,OAAO,UAAU,KAAI,CAAC,CAAC;AAE/E,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,KAAK,aAAa,QAAQ,GAAG,EAAE;AACrC,SAAG,SAAS,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,IAClC;AACA,gBAAY,8BAA8B,OAAO,QAAQ,OAAO,GAAG,eAAe;AAAA,EACpF;AACA,MAAI,CAAC,SAAS;AACZ,uCAAmC,YAAY;AAAA,EACjD;AAEA,MAAI,aAAa,QAAQ,WAAW,GAAG;AACrC,UAAM,KAAK,aAAa,QAAQ,CAAC;AAEjC,OAAG,SAAS,KAAK,WAAW,GAAG;AAC/B,WAAO;AAAA,EACT;AAEA,UAAQ;AACR,SAAO;AACT;AAEA,SAAS,kBAAkB,EAAC,OAAO,2BAA0B,GAAG;AAC9D,MAAI,EAAC,MAAM,QAAQ,MAAK,IAAI;AAC5B,MAAI,SAAS,uBAAuB,UAAU;AAC5C,UAAM,aAAa,KAAK,KAAK;AAC7B,QAAI,gBAAgB,IAAI,UAAU,GAAG;AACnC,aAAO,uBAAuB;AAC9B,cAAQ;AAAA,IACV,OAAO;AACL,aAAO,sBAAsB,OAAO;AAAA,QAClC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,SAAS,uBAAuB,OAAO;AACzC,WAAO;AAAA,MACL,MAAM,SAAS;AAAA,MACf,MAAM,qBAAqB;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,mBAAmB,MAAM,EAAC,OAAM,CAAC;AAC1C;AAEA,SAAS,eAAe,SAAS,OAAO;AACtC,QAAM,EAAC,OAAO,QAAQ,iBAAiB,mBAAmB,0BAA0B,SAAS,KAAI,IAAI;AACrG,MAAI,OAAO,kBAAkB,KAAK;AAClC,QAAM,mBAAmB,KAAK,SAAS,SAAS;AAChD,QAAM,eAAe,KAAK,SAAS,kBAAkB;AACrD,QAAM,kBAAkB,gBAAgB,KAAK;AAG7C,MAAI,KAAK,SAAS,SAAS,gBAAgB;AACzC,oBAAgB,KAAK,IAAI;AACzB,QAAI,KAAK,MAAM;AACb,kBAAY,mBAAmB,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,IACzD;AAAA,EACF;AAEA,MAAI,oBAAoB,MAAM,oBAAoB;AAEhD,UAAM,IAAI,MAAM,mDAAmD;AAAA,EACrE;AACA,MAAI,YAAY,qBAAqB,OAAO,QAAQ,OAAO,CAAC;AAC5D,SAAO,UAAU,SAAS,WAAW,YAAY;AAC/C,QAAI,UAAU,SAAS,WAAW,YAAY;AAC5C,WAAK,aAAa,KAAK,kBAAkB,CAAC;AAE1C,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,MAAM,KAAK,aAAa,GAAG,EAAE;AACnC,YAAM,QAAQ,KAAK,KAAK;AAAA,QACtB,GAAG;AAAA,QACH,oBAAoB,MAAM,sBAAsB;AAAA,QAChD,gBAAgB,MAAM,kBAAkB;AAAA,QACxC,mBAAmB,MAAM,qBAAqB;AAAA,MAChD,CAAC;AACD,UAAI,SAAS,KAAK,KAAK;AAEvB,WAAK,gBAAgB,MAAM,mBAAmB,CAAC,0BAA0B;AAIvE,cAAM,MAAM;AACZ,YAAI,mBAAmB,MAAM,mBAAmB;AAG9C,cAAI,MAAM,SAAS,kBAAkB,aAAa,MAAM,SAAS,SAAS,gBAAgB;AACxF,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB;AAAA,QACF,OAAO;AAGL,cAAI,MAAM,SAAS,kBAAkB,aAAc,MAAM,SAAS,kBAAkB,cAAc,MAAM,QAAS;AAC/G,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,gBAAY,qBAAqB,OAAO,QAAQ,OAAO,CAAC;AAAA,EAC1D;AACA,MAAI,CAAC,SAAS;AACZ,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AAEA,UAAQ;AACR,SAAO;AACT;AAEA,SAAS,gBAAgB,EAAC,OAAO,OAAM,GAAG;AACxC,QAAM,EAAC,KAAK,KAAK,QAAQ,YAAAE,YAAU,IAAI;AACvC,QAAM,iBAAiB,OAAO,SAAS,GAAG,EAAE;AAC5C,MACE,CAAC,kBACD,eAAe,SAAS,SAAS,aACjC,eAAe,SAAS,SAAS,WACjC;AACA,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AACA,QAAM,OAAO,iBAAiB,gBAAgB,KAAK,KAAK,QAAQA,WAAU;AAC1E,SAAO,SAAS,IAAI;AACpB,SAAO;AACT;AA8BA,SAAS,gBAAgB,SAAS;AAChC,QAAM,EAAC,OAAO,iBAAiB,YAAW,IAAI;AAC9C,MAAI,MAAM,MAAM,IAAI,MAAM,GAAG,EAAE;AAC/B,QAAM,cAAc,qCAAqC,KAAK,GAAG;AACjE,MAAI,aAAa;AACf,UAAM,MAAM,CAAC,YAAY,OAAO;AAChC,UAAM,oBAAoB,gBAAgB;AAC1C,YAAQ,iBAAiB;AACzB,UAAM;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK,oBAAoB;AAAA,MACzB,KAAK,oBAAoB,IAAI;AAAA,IAC/B,EAAE,YAAY,OAAO,IAAI;AACzB,QAAI,MAAM,GAAG;AACX,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAAA,EAEF,WAAW,QAAQ,KAAK;AACtB,UAAM;AAAA,EACR;AACA,QAAM,OAAO,iBAAiB,GAAG;AACjC,cAAY,KAAK,IAAI;AACrB,SAAO;AACT;AAEA,SAAS,qBAAqB,MAAM;AAClC,MAAI,SAAS,uBAAuB,UAAU;AAC5C,UAAM,IAAI,MAAM,oCAAoC,IAAI,GAAG;AAAA,EAC7D;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA,cAAc,CAAC,kBAAkB,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,oBAAoB;AAC3B,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,UAAU,CAAC;AAAA,EACb;AACF;AAEA,SAAS,gBAAgB,MAAM,SAAS;AAEtC,QAAM,SAAS,CAAC,CAAC,SAAS;AAC1B,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA,GAAI,SAAS,kBAAkB,iBAAiB,EAAC,OAAM;AAAA,EACzD;AACF;AAEA,SAAS,yBAAyB,EAAC,KAAI,GAAG;AACxC,SAAO;AAAA,IACL,WAAW;AAAA,MACT,KAAK,kBAAkB;AAAA,MACvB,KAAK,kBAAkB;AAAA,MACvB,OAAO,kBAAkB;AAAA,MACzB,OAAO,kBAAkB;AAAA,MACzB,OAAO,kBAAkB;AAAA,MACzB,OAAO,kBAAkB;AAAA,MACzB,OAAO,kBAAkB;AAAA,MACzB,OAAO,kBAAkB;AAAA,IAC3B,EAAE,IAAI,GAAG,8BAA8B,IAAI,GAAG;AAAA,IAC9C,EAAC,QAAQ,SAAS,MAAK;AAAA,EACzB;AACF;AAEA,SAAS,oBAAoB,KAAK,SAAS;AACzC,QAAM,SAAS,CAAC,CAAC,SAAS;AAC1B,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,GAAI,UAAU,EAAC,OAAM;AAAA,IACrB;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,EAAC,OAAO,MAAM,MAAM,QAAQ,OAAM,GAAG;AAC9D,UAAQ,MAAM;AAAA,IACZ,KAAK,gBAAgB;AACnB,aAAO,qBAAqB,uBAAuB,QAAQ;AAAA,IAC7D,KAAK,gBAAgB;AACnB,aAAO,YAAY,EAAC,QAAQ,KAAI,CAAC;AAAA,IACnC,KAAK,gBAAgB;AACnB,aAAO,qBAAqB,QAAQ,IAAI;AAAA,IAC1C,KAAK,gBAAgB;AACnB,aAAO,YAAY,EAAC,MAAK,CAAC;AAAA,IAC5B,KAAK,gBAAgB;AAAA,IACrB,KAAK,gBAAgB;AACnB,aAAO,iBAAiB;AAAA,QACtB,QAAQ,SAAS,gBAAgB;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AACE,YAAM,IAAI,MAAM,0BAA0B,IAAI,GAAG;AAAA,EACrD;AACF;AAEA,SAAS,qBAAqB,QAAQ,MAAM;AAC1C,QAAM,UAAU,SAAS;AACzB,MAAI,WAAW,CAAC,0BAA0B,IAAI,GAAG;AAC/C,UAAM,IAAI,MAAM,eAAe,IAAI,wBAAwB;AAAA,EAC7D;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA,GAAI,WAAW,EAAC,KAAI;AAAA,IACpB,cAAc,CAAC,kBAAkB,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,gBAAgB,UAAU,SAAS;AAC1C,QAAM,OAAO;AAAA,IACX,cAAc;AAAA,IACd,GAAG;AAAA,EACL;AACA,MAAI,WAAW,SAAU;AACvB,UAAM,MAAM,SAAS,SAAS,EAAE;AAChC,QAAI,KAAK,cAAc;AACrB,iBAAW;AAAA,IACb,WAAW,WAAW,SAAU;AAC9B,YAAM,IAAI,MAAM,wCAAwC,GAAG,IAAI;AAAA,IACjE,OAAO;AACL,YAAM,IAAI,MAAM,8CAA8C,GAAG,IAAI;AAAA,IACvE;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,OAAO;AAAA,EACT;AACF;AAEA,SAAS,qBAAqB,SAAS;AACrC,QAAM,OAAO;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,QAAQ,KAAK;AAAA,IACb,UAAU,KAAK,WAAW,CAAC,IAAI,CAAC,iCAAiC,CAAC;AAAA,EACpE;AACF;AAEA,SAAS,mCAAmC;AAC1C,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,SAAS,CAAC,qBAAqB,EAAC,QAAQ,OAAO,UAAU,KAAI,CAAC,CAAC;AAAA,EACjE;AACF;AAEA,SAAS,0BAA0B,KAAK,KAAK;AAC3C,MAAI,IAAI,QAAQ,IAAI,OAAO;AACzB,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,MAAM,EAAC,OAAM,GAAG;AAC1C,QAAM,OAAO;AAAA,IACX,MAAM,SAAS;AAAA,IACf,MAAM,WAAW,qBAAqB,IAAI,GAAG,kCAAkC,IAAI,GAAG;AAAA,EACxF;AACA,MACE,SAAS,uBAAuB,SAChC,SAAS,uBAAuB,OAChC,SAAS,uBAAuB,SAChC,SAAS,uBAAuB,MAChC;AACA,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,EAAC,MAAM,MAAK,GAAG;AAC/C,QAAM,OAAO;AAAA,IACX,MAAM,SAAS;AAAA,IACf,MAAM,WAAW,kBAAkB,IAAI,GAAG,8BAA8B,IAAI,GAAG;AAAA,EACjF;AAIA,MAAI,SAAS,oBAAoB,OAAO;AACtC,SAAK,QAAQ;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,YAAY,EAAC,YAAY,QAAQ,UAAU,cAAc,cAAc,YAAW,GAAG;AAC5F,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,YAAY,SAAS;AAC5B,QAAMC,UAAS,SAAS;AACxB,QAAM,QAAQ,SAAS;AACvB,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,GAAIA,WAAU,EAAC,QAAAA,QAAM;AAAA,IACrB,GAAI,SAAS,EAAC,MAAK;AAAA,IACnB,cAAc,CAAC,kBAAkB,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,iBAAiB,SAAS;AACjC,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,MAAM,KAAK,SAAS,kBAAkB,aAAa,kBAAkB;AAAA,IACrE,QAAQ,KAAK;AAAA,IACb,cAAc,CAAC,kBAAkB,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,gBAAgB;AACvB,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,cAAc,CAAC,kBAAkB,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,iBAAiB,SAAS,KAAK,KAAK,SAAS,MAAMD,cAAa,OAAO;AAC9E,QAAM,OAAO;AAAA,IACX,MAAM,SAAS;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAAA;AAAA,IACA;AAAA,EACF;AACA,MAAI,MAAM,KAAK;AACb,WAAO;AAAA,MACL,GAAG;AAAA,MACH,KAAK;AAAA,MACL,KAAK;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,SAAS,OAAO;AACnC,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,KAAK;AAC7B,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,OAAO,SAAS;AAC7C,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR,4BAA4B;AAAA,IAC5B,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,MAAM,qBAAqB;AAAA,IAC3B,OAAO,KAAK,6BAA6B,QAAQ,yBAAyB,KAAK;AAAA,IAC/E,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,iCAAiC,MAAM;AAC9C,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf,MAAM,WAAW;AAAA,MACf,OAAO,mCAAmC;AAAA,MAC1C,OAAO,mCAAmC;AAAA,IAC5C,EAAE,IAAI,GAAG,+BAA+B,IAAI,GAAG;AAAA,EACjD;AACF;AAIA,SAAS,yBAAyB,OAAO;AACvC,QAAM,UAAU,KAAK,KAAK;AAC1B,MAAI,gCAAgC,IAAI,OAAO,GAAG;AAEhD,UAAM,IAAI,MAAM,yBAAyB,KAAK,6BAA6B;AAAA,EAC7E;AACA,QAAM,SAAS,uBAAuB,IAAI,OAAO;AACjD,MAAI,QAAQ;AACV,WAAO;AAAA,EACT;AAIA,SAAO,MACL,KAAK,EACL,QAAQ,WAAW,GAAG,EACtB,QAAQ,yBAAyB,KAAK,EACtC,QAAQ,cAAc,OAAK,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC;AAC5E;AAGA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,kBAAkB,KAAK,aAAa,CAAC,EAAE,SAAS,CAAC;AACvD,MACE,KAAK,SAAS,SAAS,SACvB,aAAa,MAAM,SAAO,IAAI,SAAS,SAAS,KAAK,KACrD,EAAE,KAAK,UAAU,gBAAgB,UACjC,EAAE,KAAK,UAAU,gBAAgB,UAAU,gBAAgB,SAC3D;AACA,QAAI,KAAK,QAAQ;AACf,sBAAgB,SAAS;AAAA,IAC3B,WAAW,KAAK,OAAO;AACrB,sBAAgB,QAAQ,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,0BAA0B,MAAM;AAIvC,SAAO,4BAA4B,KAAK,IAAI;AAC9C;AAGA,SAAS,mCAAmC,cAAc;AACxD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,QAAQ,KAAK;AACpD,UAAM,KAAK,aAAa,QAAQ,CAAC;AACjC,UAAM,aAAa,GAAG,SAAS,CAAC;AAChC,QAAI,GAAG,SAAS,WAAW,KAAK,WAAW,SAAS,SAAS,gBAAgB;AAC3E,mBAAa,QAAQ,CAAC,IAAI;AAC1B,iBAAW,SAAS,GAAG,WAAW,WAAW;AAAA,IAC/C;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,OAAO,iBAAiB;AAC7D,SAAO;AAAA,IACL;AAAA;AAAA,IAEA,GAAG,iBAAiB,UAAU,KAAK,UAAU,UAAU;AAAA,EACzD;AACF;AAEA,SAAS,qBAAqB,OAAO;AACnC,SAAO,WAAW,OAAO,gBAAgB;AAC3C;;;ACjxBA,OAAO,gBAAgB;AA+BvB,SAAS,UAAU,KAAK,SAAS;AAC/B,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOX,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,GAAG;AAAA,EACL;AACA,QAAM,iBAAiB;AAAA,IACrB,UAAU,KAAK;AAAA,IACf,qBAAqB,KAAK;AAAA,IAC1B,eAAe,KAAK;AAAA,IACpB,qBAAqB,oBAAI,IAAI;AAAA,IAC7B,gBAAgB,oBAAI,IAAI;AAAA,IACxB,iBAAiB,YAAY,KAAK,kBAAkB,QAAQ;AAAA,IAC5D,kBAAkB;AAAA,IAClB,UAAU;AAAA;AAAA,IAEV,kBAAkB,oBAAI,IAAI;AAAA,IAC1B,iBAAiB,oBAAI,IAAI;AAAA,IACzB,cAAc,IAAI,MAAM;AAAA,IACxB,cAAc,IAAI,MAAM;AAAA,IACxB,aAAa,IAAI,MAAM;AAAA,EACzB;AACA,WAAS,EAAC,MAAM,IAAG,GAAG,gBAAgB,gBAAgB;AAEtD,QAAM,cAAc;AAAA,IAClB,QAAQ,IAAI,MAAM;AAAA,IAClB,YAAY,IAAI,MAAM;AAAA,EACxB;AAMA,QAAM,kBAAkB;AAAA,IACtB,cAAc;AAAA,IACd,WAAW;AAAA,IACX;AAAA,IACA,mBAAmB,oBAAI,IAAI;AAAA,IAC3B,cAAc,oBAAI,IAAI;AAAA,IACtB,8BAA8B,oBAAI,IAAI;AAAA,IACtC,UAAU,oBAAI,IAAI;AAAA,IAClB,yBAAyB,oBAAI,IAAI;AAAA,IACjC,kBAAkB,eAAe;AAAA,EACnC;AACA,WAAS,EAAC,MAAM,IAAG,GAAG,iBAAiB,iBAAiB;AACxD,QAAM,iBAAiB;AAAA,IACrB,cAAc,gBAAgB;AAAA,IAC9B,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,yBAAyB,gBAAgB;AAAA,EAC3C;AACA,WAAS,EAAC,MAAM,IAAG,GAAG,gBAAgB,gBAAgB;AACtD,MAAI,aAAa,gBAAgB;AACjC,MAAI,YAAY,eAAe;AAC/B,SAAO;AACT;AAEA,IAAM,mBAAmB;AAAA,EACvB,eAAe,EAAC,MAAM,YAAW,GAAG;AAElC,UAAM,QAAQ,cAAc,YAAY,GAAG;AAAA,MACzC,iBAAiB,iBAAiB,EAAC,QAAQ,KAAI,CAAC,GAAG,KAAK,YAAY;AAAA,MACpE,sBAAsB,KAAK;AAAA,IAC7B,CAAC;AACD,UAAM,aAAa,iBAAiB,OAAO,GAAG,QAAQ;AACtD,UAAM,SAAS;AACf,gBAAY,cAAc,YAAY,GAAG,CAAC,UAAU,CAAC,CAAC;AAAA,EACxD;AAAA,EAEA,aAAa;AAAA,IACX,MAAM,EAAC,MAAM,QAAQ,IAAG,GAAG,EAAC,oBAAmB,GAAG;AAGhD,YAAM,iBAAiB,KAAK,SAAS,OAAO,QAAM,GAAG,SAAS,kBAAkB,KAAK;AACrF,eAAS,IAAI,MAAM,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACzD,cAAM,oBAAoB,OAAO,aAAa,CAAC;AAC/C,oBAAY,qBAAqB,mBAAmB,CAAC,CAAC,EAAE,KAAK,GAAG,cAAc;AAAA,MAChF;AAAA,IACF;AAAA,IACA,KAAK,EAAC,KAAI,GAAG,EAAC,oBAAmB,GAAG;AAIlC,UAAI,oBAAoB,IAAI,IAAI,GAAG,QAAQ;AACzC,cAAM,QAAQ,iCAAiC,oBAAoB,IAAI,IAAI,CAAC;AAC5E,YAAI,OAAO;AACT,gBAAM,YAAY,cAAc,YAAY,EAAC,MAAK,CAAC,GAAG,KAAK,QAAQ;AAEnE,oBAAU,SAAS;AACnB,eAAK,WAAW,CAAC,SAAS;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,EAAC,MAAM,KAAK,WAAW,KAAK,QAAQ,YAAW,GAAG,OAAO;AACjE,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,EAAC,qBAAqB,eAAe,iBAAiB,YAAW,IAAI;AAC3E,QAAI,SAAS,kBAAkB,UAAU;AAEvC,kBAAY,cAAc,YAAY,CAAC;AAAA,IACzC,WAAW,SAAS,kBAAkB,YAAY;AAGhD,kBAAY,cAAc,qBAAqB,EAAC,0BAA0B,KAAI,CAAC,CAAC;AAAA,IAClF,WAAW,SAAS,kBAAkB,YAAY;AAChD,YAAM,mBAAmB;AAAA,IAC3B,WAAW,SAAS,kBAAkB,cAAc;AAClD,UAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,YAAI,MAAM,SAAS;AACnB,eAAO;AAAA,MACT,OAAO;AACL,cAAM,OAAO,UAAU,MAAM,CAAC;AAI9B,YAAI,QAAQ,sBAAsB,IAAI,GAAG;AACvC,sBAAY,cAAc,iBAAiB,EAAC,QAAQ,KAAI,CAAC,CAAC,CAAC;AAAA,QAC7D,WAAW,eAAe;AACxB,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAChE,OAAO;AACL,sBAAY,gBAAgB,kBAAkB,YAAY,CAAC;AAC3D,gBAAM,WAAW;AAAA,QACnB;AAAA,MACF;AAAA,IACF,WAAW,SAAS,kBAAkB,oBAAoB;AACxD,kBAAY,cAAc,YAAY,CAAC;AAAA,IACzC,WAAW,SAAS,kBAAkB,iBAAiB,CAAC,eAAe,CAAC,qBAAqB;AAC3F,YAAM,IAAI,UAAU,eAAe,OAAO,eAAe,SAAS,eAAe,OAAO,eAAe;AACvG,YAAM,IAAI,UAAU,eAAe,OAAO,eAAe,SAAS,eAAe,OAAO,eAAe;AACvG,kBAAY,cAAc,SAAS,IAAI,CAAC,CAAC;AAAA,IAC3C;AAAA,EAGF;AAAA,EAEA,cAAc,EAAC,KAAI,GAAG,EAAC,eAAc,GAAG;AACtC,QAAI,EAAC,IAAG,IAAI;AACZ,QAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;AACvD,YAAM,uBAAuB,KAAK,cAAc;AAChD,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EAEA,eAAe,EAAC,KAAI,GAAG,EAAC,gBAAgB,iBAAgB,GAAG;AACzD,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,CAAC,mBAAmB,IAAI,GAAG;AACrC,aAAO,uBAAuB,MAAM,cAAc;AAClD,WAAK,OAAO;AAAA,IACd;AACA,qBAAiB,IAAI,KAAK,QAAQ,IAAI;AACtC,QAAI,MAAM;AACR,uBAAiB,IAAI,MAAM,IAAI;AAAA,IACjC;AAAA,EACF;AAAA,EAEA,aAAa,EAAC,MAAM,YAAW,GAAG,EAAC,UAAU,iBAAiB,cAAc,cAAc,YAAW,GAAG;AACtG,UAAM,EAAC,MAAM,QAAQ,MAAK,IAAI;AAE9B,QAAI,iBAAiB,SAAS,qBAAqB,SAAS,UAAU,UAAU;AAC9E,kBAAY,mBAAmB,qBAAqB,OAAO,EAAC,OAAM,CAAC,CAAC;AACpE;AAAA,IACF;AAEA,QAAI,iBAAiB,SAAS,qBAAqB,SAAS,UAAU,UAAU;AAC9E,kBAAY,UAAU,cAAc,cAAc,GAAG,MAAM,CAAC;AAC5D;AAAA,IACF;AAEA,QAAI,gBAAgB,SAAS,qBAAqB,QAAQ,UAAU,SAAS;AAC3E,kBAAY,mBAAmB,qBAAqB,MAAM,EAAC,OAAM,CAAC,CAAC;AACnE;AAAA,IACF;AACA,QAAI,SAAS,qBAAqB,KAAK;AACrC,kBAAY,sBAAsB,KAAK,CAAC;AAAA,IAC1C,WAAW,SAAS,qBAAqB,OAAO;AAC9C,kBAAY,sBAAsB,MAAM,EAAC,OAAM,CAAC,CAAC;AAAA,IACnD,WAAW,SAAS,qBAAqB,KAAK;AAC5C,kBAAY,sBAAsB,QAAQ,EAAC,OAAM,CAAC,CAAC;AAAA,IACrD,WAAW,SAAS,qBAAqB,aAAa;AACpD,kBAAY,cAAc,QAAQ,CAAC;AAAA,IACrC,WAAW,SAAS,qBAAqB,OAAO;AAE9C,kBAAY,sBAAsB,SAAS,EAAC,OAAM,CAAC,CAAC;AAAA,IACtD,WAAW,SAAS,qBAAqB,MAAM;AAC7C,kBAAY,UAAU,cAAc,eAAe,GAAG,MAAM,CAAC;AAAA,IAC/D,WAAW,SAAS,qBAAqB,UAAU;AACjD,UAAI,CAAC,oBAAoB,IAAI,KAAK,GAAG;AAGnC,aAAK,MAAM;AAAA,MACb;AAAA,IACF,WAAW,SAAS,qBAAqB,OAAO;AAC9C,UAAI,CAAC,oBAAoB,UAAU,WAAW,UAAU,UAAU;AAChE,YAAI,aAAa,UAAU;AACzB,gBAAM,IAAI,MAAM,gBAAgB,KAAK,qDAAqD;AAAA,QAC5F;AACA,YAAI,QAAQ;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,QACT,EAAE,KAAK;AACP,YAAI,QAAQ;AAGV,kBAAQ,MAAM,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AAAA,QAC3E;AACA,oBAAY,cAAc,IAAI,KAAK,GAAG,CAAC;AAAA,MACzC,OAAO;AACL,oBAAY,UAAU,cAAc,gBAAgB,IAAI,KAAK,CAAC,GAAG,MAAM,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,MAAM,OAAO;AACrB,UAAM,EAAC,MAAM,QAAQ,KAAK,QAAQ,aAAa,uBAAuB,sBAAqB,IAAI;AAC/F,UAAM,EAAC,MAAM,MAAK,IAAI;AACtB,QAAI,SAAS,kBAAkB,OAAO;AACpC,UAAI,CAAC,MAAM,UAAU,CAAC,MAAM,SAAS;AAEnC,eAAO;AAAA,MACT,OAAO;AACL,cAAM,YAAY,cAAc,YAAY,EAAC,MAAK,CAAC,GAAG,sBAAsB,CAAC;AAC7E,oBAAY,SAAS;AACrB,4BAAoB,WAAW,MAAM,OAAO,gBAAgB;AAAA,MAC9D;AAAA,IACF,WAAW,SAAS,kBAAkB,MAAM;AAC1C,YAAM,kBAAkB,IAAI,QAAQ,aAAa,CAAC,EAAE,SAAS,CAAC;AAE9D,YAAM;AAAA;AAAA,QAEJ,aAAa,IAAI,SAAS,SAAO,IAAI,SAAS,SAAS,KAAK,KAC5D,gBAAgB,aAAa,WAAW;AAAA;AAC1C,YAAM,WAAW,kBAAkB,kBAAkB,IAAI;AACzD,UAAI,OAAO,WAAW,YAAY,SAAS,aAAa,SAAS,GAAG;AAClE,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC1D;AACA,kBAAY,cAAc,iBAAiB,EAAC,QAAQ,KAAI,CAAC,GAAG,sBAAsB,CAAC,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EAEA,MAAM,EAAC,MAAM,OAAM,GAAG;AAEpB;AAAA,MAAE;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF,EAAE,QAAQ,OAAK,OAAO,KAAK,CAAC,CAAC;AAC7B,WAAO,OAAO,MAAM;AAAA;AAAA,MAElB,QAAQ;AAAA;AAAA,MAER,YAAY;AAAA;AAAA;AAAA;AAAA,MAIZ,WAAW;AAAA;AAAA,MAEX,QAAQ,KAAK,UAAU;AAAA;AAAA;AAAA;AAAA,IAIzB,CAAC;AAED,WAAO,UAAU;AAAA,MACf,SAAS;AAAA;AAAA,QAEP,GAAG;AAAA;AAAA;AAAA,QAGH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA;AAAA;AAAA;AAAA,QAIL,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,EAAC,KAAI,GAAG;AACZ,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAM,EAAC,QAAQ,QAAO,IAAI,KAAK;AAE/B,YAAQ,YAAY,OAAO,OAAO;AAClC,aAAS,YAAY,OAAO,QAAQ;AAEpC,YAAQ,UAAU,SAAS,UAAU,OAAO,OAAO;AACnD,YAAQ,cAAc,SAAS,cAAc,OAAO,OAAO;AAE3D,cAAU,CAAC,OAAO,KAAK,MAAM,EAAE,UAAU,OAAO,KAAK,MAAM;AAC3D,eAAW,CAAC,OAAO,KAAK,OAAO,EAAE,UAAU,OAAO,KAAK,MAAM;AAC7D,KAAC,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM,WAAW,OAAO,KAAK;AAAA,EAC3D;AAAA,EAEA,SAAS;AAAA,IACP,MAAM,EAAC,KAAI,GAAG,EAAC,gBAAe,GAAG;AAI/B,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB;AACtB,UAAI,qBAAqB;AACzB,iBAAW,OAAO,KAAK,cAAc;AACnC,YAAI,IAAI,SAAS,WAAW,KAAK,IAAI,SAAS,CAAC,EAAE,SAAS,kBAAkB,cAAc;AAMxF,cAAI,SAAS,IAAI;AAAA,QACnB,OAAO;AACL,gBAAM,WAAW,YAAY,IAAI,QAAQ;AACzC,cAAI,UAAU;AACZ,8BAAkB;AAClB,kBAAM,QAAQ,QAAQ,IACpB,UAAU,KAAK,GAAG,QAAQ,IAC1B,UAAU,KAAK,QAAQ;AAAA,UAC3B,OAAO;AACL,iCAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,UAAI,mBAAmB,CAAC,oBAAoB;AAE1C,kBAAU,QAAQ,OAAK,gBAAgB,IAAI,CAAC,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,KAAK,GAAG,EAAC,UAAU,kBAAkB,SAAQ,GAAG;AAC9C,UAAI,aAAa,YAAY,oBAAoB,UAAU;AACzD,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,KAAI,GAAG;AACjB,QAAI,KAAK,QAAQ,SAAS,SAAS,YAAY;AAE7C,YAAM,QAAQ,cAAc,YAAY,GAAG,CAAC,KAAK,OAAO,CAAC;AAEzD,YAAM,SAAS;AACf,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,KAAI,GAAG,EAAC,eAAc,GAAG;AACnC,QAAI,EAAC,IAAG,IAAI;AACZ,QAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;AACvD,YAAM,uBAAuB,KAAK,cAAc;AAChD,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EAEA,2BAA2B,EAAC,MAAM,YAAW,GAAG,EAAC,UAAU,gBAAe,GAAG;AAC3E,UAAM,EAAC,KAAI,IAAI;AACf,QAAI,SAAS,mCAAmC,SAAS;AACvD,kBAAY,cAAc,oCAAoC,CAAC;AAAA,IACjE,WAAW,SAAS,mCAAmC,UAAU;AAC/D,UAAI,aAAa,UAAU;AACzB,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC7D;AAIA,YAAM,QAAQ,kBAAkB,mBAAmB,WAAW,EAAE,OAAO,QAAQ,UAAU,MAAM;AAG/F,kBAAY,cAAc,YAAY,KAAK,iBAAiB,EAAC,4BAA4B,KAAI,CAAC,CAAC;AAAA,IACjG,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B,IAAI,GAAG;AAAA,IACxD;AAAA,EACF;AACF;AAEA,IAAM,oBAAoB;AAAA,EACxB,cAAc,EAAC,KAAI,GAAG,EAAC,8BAA8B,wBAAuB,GAAG;AAC7E,UAAM,EAAC,QAAQ,IAAG,IAAI;AACtB,QAAI,CAAC,QAAQ;AAGX,8BAAwB,IAAI,MAAM,CAAC,GAAG,6BAA6B,IAAI,GAAG,EAAE,IAAI,CAAC,EAAC,MAAAE,MAAI,MAAMA,KAAI,CAAC,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EAEA,gBAAgB;AAAA,IACd,MACE;AAAA,MAAE;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA;AAAA,MAAE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA;AAEA,YAAM,SAAS,kBAAkB,IAAI,IAAI;AAGzC,UAAI,UAAU,SAAS,IAAI,KAAK,MAAM,GAAG;AAIvC,cAAMC,aAAY,gBAAgB,KAAK,MAAM;AAC7C,gCAAwB,IAAIA,YAAW,SAAS,IAAI,KAAK,MAAM,CAAC;AAChE,oBAAYA,UAAS;AAErB,aAAK;AACL;AAAA,MACF;AACA,eAAS,IAAI,KAAK,QAAQ,IAAI;AAG9B,mCAA6B,IAAI,KAAK,QAAQ,CAAC,CAAC;AAChD,UAAI,KAAK,MAAM;AACb,oBAAY,8BAA8B,KAAK,MAAM,CAAC,CAAC;AAAA,MACzD;AACA,YAAM,iBAAiB,6BAA6B,IAAI,KAAK,QAAQ,KAAK,MAAM;AAChF,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAO9C,cAAM,YAAY,eAAe,CAAC;AAClC;AAAA;AAAA;AAAA,UAGG,WAAW,UAAU,QAAS,UAAU,WAAW,UAAU;AAAA;AAAA,UAG9D,SAAS,UAAU;AAAA,UACnB;AACA,yBAAe,OAAO,GAAG,CAAC;AAC1B;AAAA,QACF;AAAA,MACF;AACA,mCAA6B,IAAI,KAAK,MAAM,EAAE,KAAK,EAAC,MAAM,OAAM,CAAC;AACjE,UAAI,KAAK,MAAM;AACb,qCAA6B,IAAI,KAAK,IAAI,EAAE,KAAK,EAAC,MAAM,OAAM,CAAC;AAAA,MACjE;AAQA,UAAI,KAAK,MAAM;AACb,cAAM,qBAAqB,YAAY,cAAc,KAAK,MAAM,oBAAI,IAAI,CAAC;AACzE,YAAI,2BAA2B;AAC/B,YAAI,QAAQ;AAEV,qCAA2B;AAAA,QAC7B,OAAO;AACL,qBAAW,aAAa,mBAAmB,OAAO,GAAG;AACnD,gBAAI,CAAC,UAAU,0BAA0B;AAEvC,yCAA2B;AAC3B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,qBAAa,IAAI,KAAK,IAAI,EAAE,IAAI,MAAM,EAAC,MAAM,yBAAwB,CAAC;AAAA,MACxE;AAAA,IACF;AAAA,IACA,KAAK,EAAC,KAAI,GAAG,EAAC,SAAQ,GAAG;AACvB,eAAS,OAAO,KAAK,MAAM;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,MAAM,EAAC,KAAI,GAAG,OAAO;AAEnB,YAAM,YAAY,MAAM;AACxB,UAAI,KAAK,OAAO;AACd,cAAM,eAAe,mBAAmB,MAAM,cAAc,KAAK,KAAK;AAAA,MACxE;AAAA,IACF;AAAA,IACA,KAAK,GAAG,OAAO;AACb,YAAM,eAAe,MAAM;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,UAAU,EAAC,MAAM,OAAM,GAAG,EAAC,wBAAuB,GAAG;AAInD,UAAM,EAAC,IAAG,IAAI;AAEd,QAAI,SAAS;AACb,WAAQ,SAAS,OAAO,QAAS;AAC/B,UAAI,OAAO,SAAS,SAAS,mBAAmB,OAAO,SAAS,OAAO,OAAO,WAAW,MAAM;AAC7F;AAAA,MACF;AAAA,IACF;AAGA,4BAAwB,IAAI,MAAM,MAAM;AAAA,EAC1C;AAAA,EAEA,WAAW,MAAM,OAAO;AACtB,UAAM,EAAC,MAAM,YAAW,IAAI;AAC5B,UAAM,EAAC,IAAG,IAAI;AACd,UAAM,kBAAkB,MAAM,iBAAiB,IAAI,GAAG;AAEtD,UAAM,oBAAoB,QAAQ;AAClC,UAAM,qBAAqB,oBACzB,gBAAgB,CAAC;AAAA;AAAA,MAEjB,oBAAoB,iBAAiB,MAAM,mBAAmB,IAAI;AAAA;AACpE,QAAI,cAAc;AAClB,QAAI,CAAC,mBAAmB;AAEtB,YAAM,sBAAsB,iCAAiC,cAAc,iBAAiB,CAAAD,UAAQ;AAClG,eAAOA,MAAK,SAAS,SAAS,SAAS,CAAC,CAACA,MAAK;AAAA,MAChD,CAAC,CAAC;AACF,YAAM,mBAAmB,sBACvB,mBAAmB,MAAM,aAAa,mBAAmB,IACzD,MAAM;AACR,UAAI,CAAC,cAAc,kBAAkB,MAAM,YAAY,GAAG;AACxD,sBAAc,cAAc,YAAY;AAAA,UACtC,OAAO,qBAAqB,gBAAgB;AAAA,QAC9C,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAAA,MAC1B;AAAA,IACF;AACA,gBAAY,WAAW;AACvB,QAAI,CAAC,mBAAmB;AACtB,0BAAoB,aAAa,MAAM,OAAO,iBAAiB;AAAA,IACjE;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,cAAc,EAAC,MAAM,YAAW,GAAG,OAAO;AACxC,QAAI,KAAK,QAAQ;AACf,YAAM,uBAAuB,KAAK,IAAI,MAAM,sBAAsB,KAAK,GAAG;AAE1E;AAAA,IACF;AACA,UAAM,cAAc,MAAM,wBAAwB,IAAI,IAAI;AAC1D,UAAM,eAAe,YAAY,OAAO,YAAU,uBAAuB,QAAQ,IAAI,CAAC;AAKtF,QAAI,CAAC,aAAa,QAAQ;AAGxB,kBAAY,cAAc,iBAAiB,EAAC,QAAQ,KAAI,CAAC,CAAC,CAAC;AAAA,IAC7D,WAAW,aAAa,SAAS,GAAG;AAElC,YAAM,OAAO,aAAa,IAAI,YAAU;AAAA,QACtC,kBAAkB;AAAA,QAClB,CAAC,oBAAoB,OAAO,MAAM,CAAC;AAAA,MACrC,CAAC;AACD,kBAAY,iBAAiB,YAAY,GAAG,IAAI,CAAC;AAAA,IACnD,OAAO;AACL,WAAK,MAAM,aAAa,CAAC,EAAE;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,eAAe,EAAC,KAAI,GAAG,OAAO;AAE5B,SAAK,SAAS,EAAE,MAAM;AACtB,QAAI,KAAK,MAAM;AAGb,UAAI,MAAM,aAAa,IAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,0BAA0B;AACxE,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,EAAC,KAAI,GAAG,OAAO;AACvB,QAAI,KAAK,QAAQ,GAAG;AAClB;AAAA,IACF;AAMA,SAAK,MAAM,MAAM,wBAAwB,IAAI,IAAI,EAAE;AAAA,EACrD;AAAA,EAEA,OAAO;AAAA,IACL,KAAK,EAAC,KAAI,GAAG,OAAO;AAUlB,YAAM,gBAAgB,KAAK,IAAI,MAAM,uBAAuB,MAAM,mBAAmB,CAAC;AACtF,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,cAAM,eAAe,qBAAqB;AAC1C,aAAK,QAAQ,aAAa,GAAG,EAAE,EAAE,SAAS,KAAK,YAAY;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAM,iBAAiB;AAEvB,IAAM,kBAAkB;AAExB,SAAS,iBAAiB,QAAQ,MAAM;AACtC,OAAK,QAAQ,SAAO,IAAI,SAAS,MAAM;AACvC,SAAO,qBAAqB,MAAM,CAAC,IAAI;AACvC,SAAO;AACT;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE;AACrD;AAEA,SAAS,uBAAuB,SAAS,MAAM;AAG7C,MAAI,iBAAiB;AACrB,KAAG;AACD,QAAI,eAAe,SAAS,SAAS,SAAS;AAE5C,aAAO;AAAA,IACT;AACA,QAAI,eAAe,SAAS,SAAS,aAAa;AAEhD;AAAA,IACF;AACA,QAAI,mBAAmB,SAAS;AAE9B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,QAAQ,eAAe,MAAM;AAClD,eAAW,OAAO,cAAc;AAC9B,UAAI,QAAQ,gBAAgB;AAE1B;AAAA,MACF;AACA,UAAI,QAAQ,SAAS;AACnB,eAAO;AAAA,MACT;AACA,UAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAU,iBAAiB,eAAe;AAC1C,QAAM,IAAI,MAAM,iBAAiB;AACnC;AAKA,SAAS,oBAAoB,KAAK,WAAW,IAAI,KAAK;AACpD,QAAM,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACzC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC9C,QAAI,QAAQ,UAAU;AAEpB,YAAM,SAAS,MAAM,QAAQ,EAAE,IAAI,MAAM;AAAA,IAC3C,WAAW,SAAS,OAAO,UAAU,UAAU;AAC7C,YAAM,GAAG,IAAI,oBAAoB,OAAO,WAAW,OAAO,EAAE;AAAA,IAC9D,OAAO;AACL,UAAI,QAAQ,UAAU,UAAU,SAAS,gBAAgB;AAEvD,kBAAU,IAAI,OAAO,UAAU,IAAI,GAAG,KAAK,GAAG;AAAA,MAChD;AACA,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,gBAAgB,KAAK;AAC5B,SAAO;AAAA,IACL,MAAM,SAAS;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,cAAc,MAAM,UAAU;AACrC,QAAM,UAAU,CAAC;AACjB,SAAQ,OAAO,KAAK,QAAS;AAC3B,QAAI,CAAC,YAAY,SAAS,IAAI,GAAG;AAC/B,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,uBAAuB,MAAM,KAAK;AACzC,MAAI,IAAI,IAAI,IAAI,GAAG;AACjB,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AAEA,QAAM,SAAS,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,2CAA2C,GAAG,CAAC;AAC3F,MAAI,IAAI,MAAM,MAAM;AACpB,SAAO;AACT;AAGA,SAAS,qBAAqB,MAAM;AAClC,aAAW,YAAY,CAAC,gBAAgB,WAAW,UAAU,GAAG;AAC9D,QAAI,KAAK,QAAQ,GAAG;AAClB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,iCAAiC,WAAW;AACnD,QAAM,YAAY,CAAC,UAAU,YAAY;AACzC,QAAM,gBAAgB,EAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,EAAC;AAC9C,YAAU,QAAQ,CAAC,EAAC,MAAK,MAAM;AAC7B,cAAU,QAAQ,UAAQ;AACxB,UAAI,MAAM,SAAS,IAAI,GAAG;AAExB,eAAO,cAAc,QAAQ,IAAI;AACjC,sBAAc,OAAO,IAAI,IAAI;AAAA,MAC/B;AACA,UAAI,MAAM,UAAU,IAAI,GAAG;AACzB,sBAAc,QAAQ,IAAI,IAAI;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,OAAO,KAAK,cAAc,MAAM,EAAE,QAAQ;AAC7C,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,CAAC,OAAO,KAAK,cAAc,OAAO,EAAE,QAAQ;AAC9C,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,cAAc,UAAU,cAAc,SAAS;AACjD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,EAAC,QAAQ,WAAU,GAAG;AAClD,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,YAAY;AACxB,SAAK,SAAS,CAAC;AACf,eAAW,KAAK,OAAO,SAAS;AAChC,mBAAe,KAAK,OAAO,aAAa;AAAA,EAC1C;AACA,MAAI,CAAC,UAAU,CAAC,YAAY;AAC1B,SAAK,UAAU,CAAC;AAChB,KAAC,WAAW,KAAK,QAAQ,SAAS;AAClC,KAAC,eAAe,KAAK,QAAQ,aAAa;AAAA,EAC5C;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAGA,MAAI,KAAK,SAAS,SAAS,YAAY;AACrC,WAAO,CAAC,KAAK,OAAO;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,IAAI;AAC1C,SAAO,YAAY,KAAK,QAAQ;AAClC;AAEA,SAAS,YAAY,KAAK;AACxB,QAAM,kBAAkB,IAAI,KAAK,QAC/B,GAAG,SAAS,kBAAkB,gBAC9B,kBAAkB,IAAI,EAAC,QAAQ,MAAK,CAAC,KACrC,CAAC,mBAAmB,EAAE,CACvB;AACD,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,SAAS,kBAAkB,cAAc;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,aAAa,eAAe,GAAG;AACjC,WAAO,gBAAgB,aAAa,CAAC,EAAE,SAAS,CAAC;AAAA,EACnD;AACA,MAAI,mBAAmB,eAAe,GAAG;AACvC,UAAM,iBAAiB,CAAC;AAExB,eAAW,OAAO,gBAAgB,cAAc;AAC9C,YAAM,WAAW,YAAY,IAAI,QAAQ;AACzC,UAAI,CAAC,UAAU;AAEb,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,QAAQ,IACpB,eAAe,KAAK,GAAG,QAAQ,IAC/B,eAAe,KAAK,QAAQ;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,cAAc,MAAM,YAAY;AACvC,QAAM,OAAO,QAAQ,IAAI,KAAK,CAAC;AAC/B,aAAW,OAAO,MAAM;AACtB,QACE,QAAQ,cACR,cAAc,KAAK,UAAU,GAC7B;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,MAAM,SAAS;AACxC,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR,GAAG;AAAA,EACL;AACA,SACE,aAAa,IAAI,MAChB,KAAK,WAAW,QAAQ,KAAK,WAAW,KAAK,WAC9C,aAAa,MAAM,SAAO,IAAI,SAAS,kBAAkB,YAAY;AAEzE;AAGA,SAAS,mBAAmB,MAAM;AAGhC,SAAO,wCAAwC,KAAK,IAAI;AAC1D;AAGA,SAAS,cAAc,SAAS,SAAS;AACvC,QAAM,MAAM,MAAM,SAAS,OAAO,GAAG,OAAO;AAC5C,QAAM,OAAO,IAAI,QAAQ;AACzB,MAAI,KAAK,SAAS,KAAK,KAAK,CAAC,EAAE,SAAS,SAAS,GAAG;AAClD,WAAO,iBAAiB,YAAY,GAAG,IAAI;AAAA,EAC7C;AACA,SAAO,KAAK,CAAC,EAAE,SAAS,CAAC;AAC3B;AAEA,SAAS,cAAc,MAAM,MAAM;AACjC,QAAM,WAAW,qBAAqB,IAAI;AAE1C,OAAK,QAAQ,EAAE,CAAC,EAAE,SAAS;AAC3B,MAAI,MAAM;AACR,qBAAiB,KAAK,QAAQ,EAAE,CAAC,GAAG,IAAI;AAAA,EAC1C;AACA,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,QAAQ;AAC/B,OAAK,SAAS;AACd,SAAO;AACT;AAEA,SAAS,oBAAoB,aAAa,EAAC,QAAQ,KAAK,UAAS,GAAG,OAAO,SAAS;AAClF,WAAS;AAAA;AAAA,IAEP,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,OAAO,OAAO;AACnB;;;ACr4BA,SAAS,SAAS,KAAK,SAAS;AAC9B,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,kBAAkB,YAAY,KAAK,QAAQ,QAAQ;AACzD,QAAM,kBAAkB,YAAY,KAAK,QAAQ,QAAQ;AACzD,QAAM,iBAAiB,KAAK,MAAM;AAClC,MAAI,CAAC,OAAO,UAAU,cAAc,KAAK,iBAAiB,KAAK,iBAAiB,IAAI;AAClF,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AAOA,MAAI,yBAAyB;AAC7B,MAAI,uBAAuB;AAC3B,MAAI,CAAC,iBAAiB;AACpB,UAAM,SAAS,CAAC,IAAI,MAAM,UAAU;AACpC,aAAS,EAAC,MAAM,IAAG,GAAG;AAAA,MACpB,gBAAgB,MAAM,OAAO,GAAG,EAAE;AAAA,MAClC,UAAU;AAAC,eAAO,IAAI;AAAA,MAAC;AAAA,MACvB,SAAS,OAAO;AAAC,eAAO,KAAK,KAAK;AAAA,MAAC;AAAA,MACnC,kBAAkB;AAChB,YAAI,OAAO,GAAG,EAAE,GAAG;AACjB,mCAAyB;AAAA,QAC3B,OAAO;AACL,iCAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,GAAG,mBAAmB;AAAA,EACxB;AAEA,QAAM,qBAAqB;AAAA,IACzB,QAAQ,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,IAKlB,YAAY,CAAC,GAAG,IAAI,MAAM,cAAc,2BAA2B,CAAC;AAAA,EACtE;AACA,MAAI,WAAW;AACf,QAAM,QAAQ;AAAA,IACZ,UAAU,KAAK;AAAA,IACf;AAAA,IACA,YAAY,oBAAI,IAAI;AAAA,IACpB,cAAc;AAAA,MACZ,QAAQ,IAAI,MAAM;AAAA,MAClB,YAAY,IAAI,MAAM;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,WAAW,IAAI;AAAA,IACf;AAAA,IACA,sBAAsB,CAAC,EAAE,CAAC,mBAAmB,0BAA0B;AAAA,IACvE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS,KAAK;AAAA,EAChB;AACA,WAAS,IAAI,MAAM;AACjB,UAAM,WAAW;AACjB,eAAW;AACX,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,SAAS;AAEZ,eAAO;AAAA,UACL,SAAS,IAAI,KAAK,OAAO;AAAA,UACzB,OAAO,IAAI,KAAK,KAAK;AAAA,UACrB,SAAS,EAAC,GAAG,KAAK,QAAO;AAAA,QAC3B;AAAA,MACF,KAAK,SAAS;AACZ,eAAO,KAAK,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE;AAAA,MACvC,KAAK,SAAS;AACZ,eAAO,aAAa,MAAM,OAAO,GAAG;AAAA,MACtC,KAAK,SAAS;AACZ,eAAO,iBAAiB,MAAM,KAAK;AAAA,MACrC,KAAK,SAAS;AACZ,eAAO,kBAAkB,MAAM,OAAO,GAAG;AAAA,MAC3C,KAAK,SAAS;AACZ,eAAO,aAAa,MAAM,KAAK;AAAA,MACjC,KAAK,SAAS;AACZ,eAAO,kBAAkB,MAAM,OAAO,GAAG;AAAA,MAC3C,KAAK,SAAS;AACZ,YAAI,CAAC,MAAM,UAAU;AACnB,gBAAM,IAAI,MAAM,sDAAsD;AAAA,QACxE;AACA,eAAO,KAAK,QAAQ,IAAI,GAAG,EAAE,KAAK,IAAI;AAAA,MACxC,KAAK,SAAS;AACZ,eAAO,uBAAuB,MAAM,KAAK;AAAA,MAC3C,KAAK,SAAS;AACZ,eAAO,gBAAgB,MAAM,KAAK;AAAA,MACpC,KAAK,SAAS;AACZ,eAAO,SAAS,MAAM,KAAK;AAAA,MAC7B,KAAK,SAAS;AACZ,eAAO,SAAS,MAAM,OAAO,GAAG;AAAA,MAClC,KAAK,SAAS;AACZ,eAAO,KAAK,aAAa,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA,MAC5C,KAAK,SAAS;AACZ,eAAO,IAAI,KAAK,OAAO,IAAI,iBAAiB,IAAI;AAAA,MAClD,KAAK,SAAS;AACZ,eAAO,aAAa,MAAM,KAAK;AAAA,MACjC;AAGE,cAAM,IAAI,MAAM,yBAAyB,KAAK,IAAI,GAAG;AAAA,IACzD;AAAA,EACF;AAEA,QAAM,SAAS,IAAI,GAAG;AACtB,MAAI,CAAC,iBAAiB;AAEpB,WAAO,OAAO,QAAQ,MAAM;AAC5B,WAAO,QAAQ,QAAQ,IAAI;AAC3B,WAAO,QAAQ,oBAAoB;AAAA,EACrC;AACA,SAAO,oBAAoB,oBAAI,IAAI;AACnC,SAAO,kBAAkB,CAAC;AAC1B,QAAM,WAAW,QAAQ,CAAC,OAAO,QAAQ;AACvC,QAAI,MAAM,QAAQ;AAChB,aAAO,gBAAgB,KAAK,GAAG;AAAA,IACjC;AACA,QAAI,MAAM,YAAY;AACpB,kBAAY,OAAO,mBAAmB,MAAM,YAAY,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACtE;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAM,sBAAsB;AAAA,EAC1B,UAAU;AAAA,IACR,MAAM,EAAC,KAAI,GAAG,OAAO;AACnB,YAAM,cAAc,MAAM,eAAe;AACzC,YAAM;AAAA,QACJ,KAAK,QACH,mBAAmB,EAAC,YAAY,YAAW,GAAG,KAAK,KAAK,EAAE,aAC1D;AAAA,MACJ;AAAA,IACF;AAAA,IACA,KAAK,GAAG,OAAO;AACb,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc,GAAG,OAAO;AAItB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,UAAU,EAAC,KAAI,GAAG,OAAO;AACvB,QAAI,YAAY,GAAG,KAAK,KAAK,CAAC,GAAG;AAC/B,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,oBAAoB,EAAC,MAAM,KAAI,GAAG,OAAO;AACvC,SAAK;AACL,QAAI,8BAA8B,MAAM,EAAC,WAAW,KAAI,CAAC,EAAE,QAAQ;AACjE,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,aAAa,EAAC,KAAI,GAAG,OAAO;AAC1B,QACE,KAAK,SAAS,qBAAqB,YACnC,kCAAkC,IAAI,KAAK,KAAK,GAChD;AACA,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AACpE,CAAC;AACD,IAAM,uBAAuB,oBAAI,IAAI;AAAA,EACnC;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA;AAAA;AAAA,EAGhB;AACF,CAAC;AACD,IAAM,4BAA4B,oBAAI,IAAI;AAAA,EACxC;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA;AAAA,EAEnD;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AACvF,CAAC;AACD,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAChC,CAAE,GAAG,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,MAAQ,SAAS;AAAA;AAAA,EAClB,CAAC,MAAQ,SAAS;AAAA;AAAA,EAClB,CAAC,OAAQ,SAAS;AAAA;AACpB,CAAC;AAED,IAAM,UAAU;AAChB,SAAS,YAAY,MAAM;AACzB,SAAO,QAAQ,KAAK,IAAI;AAC1B;AAEA,SAAS,aAAa,MAAM,GAAG,KAAK;AAClC,QAAM,EAAC,MAAM,QAAQ,aAAY,IAAI;AACrC,MAAI,aAAa,IAAI,GAAG;AACtB,UAAM,SAAS,GAAG,SAAS,kBAAkB,YAAY,KAAK,GAAG,GAAG,SAAS,MAAM,GAAG;AACtF,WAAO,KAAK,MAAM,GAAG,aAAa,IAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EACtD;AAGA,MAAI,SAAS,kBAAkB,YAAY;AACzC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,kBAAkB,cAAc;AAC3C,WAAO;AAAA,EACT;AAGA,MAAI,SAAS,kBAAkB,eAAe;AAC5C,WAAO,SAAS,QAAQ;AAAA,EAC1B;AAGA,QAAM,IAAI,MAAM,8BAA8B,IAAI,GAAG;AACvD;AAEA,SAAS,iBAAiB,EAAC,IAAG,GAAG,OAAO;AACtC,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AACA,MACE,CAAC,MAAM,eACP,MAAM,aAAa,YACnB,MAAM,aAAa,cACnB,CAAC,MAAM,WAAW,IAAI,GAAG,EAAE,YAC3B;AACA,UAAM,IAAI,MAAM,uGAAuG;AAAA,EACzH;AACA,SAAO,OAAO;AAChB;AAEA,SAAS,kBAAkB,MAAM,OAAO,KAAK;AAC3C,QAAM,EAAC,MAAM,QAAQ,aAAY,IAAI;AACrC,QAAM,OAAO,EAAC,YAAY,MAAM,aAAa,WAAU;AAEvD,QAAM,SAAS,MAAM,UAAU,IAAI,IAAI;AACvC,MAAI,QAAQ;AAEV,SAAK,SAAS;AAId,QAAI,SAAS,OAAO,QAAQ;AAC1B,WAAK,aAAa,OAAO;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,WAAW,IAAI,QAAQ,IAAI;AACjC,SAAO,IAAI,OAAO,KAAK,IAAI,MAAM,EAAE,GAAG,aAAa,IAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AACvE;AAEA,SAAS,aAAa,EAAC,MAAK,GAAG,OAAO;AACpC,QAAM,OAAO,GAAG,KAAK;AACrB,QAAM,UAAU,cAAc,OAAO;AAAA,IACnC,gBAAgB,MAAM,SAAS,SAAS,SAAS;AAAA,IACjD,aAAa,MAAM;AAAA,IACnB,UAAU,MAAM;AAAA,EAClB,CAAC;AACD,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,wBAAwB,MAAM,aAAa,cAAc,YAAY,IAAI,GAAG;AACpF,UAAM,QAAQ,wBAAwB,IAAI;AAC1C,WAAO,MAAM,cACX,MAAM,KAAK,EAAE,IACZ,MAAM,SAAS,IAAI,IAAI,MAAM,KAAK,EAAE,CAAC,MAAM,MAAM,CAAC;AAAA,EACvD;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,EAAC,QAAQ,QAAQ,SAAQ,GAAG,OAAO,KAAK;AAEjE,MAAI,SAAS,wCAAwC,MAAM,YAAY,SAAS,KAAK,eAAe,GAAG;AAErG,eAAW,SAAS,OAAO,UAAQ,CAAC,gBAAgB,IAAI,CAAC;AACzD,aAAS,KAAK,gBAAgB,EAAE,CAAC;AAAA,EACnC;AACA,QAAM,WAAW,MAAM,IAAI,SAAS,MAAM,EAAE,GAAG,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AACzE,MAAI,CAAC,MAAM,aAAa;AAEtB,UAAM,cAAc;AACpB,UAAM,SAAS,SAAS;AACxB,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,SAAS,CAAC,GAAG;AAC/B,MACE,CAAC,UACD;AAAA,IAGK,CAAC,MAAM,YAAY,CAAC,MAAM,YAC3B,OAAO,SAAS,SAAS,kBACzB,cAAc,SAAS,8BACvB,EAAE,SAAS,wCAAwC,MAAM,aAEzD,CAAC,MAAM,WACP,OAAO,SAAS,SAAS;AAAA,EAEzB,SAAS,WAAW,KACpB,cAAc,SAAS,kBACvB,cAAc,SAAS,sBAG3B;AAGA,WAAO,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE;AAAA,EAClC;AACA,MAAI,CAAC,MAAM,YAAY,OAAO,SAAS,SAAS,gBAAgB;AAC9D,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC5E;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,uBAAuB,MAAM,OAAO;AAC3C,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,UAAU;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU,MAAM;AAAA,EAClB;AACA,QAAM,SAAS,cAAc,KAAK,OAAO;AACzC,QAAM,SAAS,cAAc,KAAK,OAAO;AACzC,QAAM,aAAa,oBAAI,IAAI;AAC3B,MAAI,MAAM,wBAAwB,MAAM,aAAa,YAAY;AAE/D,UAAM,oBAAoB,8BAA8B,IAAI;AAC5D,UAAM,SAAS,4BAA4B,iBAAiB;AAC5D,WAAO,QAAQ,WAAS;AACtB,iBAAW;AAAA,QACT,MAAM,QAAQ,KAAK,IACjB,GAAG,cAAc,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,cAAc,MAAM,CAAC,GAAG,OAAO,CAAC,KACvE,cAAc,OAAO,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,CAAC;AACvD;AAEA,SAAS,gBAAgB,EAAC,MAAM,QAAQ,OAAO,IAAG,GAAG,OAAO;AAC1D,MAAI,SAAS,qBAAqB,KAAK;AACrC,WAAO,MAAM,aAAa,SACtB,MAAM,mBAAmB,UAAU,MAAM,cAAe,MAAM;AAAA;AAAA,MAEhE;AAAA;AAAA,EACJ;AACA,MAAI,SAAS,qBAAqB,OAAO;AACvC,WAAO,SAAS,QAAQ;AAAA,EAC1B;AACA,MAAI,SAAS,qBAAqB,UAAU;AAC1C,QACE,MAAM,wBACN,MAAM,aAAa,cACnB,kCAAkC,IAAI,KAAK,GAC3C;AAKA,YAAM,IAAI,MAAM,qBAAqB,KAAK,iEAAiE;AAAA,IAC7G;AACA,WAAO,GAAG,SAAS,QAAQ,KAAK,IAAI,MAAM,GAAG,GAAG,MAAM,EAAE,GAAG,KAAK;AAAA,EAClE;AACA,MAAI,SAAS,qBAAqB,MAAM;AACtC,WAAO,SAAS,QAAQ;AAAA,EAC1B;AAEA,QAAM,IAAI,MAAM,kCAAkC,IAAI,GAAG;AAC3D;AAEA,SAAS,SAAS,MAAM,OAAO;AAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAMG,MAAM,mBAAmB,aAAa,MAAM,OAC5C,KAAK,SAAS,MAAM,OACpB,KAAK,SAAS,MAAM;AAAA;AAIzB;AAEA,SAAS,SAAS,EAAC,QAAAE,SAAQ,OAAO,QAAQ,aAAY,GAAG,OAAO,KAAK;AACnE,QAAM,eAAe,MAAM;AAC3B,MAAI,OAAO;AACT,UAAM,eAAe,mBAAmB,cAAc,KAAK;AAAA,EAC7D;AACA,QAAM,WAAW,aAAa,IAAI,GAAG,EAAE,KAAK,GAAG;AAC/C,QAAM,SACJ,CAAC,MAAM,WACP,aAAa,WAAW,KACxB,OAAO,SAAS,SAAS,cACzB,CAACA,YACA,CAAC,MAAM,eAAe,CAAC,SACrB,WAAW,KAAK,eAAeA,SAAQ,OAAO,MAAM,WAAW,CAAC,GAAG,QAAQ;AAChF,QAAM,eAAe;AACrB,SAAO;AACT;AAEA,SAAS,aAAa,EAAC,IAAG,GAAG,OAAO;AAClC,QAAM,QAAQ,MAAM;AAEpB,SAAO,QAAQ,IAAI,OAAO,KAAK,MAAM,OAAO,GAAG,MAAM,KAAK;AAC5D;AAMA,SAAS,8BAA8B,MAAM,SAAS;AACpD,QAAM,YAAY,CAAC,CAAC,SAAS;AAC7B,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,QAAQ,CAAC;AAIf,MAAK,MAAM,OAAO,QAAQ,SAAU,OAAO,WAAc,QAAQ,SAAW,OAAO,QAAU;AAC3F,WAAO;AAAA,EACT;AACA,WAAS,IAAI,KAAK,KAAK,KAAK,KAAK;AAC/B,UAAM,OAAO,GAAG,CAAC;AACjB,QAAI,CAAC,YAAY,IAAI,GAAG;AACtB;AAAA,IACF;AACA,UAAM,oBAAoB,wBAAwB,IAAI,EAAE,OAAO,gBAAc;AAC3E,YAAM,MAAM,WAAW,YAAY,CAAC;AACpC,aAAO,MAAM,OAAO,MAAM;AAAA,IAC5B,CAAC;AACD,QAAI,kBAAkB,QAAQ;AAC5B,YAAM,KAAK,GAAG,iBAAiB;AAC/B,UAAI,WAAW;AACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,cAAc,WAAW,EAAC,gBAAgB,aAAa,SAAQ,GAAG;AACzE,MAAI,kBAAkB,IAAI,SAAS,GAAG;AACpC,WAAO,kBAAkB,IAAI,SAAS;AAAA,EACxC;AACA;AAAA;AAAA,IAEE,YAAY,MAAO,YAAY,OAAO,YAAY;AAAA,IAElD,YAAY;AAAA,IAEX,kBAAkB,gBAAgB,SAAS;AAAA,IAC5C;AAEA,WAAO,YAAY,MACjB,OAAO,UAAU,SAAS,EAAE,EAAE,YAAY,CAAC,MAC3C,MAAM,UAAU,SAAS,EAAE,EAAE,YAAY,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,EAC/D;AACA,QAAM,cAAc,cACjB,WAAW,4BAA4B,uBACxC;AACF,QAAM,OAAO,GAAG,SAAS;AACzB,UAAQ,YAAY,IAAI,IAAI,IAAI,OAAO,MAAM;AAC/C;AAEA,SAAS,4BAA4B,OAAO;AAC1C,QAAM,aAAa,MAAM,IAAI,UAAQ,KAAK,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAC9E,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,WAAW,IAAI,CAAC,MAAM,WAAW,CAAC,IAAI,GAAG;AAC3C,gBAAU,WAAW,CAAC;AAAA,IACxB,WAAW,UAAU,MAAM;AACzB,aAAO,KAAK,WAAW,CAAC,CAAC;AAAA,IAC3B,OAAO;AACL,aAAO,KAAK,CAAC,OAAO,WAAW,CAAC,CAAC,CAAC;AAClC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAeA,SAAQ,UAAU,aAAa;AACrD,MAAIA,SAAQ;AACV,WAAO;AAAA,EACT;AACA,MAAI,OAAO;AACX,MAAI,YAAY,aAAa;AAC3B,UAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,YACG,QAAQ,aAAa,MAAM,OAC3B,QAAQ,SAAS,MAAM,OACvB,UAAU,MAAM,OAChB,SAAS,aAAa,MAAM,OAC5B,SAAS,SAAS,MAAM;AAAA,EAC7B;AACA,SAAO,GAAG,IAAI;AAChB;AAEA,SAAS,iBAAiB,EAAC,KAAK,KAAK,QAAQ,YAAAC,YAAU,GAAG;AACxD,MAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,WAAO;AAAA,EACT,WAAW,CAAC,OAAO,QAAQ,UAAU;AACnC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK,QAAQ,UAAU;AACxC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK;AACtB,WAAO,IAAI,GAAG;AAAA,EAChB,OAAO;AACL,WAAO,IAAI,GAAG,IAAI,QAAQ,WAAW,KAAK,GAAG;AAAA,EAC/C;AACA,SAAO,QAAQA,cAAa,MAAO,SAAS,KAAK;AACnD;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,QAAQ,MAAM,QAAQ;AAC/B;AAEA,SAAS,gBAAgB,EAAC,MAAM,MAAK,GAAG;AACtC,SAAO,SAAS,SAAS,aAAa,UAAU;AAClD;;;ACzhBA,IAAM,iBAAN,MAAM,wBAAuB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlC,cAAc,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAKtB,YAAY;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA,EAKX,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,aAAa,CAAC;AAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,SAAS,OAAO,SAAS;AACnC,UAAM,cAAc,CAAC,CAAC,SAAS;AAC/B,QAAI,mBAAmB,QAAQ;AAG7B,UAAI,SAAS;AACX,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAChE;AACA,YAAM,KAAK;AACX,YAAM,IAAI,KAAK;AACf,WAAK,WAAW,GAAG;AACnB,UAAI,cAAc,iBAAgB;AAChC,aAAK,cAAc,GAAG;AACtB,aAAK,WAAW,GAAG;AACnB,aAAK,YAAY,GAAG;AACpB,aAAK,aAAa,GAAG;AAAA,MACvB;AAAA,IACF,OAAO;AACL,YAAM,OAAO;AAAA,QACX,gBAAgB,CAAC;AAAA,QACjB,UAAU;AAAA,QACV,WAAW,CAAC;AAAA,QACZ,GAAG;AAAA,MACL;AACA,YAAM,cAAc,KAAK,SAAS,KAAK;AACvC,WAAK,WAAW;AAChB,WAAK,cAAc,iBAAiB,KAAK,gBAAgB,KAAK,SAAS;AACvE,WAAK,YAAY,KAAK;AAEtB,WAAK,aAAa,WAAW,CAAC;AAAA,IAChC;AACA,QAAI,CAAC,aAAa;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,KAAK;AAER,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,EAAC,aAAa,GAAG,KAAI,IAAI,KAAK;AACpC,WAAK,YAAY,IAAI,gBAAe,KAAK,UAAU,KAAK,OAAO,IAAI;AAAA,IACrE;AAEA,UAAM,eAAe,KAAK,UAAU,KAAK;AACzC,UAAM,MAAM,KAAK;AAEjB,QAAI,KAAK,cAAc,iBAAiB,gBAAgB,KAAK;AAE3D,WAAK,YAAY;AAOjB,YAAM,QAAQ,KAAK,UAAU,IAAI,MAAM,GAAG,CAAC;AAC3C,UAAI,OAAO;AACT,oCAA4B,OAAO,KAAK,KAAK,KAAK,UAAU;AAC5D,aAAK,aAAa;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,KAAK;AAEb,SAAK,UAAU,YAAY,KAAK;AAChC,UAAM,QAAQ,MAAM,KAAK,KAAK,KAAK,WAAW,GAAG;AACjD,SAAK,YAAY,KAAK,UAAU;AAEhC,QAAI,CAAC,SAAS,CAAC,KAAK,YAAY,MAAM;AACpC,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,CAAC,GAAG,KAAK;AAE3B,UAAM,SAAS;AACf,QAAI;AACJ,QAAI,KAAK,YAAY;AACnB,oBAAc,CAAC,GAAG,MAAM,OAAO;AAC/B,YAAM,QAAQ,SAAS;AAAA,IACzB;AACA,UAAM,aAAa,CAAC,CAAC;AACrB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAM,EAAC,QAAQ,WAAU,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,CAAC;AACzD,UAAI,QAAQ;AACV,mBAAW,KAAK,IAAI;AAAA,MACtB,OAAO;AACL,mBAAW,KAAK,MAAM,MAAM;AAC5B,cAAM,KAAK,UAAU,CAAC,CAAC;AACvB,YAAI,KAAK,YAAY;AACnB,gBAAM,QAAQ,KAAK,YAAY,CAAC,CAAC;AAAA,QACnC;AAAA,MACF;AAGA,UAAI,cAAc,UAAU,CAAC,MAAM,QAAW;AAC5C,cAAM,KAAK,WAAW,WAAW,UAAU,CAAC;AAC5C,cAAM,EAAE,IAAI,UAAU,CAAC;AACvB,YAAI,KAAK,YAAY;AACnB,gBAAM,QAAQ,EAAE,IAAI,YAAY,CAAC;AAAA,QACnC;AACA,YAAI,MAAM,QAAQ;AAChB,cAAI,CAAC,KAAK,UAAU;AAElB,iBAAK,WAAW,cAAc,KAAK,MAAM;AAAA,UAC3C;AACA,gBAAM,OAAO,KAAK,SAAS,IAAI,UAAU;AACzC,cAAI,MAAM;AACR,kBAAM,OAAO,IAAI,IAAI,UAAU,CAAC;AAChC,gBAAI,KAAK,YAAY;AACnB,oBAAM,QAAQ,OAAO,IAAI,IAAI,YAAY,CAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,4BAA4B,OAAO,QAAQ,OAAO,YAAY;AACrE,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,MAAI,YAAY;AACd,UAAM,UAAU,MAAM;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,MAAM,QAAQ,CAAC;AACrB,UAAI,KAAK;AAIP,gBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM;AAAA,MAChD;AAAA,IACF;AACA,UAAM,eAAe,QAAQ;AAC7B,QAAI,cAAc;AAChB,aAAO,KAAK,YAAY,EAAE,QAAQ,SAAO;AACvC,cAAM,MAAM,aAAa,GAAG;AAC5B,YAAI,KAAK;AACP,uBAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAYA,SAAS,iBAAiB,gBAAgB,WAAW;AACnD,QAAM,aAAa,oBAAI,IAAI;AAC3B,aAAW,OAAO,gBAAgB;AAChC,eAAW,IAAI,KAAK;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,aAAW,CAAC,IAAI,IAAI,KAAK,WAAW;AAClC,eAAW,OAAO,MAAM;AACtB,kBAAY,YAAY,KAAK,CAAC,CAAC,EAAE,aAAa;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AAMA,SAAS,cAAc,SAAS;AAC9B,QAAM,KAAK;AACX,QAAM,MAAM,oBAAI,IAAI;AACpB,MAAI,qBAAqB;AACzB,MAAI,cAAc;AAClB,MAAI;AACJ,SAAQ,QAAQ,GAAG,KAAK,OAAO,GAAI;AACjC,UAAM,EAAC,GAAG,GAAG,QAAQ,EAAC,SAAS,KAAI,EAAC,IAAI;AAIxC,QAAI,MAAM,KAAK;AACb;AAAA,IACF,WAAW,CAAC,oBAAoB;AAC9B,UAAI,SAAS;AACX;AACA,YAAI,MAAM;AACR,cAAI,IAAI,aAAa,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,MAAM,KAAK;AACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACnRA,SAAQ,QAAQ,kBAAiB;AACjC,SAAQ,iBAAgB;AA2BxB,SAAS,eAAe,SAAS,SAAS;AACxC,QAAM,OAAO;AAAA,IACX,OAAO,SAAS,SAAS;AAAA,IACzB,OAAO;AAAA,MACL,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,GAAI,SAAS;AAAA,IACf;AAAA,EACF;AACA,SAAO,MAAM,SAAS,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AACxD;AA4BA,SAAS,SAAS,SAAS,SAAS;AAClC,QAAM,IAAI,gBAAgB,SAAS,OAAO;AAC1C,MAAI,EAAE,SAAS;AACb,WAAO,IAAI,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;AAAA,EACzD;AACA,SAAO,IAAI,OAAO,EAAE,SAAS,EAAE,KAAK;AACtC;AAYA,SAAS,gBAAgB,SAAS,SAAS;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,YAAY,SAAS,SAAS,KAAK,OAAO;AAAA,IAC9C,cAAc,KAAK,MAAM;AAAA,IACzB,YAAY,KAAK,MAAM;AAAA,EACzB,CAAC;AACD,QAAM,eAAe,MAAM,WAAW;AAAA,IACpC,uBAAuB,KAAK,MAAM;AAAA,IAClC,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,QAAM,WAAW,UAAU,cAAc;AAAA,IACvC,UAAU,KAAK;AAAA,IACf,qBAAqB,KAAK,MAAM;AAAA,IAChC,eAAe,KAAK;AAAA,IACpB,kBAAkB,KAAK;AAAA,EACzB,CAAC;AACD,QAAM,YAAY,SAAS,UAAU,IAAI;AACzC,QAAM,kBAAkB,UAAU,UAAU,SAAS;AAAA,IACnD,kBAAkB,UAAU;AAAA,IAC5B,gBAAgB,UAAU;AAAA,IAC1B,MAAM;AAAA,EACR,CAAC;AACD,QAAM,mBAAmB,WAAW,gBAAgB,OAAO;AAC3D,QAAM,eAAe,OAAO,iBAAiB,SAAS;AAAA,IACpD,kBAAkB,gBAAgB;AAAA,IAClC,gBAAgB,gBAAgB;AAAA,EAClC,CAAC;AACD,QAAM,UAAU;AAAA,IACd,SAAS,aAAa;AAAA,IACtB,OAAO,GAAG,KAAK,aAAa,MAAM,EAAE,GAAG,KAAK,SAAS,MAAM,EAAE,GAAG,UAAU,KAAK,GAAG,UAAU,QAAQ,QAAQ,IAAI,MAAM,GAAG;AAAA,EAC3H;AACA,MAAI,KAAK,eAAe;AACtB,QAAI,KAAK,sBAAsB,UAAU;AACvC,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAAA,EACF,OAAO;AAEL,UAAM,iBAAiB,aAAa,eAAe,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAEvE,UAAM,YAAY,MAAM,KAAK,aAAa,gBAAgB;AAC1D,UAAM,WAAW,SAAS;AAC1B,UAAM,cAAc,QAAQ,QAAQ,UAAU,KAAK;AACnD,QAAI,eAAe,UAAU,UAAU,UAAU,YAAY,aAAa;AACxE,cAAQ,UAAU;AAAA,QAChB,GAAI,eAAe,UAAU,EAAC,eAAc;AAAA,QAC5C,GAAI,UAAU,UAAU,EAAC,UAAS;AAAA,QAClC,GAAI,YAAY,EAAC,SAAQ;AAAA,QACzB,GAAI,eAAe,EAAC,YAAW;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;", "names": ["range", "raw", "value", "path", "key", "possessive", "atomic", "node", "recursion", "atomic", "possessive"]}