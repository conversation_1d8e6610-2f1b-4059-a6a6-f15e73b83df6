const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const logger = require('../log/logger');

const pool = mysql.createPool({
    host: '*************',
    port: 3306,
    user: 'root',
    password: 'Asuse12.',
    database: 'user_db',
    waitForConnections: true,
    connectionLimit: 1000,
    queueLimit: 0,
    connectTimeout: 10000,
    // 添加连接保活设置
    enableKeepAlive: true,
    keepAliveInitialDelay: 10000
});

// 添加连接错误监听
pool.on('connection', (connection) => {
    logger.info('MySQL新连接建立');
});

pool.on('acquire', (connection) => {
    logger.info('连接从池中获取');
});

pool.on('release', (connection) => {
    logger.info('连接放回池中');
});

pool.on('enqueue', () => {
    logger.info('等待可用连接');
});

const JWT_SECRET = process.env.JWT_SECRET || 'your_secret_key';

// 密码加密函数
async function hashPassword(password) {
    return await bcrypt.hash(password, 10);
}

// 用户注册
async function registerUser({ account, password, nickname, avatar, signature }) {
    try {
        // 账号格式校验
        const accountRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
        if (!accountRegex.test(account)) {
            return { success: false, message: '账号只能包含中文、英文或数字' };
        }

        // 唯一性校验
        const [existing] = await pool.execute(
            'SELECT id FROM users WHERE account = ?',
            [account]
        );

        if (existing.length > 0) {
            return { success: false, message: '账号已存在' };
        }

        // 密码复杂度校验
        const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/;
        if (!passwordRegex.test(password)) {
            return { success: false, message: '密码需至少6位且包含字母和数字' };
        }

        const hashedPassword = await hashPassword(password);
        const [result] = await pool.execute(
            'INSERT INTO users (account, password, nickname, avatar, signature) VALUES (?, ?, ?, ?, ?)',
            [
                account,
                hashedPassword,
                'pandaman', // 临时占位昵称
                avatar || '/images/default-avatar.jpg',
                signature || '这个人很懒，什么都没写~'
            ]
        );

        // 默认昵称为 pandaman+用户ID
        const userId = result.insertId;
        await pool.execute(
            'UPDATE users SET nickname = ? WHERE id = ?',
            [`pandaman${userId}`, userId]
        );

        logger.info('注册成功:', {
            account,
            userId,
            avatar,
            signature
        });

        return {
            success: true,
            user: {
                id: userId,
                account,
                nickname: `pandaman${userId}`,
                avatar: avatar || '/images/default-avatar.jpg',
                signature: signature || '这个人很懒，什么都没写~'
            }
        };
    } catch (error) {
        logger.error('注册失败:', error);
        return { success: false, message: '注册失败' };
    }
}


// 用户登录
async function loginUser({ account, password }) {
    let connection;
    try {
        // 显式获取连接
        connection = await pool.getConnection();

        // 添加连接有效性检查
        await connection.ping();

        const [users] = await connection.execute(
            'SELECT * FROM users WHERE account = ?',
            [account]
        );

        if (users.length === 0) {
            return { success: false, message: '账号不存在' };
        }

        const user = users[0];
        const isMatch = await bcrypt.compare(password, user.password);

        if (!isMatch) {
            return { success: false, message: '密码错误' };
        }

        const token = jwt.sign(
            { userId: user.id, account: user.account },
            JWT_SECRET,
            { expiresIn: '7d' }
        );

        logger.info('登录成功:', {
            account,
            userId: user.id,
            token,
            avatar: user.avatar,
            signature: user.signature
        });

        return {
            success: true,
            token,
            user: {
                id: user.id,
                account: user.account,
                nickname: user.nickname,
                avatar: user.avatar,
                signature: user.signature
            }
        };
    } catch (error) {
        logger.error('完整登录错误:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            time: new Date().toISOString()
        });

        // 特殊处理连接错误
        if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
            // 尝试重新连接
            if (connection) connection.destroy();
            return loginUser({ account, password }); // 重试一次
        }

        return { success: false, message: '登录服务暂时不可用' };
    } finally {
        if (connection) connection.release();
    }
}

// 获取用户资料
async function getUserProfile(userId) {
    try {
        const [rows] = await pool.query(
            'SELECT id, username, email, avatar, created_at FROM users WHERE id = ?',
            [userId]
        );

        if (rows.length === 0) {
            return { success: false, message: '用户不存在' };
        }

        logger.info('获取用户资料成功:', {
            userId,
            data: rows[0]
        });

        return { success: true, data: rows[0] };
    } catch (error) {
        logger.error('获取用户资料失败:', error);
        return { success: false, message: '获取用户资料失败' };
    }
}

// 更新用户资料
async function updateUserProfile(profileData) {
    try {
        // 字段白名单验证
        const validFields = ['nickname', 'signature', 'avatar'];
        const updates = [];
        const values = [];

        Object.entries(profileData).forEach(([key, value]) => {
            if (validFields.includes(key) && value !== undefined) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        });

        if (updates.length === 0) {
            return { success: false, message: '无有效更新字段' };
        }

        values.push(profileData.userId);
        const query = `UPDATE users SET ${updates.join(', ')} WHERE id = ?`;

        const [result] = await pool.query(query, values);

        logger.info('更新用户资料成功:', {
            userId: profileData.userId,
            updated: result.affectedRows,
            data: profileData
        });

        return {
            success: true,
            updated: result.affectedRows,
            message: '资料更新成功'
        };
    } catch (error) {
        logger.error('更新用户资料失败:', error);
        return {
            success: false,
            message: '数据库更新失败: ' + error.message
        };
    }
}

// 修改密码
async function changeUserPassword({ userId, oldPassword, newPassword }) {
    try {
        // 验证旧密码
        if (!userId || !oldPassword || !newPassword) {
            throw new Error('缺少必要参数')
        }

        const [rows] = await pool.query(
            'SELECT password FROM users WHERE id = ?',
            [userId]
        );

        if (rows.length === 0) {
            return { success: false, message: '用户不存在' };
        }

        // 比较新旧密码
        const isMatch = await bcrypt.compare(oldPassword, rows[0].password);
        if (!isMatch) return { success: false, message: '旧密码不正确' };

        // 更新密码
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await pool.query('UPDATE users SET password = ? WHERE id = ?', [hashedPassword, userId]);

        logger.info('修改密码成功:', {
            userId,
            message: '密码修改成功'
        });

        return { success: true, message: '密码修改成功' };
    } catch (error) {
        logger.error('修改密码失败:', error);
        return { success: false, message: error.message || '修改密码失败' };
    }
}

// 配置头像上传存储
const avatarStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../../public/uploads/avatars');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const userId = req.body.userId;
        const ext = path.extname(file.originalname);

        if (!userId) {
            return cb(new Error('缺少用户ID参数'));
        }
        cb(null, `avatar_${userId}${ext}`);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支持的图片格式'), false);
    }
};

// 头像上传中间件
const uploadAvatar = multer({
    storage: avatarStorage,
    limits: { fileSize: 10 * 1024 * 1024 },
    fileFilter
}).single('avatarFile');

/**
 * 更新用户头像
 * @param {Object} params - 参数对象
 * @param {string} params.userId - 用户ID
 * @param {Object} params.file - 上传的文件对象
 * @returns {Promise<Object>} 返回操作结果
 */
async function updateUserAvatar({ userId, file }) {
    try {
        if (!userId) {
            throw new Error('缺少用户ID参数');
        }
        if (!file) {
            throw new Error('没有接收到头像文件');
        }
        // 文件已由multer中间件处理并保存，这里只需处理数据库更新
        const fileName = `avatar_${userId}${path.extname(file.originalname)}`;
        const avatarUrl = `/uploads/avatars/${fileName}`;

        // 更新数据库
        await pool.query(
            'UPDATE users SET avatar = ? WHERE id = ?',
            [avatarUrl, userId]
        );

        logger.info('更新头像成功:', {
            userId,
            avatarUrl
        });

        return {
            success: true,
            message: '头像更新成功',
            avatarUrl
        };
    } catch (error) {
        logger.error('更新头像失败:', error);
        return { success: false, message: error.message || '更新头像失败' };
    }
}

// 数据库健康检查
async function dbHealthCheck(req, res) {
    try {
        const [rows] = await pool.query('SELECT 1 AS status');
        res.json({
            dbStatus: rows[0].status === 1 ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(503).json({
            dbStatus: 'down',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
}

// 用户注册接口
async function userRegister(req, res) {
    const result = await registerUser(req.body);
    res.json(result);
}

// 用户登录接口
async function userLogin(req, res) {
    const result = await loginUser(req.body);
    res.json(result);
}

// 获取用户资料接口
async function userInfo(req, res) {
    try {
        const token = req.headers.authorization?.split(' ')[1]
        if (!token) {
            return res.status(401).json({ success: false, message: '未提供身份凭证' })
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_secret_key')
        const [users] = await pool.execute(
            'SELECT id, account, nickname, avatar, signature FROM users WHERE id = ?',
            [decoded.userId]
        )

        res.json({
            success: true,
            user: users[0] || null
        });
    } catch (error) {
        logger.error('用户信息查询错误:', error);
        res.status(401).json({ success: false, message: '身份验证失败' });
    }
}

// 用户中心相关接口
async function userProfile(req, res) {
    const userId = req.query.userId;
    const result = await getUserProfile(userId);
    res.json(result);
}

// 更新用户资料接口
async function userUpdate(req, res) {
    const result = await updateUserProfile(req.body);
    res.json(result);
}

// 修改密码接口
async function userUpdatePassword(req, res, next) {
    const { userId, oldPassword, newPassword } = req.body
    if (!userId || !oldPassword || !newPassword) {
        return res.status(400).json({
            success: false,
            message: '缺少必要参数'
        })
    }

    try {
        const result = await changeUserPassword(req.body)
        res.json(result)
    } catch (error) {
        logger.error('密码修改失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        })
    }
}

// 头像上传接口
async function userUploadAvatar(req, res) {
    try {
        logger.debug('调试信息 - 接收到的参数:', {
            headers: req.headers,
            body: req.body,
            file: req.file
        });
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '未收到文件'
            });
        }
        const result = await updateUserAvatar({
            userId: req.body.userId,
            file: req.file
        });
        res.json(result);
    } catch (error) {
        logger.error('头像上传失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '头像上传失败'
        });
    }
}

async function addOnePlan({ account, create_time, days }) {
    try {
        // 去重
        const [existing] = await pool.execute(
            'SELECT * FROM travel_guides WHERE account = ? AND create_time = ?',
            [account, create_time]
        );
        if (existing.length > 0) {
            return { success: false, msg: '本条用户规划已存在' }; // 已存在相同的旅游规划
        }

        const [result] = await pool.execute(
            'INSERT INTO travel_guides (account, create_time, days) VALUES (?, ?, ?)',
            [
                account,
                create_time,
                days
            ]
        );

        logger.info('添加用户旅游规划成功:', {
            account,
            create_time,
            days
        });

        return { success: true, msg: '添加用户旅游规划成功' };
    } catch (error) {
        logger.error('添加用户旅游规划失败:', error);
        return { success: false, msg: '添加用户旅游规划失败' };
    }
}

// 查询用户所有旅游规划
async function getAllPlans({ account }) {
    try {
        const [result] = await pool.execute(
            'SELECT * FROM travel_guides WHERE account = ?',
            [account]
        );

        logger.info('查询用户旅游规划成功:', {
            account,
            data: result
        });

        return { success: true, msg: '查询成功', data: result };
    } catch (error) {
        logger.error('查询用户旅游规划失败:', error);
        return { success: false, msg: '查询用户旅游规划失败', data: [] };
    }
}

// 获取用户攻略详细信息（包含MD文件内容）
async function getUserGuideDetails({ account }) {
    try {
        // 读取文件系统中的MD文件和图片
        const publicPath = path.join(__dirname, 'public', account);

        if (!fs.existsSync(publicPath)) {
            return { success: true, msg: '用户目录不存在', data: [] };
        }

        const files = fs.readdirSync(publicPath);

        // 按创建时间分组攻略
        const groupedGuides = {};

        // 查找所有plan文件
        const planFiles = files.filter(file =>
            file.startsWith('plan-') && file.endsWith('.md')
        );

        // 查找所有map文件
        const mapFiles = files.filter(file =>
            file.startsWith('map-') && file.endsWith('.png')
        );

        // 处理plan文件
        for (const planFile of planFiles) {
            const filePath = path.join(publicPath, planFile);
            const content = fs.readFileSync(filePath, 'utf-8');

            // 从文件名提取创建时间和天数：plan-**************-1.md
            const fileMatch = planFile.match(/plan-(\d+)-(\d+)\.md$/);
            if (!fileMatch) continue;

            const createTimeStr = fileMatch[1]; // **************
            const dayNumber = parseInt(fileMatch[2]); // 1

            // 解析创建时间：************** -> 2025-05-02 23:57:15
            const year = createTimeStr.substring(0, 4);
            const month = createTimeStr.substring(4, 6);
            const day = createTimeStr.substring(6, 8);
            const hour = createTimeStr.substring(8, 10);
            const minute = createTimeStr.substring(10, 12);
            const second = createTimeStr.substring(12, 14);
            const createTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

            // 提取标题（第二个标题）
            const titleMatch = content.match(/^## (.+)$/m);
            const title = titleMatch ? titleMatch[1].trim() : `第${dayNumber + 1}天攻略`;

            // 初始化分组
            if (!groupedGuides[createTimeStr]) {
                groupedGuides[createTimeStr] = {
                    createTime: createTime,
                    createTimeStr: createTimeStr,
                    plans: [],
                    maps: [],
                    totalDays: 0,
                    startLocation: '',
                    endLocation: ''
                };
            }

            groupedGuides[createTimeStr].plans.push({
                day: dayNumber,
                title: title,
                content: content,
                fileName: planFile
            });
        }

        // 处理map文件
        for (const mapFile of mapFiles) {
            const fileMatch = mapFile.match(/map-(\d+)-(\d+)\.png$/);
            if (!fileMatch) continue;

            const createTimeStr = fileMatch[1];
            const dayNumber = parseInt(fileMatch[2]);

            if (groupedGuides[createTimeStr]) {
                groupedGuides[createTimeStr].maps.push({
                    day: dayNumber,
                    fileName: mapFile,
                    url: `${process.env.BACKEND_SRV_URL || 'http://localhost:3999'}/api/public/${account}/${mapFile}`
                });
            }
        }

        // 处理每个分组的数据
        for (const [createTimeStr, guideGroup] of Object.entries(groupedGuides)) {
            // 按天数排序
            guideGroup.plans.sort((a, b) => a.day - b.day);
            guideGroup.maps.sort((a, b) => a.day - b.day);

            // 计算总天数
            guideGroup.totalDays = guideGroup.plans.length;

            // 从第一天的标题中提取起点和终点
            if (guideGroup.plans.length > 0) {
                const firstPlanTitle = guideGroup.plans[0].title;
                // 解析 "重庆 到 深圳" 格式
                const locationMatch = firstPlanTitle.match(/(.+?)\s*到\s*(.+)/);
                if (locationMatch) {
                    guideGroup.startLocation = locationMatch[1].trim();
                    guideGroup.endLocation = locationMatch[2].trim();
                }
            }
        }

        // 转换为数组，过滤掉没有plans的攻略，并按创建时间排序（最新的在前）
        const result = Object.values(groupedGuides)
            .filter(guide => guide.plans.length > 0) // 只返回有实际内容的攻略
            .sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

        logger.info('获取用户攻略详情成功:', {
            account,
            guidesCount: result.length
        });

        return { success: true, msg: '获取成功', data: result };
    } catch (error) {
        logger.error('获取用户攻略详情失败:', error);
        return { success: false, msg: '获取攻略详情失败', data: [] };
    }
}

module.exports = {
    registerUser,
    loginUser,
    getUserProfile,
    updateUserProfile,
    updateUserAvatar,
    changeUserPassword,
    uploadAvatar,
    dbHealthCheck,
    userRegister,
    userLogin,
    userInfo,
    userProfile,
    userUpdate,
    userUpdatePassword,
    userUploadAvatar,
    addOnePlan,
    getAllPlans,
    getUserGuideDetails,
    pool
};
