// 本文件提供爬虫功能
const puppeteer = require('puppeteer');
const logger = require('../log/logger');
const cheerio = require('cheerio');

// 随机延迟函数
const randomDelay = (min, max) => {
    const delay = Math.floor(Math.random() * (max - min + 1) + min);
    return new Promise(resolve => setTimeout(resolve, delay));
};

// 用户代理列表
const USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
];

// 用户代理会在每个函数中随机选择

// 搜索引擎配置
const SEARCH_ENGINES = [
    {
        name: '360搜索',
        baseUrl: 'https://so.com/s?q=',
        selectors: [
            '#main > ul > li:nth-child(1) > h3 > a'
        ],
        encoding: 'utf-8'
    },
    {
        name: 'Bing',
        baseUrl: 'https://cn.bing.com/search?q=',
        selectors: [
            '#b_results > li:nth-child(1) > h2 > a'
        ],
        encoding: 'utf-8'
    },
    {
        name: 'Sogou',
        baseUrl: 'https://sogou.com/web?query=',
        selectors: [
            '#main > div:nth-child(4) > div > div:nth-child(3) > div.struct201102 > h3 > a'
        ],
        encoding: 'utf-8'
    }
];

// 重试函数
const retry = async (fn, retries = 3, delay = 1000, increaseFactor = 2) => {
    try {
        return await fn();
    } catch (error) {
        if (retries <= 0) {
            throw error;
        }
        logger.info(`操作失败，${retries}次重试后重新尝试...`);
        await randomDelay(delay, delay * 2);
        return retry(fn, retries - 1, delay * increaseFactor, increaseFactor);
    }
};

// 使用单个搜索引擎尝试爬取酒店信息
async function trySearchEngine(hotelName, searchEngine, browser) {
    const page = await browser.newPage();

    try {
        logger.info(`尝试使用 ${searchEngine.name} 搜索引擎爬取酒店信息: ${hotelName}`);

        // 使用随机用户代理
        const userAgent = USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
        await page.setUserAgent(userAgent);

        // 设置更多的请求头，模拟真实浏览器
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        });

        // 导航超时时间8s
        page.setDefaultNavigationTimeout(8000);

        // 模拟真实浏览器环境
        await page.evaluateOnNewDocument(() => {
            // 覆盖 WebDriver 检测
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });

            // 覆盖 navigator 属性
            const newProto = navigator.__proto__;
            delete newProto.webdriver;

            // 添加语言和其他属性
            navigator.languages = ['zh-CN', 'zh', 'en-US', 'en'];

            // 创建一个看起来像真实插件的对象
            const mockPlugins = [
                { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: 'Portable Document Format' },
                { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
            ];

            // 使用 Object.defineProperty 替代直接赋值
            Object.defineProperty(navigator, 'plugins', {
                get: () => mockPlugins,
                enumerable: true,
                configurable: true
            });
        });

        // 随机延迟，模拟人类行为
        await randomDelay(500, 1000);

        const searchQuery = `${hotelName} site:ctrip.com`;
        const searchUrl = `${searchEngine.baseUrl}${encodeURIComponent(searchQuery)}`;

        logger.info(`访问搜索URL: ${searchUrl}`);

        // 使用更安全的导航方式
        await page.goto(searchUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 10000
        });

        const pageContent = await page.content();
        logger.info(`等待搜索结果加载成功`);

        // 随机延迟
        await randomDelay(500, 1000);

        // 模拟更自然的鼠标移动和滚动行为
        await page.mouse.move(Math.random() * 500, Math.random() * 300);

        // 随机滚动页面
        await page.evaluate(() => {
            window.scrollBy(0, Math.floor(Math.random() * 500));
        });

        await randomDelay(500, 1000);

        // 使用 cheerio 解析页面内容
        const $ = cheerio.load(pageContent);

        let hotelLink = null;
        for (const selector of searchEngine.selectors) {
            const elements = $(selector);
            if (elements.length > 0) {
                for (const element of elements) {
                    if (searchEngine.name === '360搜索') {
                        const href = $(element).attr('data-mdurl');
                        if (href && href.includes('ctrip')) {
                            hotelLink = href;
                            break;
                        }
                    } else {
                        const href = $(element).attr('href');
                        if (href && href.includes('ctrip')) {
                            hotelLink = href;
                            break;
                        }
                    }
                }
            }
        }

        if (!hotelLink) {
            throw new Error(`在 ${searchEngine.name} 中未找到酒店相关链接`);
        }

        logger.info(`使用 ${searchEngine.name} 成功找到酒店链接: ${hotelLink}`);
        return hotelLink;
    } catch (err) {
        logger.error(`使用${searchEngine.name}查询${hotelName}出现异常：${err}`);
        throw err;
    }
    finally {
        await page.close();
    }
}

// 直接使用 puppeteer 进行相关信息的查询截图并给出页面链接
async function scrapeHotelInfo(hotelName, account, create_time, day, shouldAbort) {
    logger.info(`开始爬取酒店信息:${hotelName}, ${account}, ${create_time}, ${day}`);

    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--window-size=1920,1080',
        ],
        defaultViewport: {
            width: 1920,
            height: 1080
        },
        timeout: 3000 // 默认超时时长3s，太长影响使用感受
    });

    try {
        let lastError = null;

        // 依次尝试每个搜索引擎
        for (let i = 0; i < SEARCH_ENGINES.length; i++) {
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeHotelInfo: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            const searchEngine = SEARCH_ENGINES[i];

            try {
                let hotelLink = await trySearchEngine(hotelName, searchEngine, browser);

                // 如果成功找到链接，立即返回
                await browser.close();
                logger.info(`爬取酒店信息成功:${hotelName}, ${account}, ${create_time}, ${day}, ${hotelLink}`);

                // 不同的 SEARCH_ENGINES 对 href 做了特殊处理，这里也要进行适配
                if (searchEngine.name === 'Sogou') {
                    hotelLink = 'https://sogou.com' + hotelLink;
                }
                return hotelLink;
            } catch (error) {
                lastError = error;
                logger.warn(`使用 ${searchEngine.name} 搜索失败: ${error.message}`);

                // 如果不是最后一个搜索引擎，等待一段时间后继续尝试下一个
                if (i < SEARCH_ENGINES.length - 1) {
                    logger.info(`尝试下一个搜索引擎...`);
                }
            }
        }

        // 所有搜索引擎都失败了
        await browser.close();
        logger.error(`所有搜索引擎都无法找到酒店信息:${hotelName}, ${account}, ${create_time}, ${day}`);
        throw new Error(`所有搜索引擎都无法找到酒店信息。最后一个错误: ${lastError?.message || '未知错误'}`);

    } catch (error) {
        await browser.close();
        logger.error(`爬取酒店信息失败:${hotelName}, ${account}, ${create_time}, ${day}, ${error.message}`);
        throw error;
    }
}

exports.hotelInfo = async (req, res) => {
    const { name, user, create_time, day } = req.body;

    if (!name || !user || !create_time || !day) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    // 新增：用于中止信号
    let aborted = false;
    req.on('close', () => {
        aborted = true;
    });

    try {
        const url = await scrapeHotelInfo(name, user, create_time, day, () => aborted);
        if (aborted) {
            logger.warn('请求已中断，未返回结果');
            return;
        }
        res.json({ success: true, url: url });
    } catch (error) {
        if (aborted) {
            logger.warn('请求已中断，未返回错误');
            return;
        }
        logger.error(`爬取酒店信息失败:${req.body}, ${error.message}`);
        res.status(500).json({ error: '爬取酒店信息失败', details: error.message });
    }
};

// 通过美食名获取美食照片
async function scrapeFoodPhoto(scenicName, shouldAbort) {
    logger.info(`开始爬取美食照片:${scenicName}`);

    // 使用重试机制包装主要爬取逻辑
    return retry(async () => {
        if (shouldAbort && shouldAbort()) {
            logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
            throw new Error('请求已中断');
        }
        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--window-size=1920,1080',
            ],
            defaultViewport: {
                width: 1920,
                height: 1080
            },
            timeout: 60000 // 增加启动超时时间到60秒
        });

        const page = await browser.newPage();

        try {
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 使用随机用户代理
            const userAgent = USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
            await page.setUserAgent(userAgent);

            // 设置更多的请求头，模拟真实浏览器
            await page.setExtraHTTPHeaders({
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            });

            // 设置更长的导航超时时间
            page.setDefaultNavigationTimeout(8000); // 8秒

            // 模拟真实浏览器环境
            await page.evaluateOnNewDocument(() => {
                // 覆盖 WebDriver 检测
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 覆盖 navigator 属性
                const newProto = navigator.__proto__;
                delete newProto.webdriver;

                // 添加语言和其他属性
                navigator.languages = ['zh-CN', 'zh', 'en-US', 'en'];

                // 创建一个看起来像真实插件的对象
                const mockPlugins = [
                    { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                    { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: 'Portable Document Format' },
                    { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
                ];

                // 使用 Object.defineProperty 替代直接赋值
                Object.defineProperty(navigator, 'plugins', {
                    get: () => mockPlugins,
                    enumerable: true,
                    configurable: true
                });
            });

            // 随机延迟，模拟人类行为
            await randomDelay(1000, 3000);
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            const searchQuery = `${scenicName}`;

            // 使用更安全的导航方式
            await page.goto(`https://image.so.com/i?q=${encodeURIComponent(searchQuery)}`, {
                waitUntil: 'networkidle2',
                timeout: 60000
            });
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 随机延迟
            await randomDelay(2000, 5000);
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 模拟更自然的鼠标移动和滚动行为
            await page.mouse.move(Math.random() * 500, Math.random() * 300);
            await page.mouse.move(Math.random() * 500, Math.random() * 300, { steps: 10 }); // 缓慢移动

            // 随机滚动页面
            await page.evaluate(() => {
                window.scrollBy(0, Math.floor(Math.random() * 500));
            });

            await randomDelay(1000, 2000);

            // 再次滚动
            await page.evaluate(() => {
                window.scrollBy(0, Math.floor(Math.random() * 300));
            });

            // 随机点击页面某处
            await page.mouse.click(100 + Math.random() * 200, 100 + Math.random() * 100);

            // 等待搜索结果加载，增加超时时间
            await page.waitForSelector('.item.item_expansion .entity .img img', { timeout: 30000 });

            // 随机延迟
            await randomDelay(1000, 3000);
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeFoodPhoto: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 获取图片链接
            const photoLink = await page.evaluate(() => {
                const images = Array.from(document.querySelectorAll('.item.item_expansion .entity .img img'));
                return images.length > 0 ? images[0].src : null;
            });
            if (!photoLink) {
                logger.error(`未找到美食照片, ${scenicName}`);
                throw new Error('未找到美食照片');
            }
            // 随机延迟后关闭浏览器
            await randomDelay(1000, 2000);
            await browser.close();
            logger.info(`爬取美食照片成功:${scenicName}, ${photoLink}`);
            return photoLink;
        } catch (error) {
            logger.error(`爬取美食照片失败:${scenicName}, ${error.message}`);
            await browser.close();
            throw error;
        }
    }, 3, 2000, 2); // 最多重试3次，初始延迟2秒，每次延迟翻倍
}

// 通过美食名获取美食照片
exports.foodPhoto = async (req, res) => {
    const { scenicName } = req.body;

    if (!scenicName) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    let aborted = false;
    req.on('close', () => {
        aborted = true;
    });

    try {
        const photoLink = await scrapeFoodPhoto(scenicName, () => aborted);
        if (aborted) {
            logger.warn('请求已中断，未返回结果');
            return;
        }
        res.json({ success: true, photo: photoLink });
    } catch (error) {
        if (aborted) {
            logger.warn('请求已中断，未返回错误');
            return;
        }
        logger.error(`爬取美食照片失败:${req.body}, ${error.message}`);
        res.status(500).json({ error: '爬取美食照片失败', details: error.message });
    }
};

// 通过景点名获取景点照片
async function scrapeImageUrl(viewName, shouldAbort) {
    logger.info(`开始爬取景点图片链接: ${viewName}`);

    // 使用重试机制包装主要爬取逻辑
    return retry(async () => {
        if (shouldAbort && shouldAbort()) {
            logger.warn('scrapeImageUrl: 检测到请求已中断，主动停止');
            throw new Error('请求已中断');
        }
        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--window-size=1920,1080',
            ],
            defaultViewport: {
                width: 1920,
                height: 1080
            },
            timeout: 5000 // 启动超时时间到5秒
        });

        const page = await browser.newPage();

        try {
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeImageUrl: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 使用随机用户代理
            const userAgent = USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
            await page.setUserAgent(userAgent);

            // 设置更多的请求头，模拟真实浏览器
            await page.setExtraHTTPHeaders({
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            });

            // 设置更长的导航超时时间
            page.setDefaultNavigationTimeout(8000); // 8秒

            // 模拟真实浏览器环境
            await page.evaluateOnNewDocument(() => {
                // 覆盖 WebDriver 检测
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 覆盖 navigator 属性
                const newProto = navigator.__proto__;
                delete newProto.webdriver;

                // 添加语言和其他属性
                navigator.languages = ['zh-CN', 'zh', 'en-US', 'en'];

                // 创建一个看起来像真实插件的对象
                const mockPlugins = [
                    { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                    { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: 'Portable Document Format' },
                    { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
                ];

                // 使用 Object.defineProperty 替代直接赋值
                Object.defineProperty(navigator, 'plugins', {
                    get: () => mockPlugins,
                    enumerable: true,
                    configurable: true
                });
            });

            // 随机延迟，模拟人类行为
            await randomDelay(500, 1000);
            // 搜索目标内容
            const searchQuery = `${viewName}`;

            // 使用更安全的导航方式
            await page.goto(`https://cn.bing.com/images/search?q=${encodeURIComponent(searchQuery)}&qft=+filterui:imagesize-medium&form=IRFLTR&first=1`, {
                waitUntil: 'domcontentloaded',
                timeout: 10000
            });

            logger.info(`访问搜索URL: ${searchQuery}`);

            // 随机延迟
            await randomDelay(500, 1000);
            // 模拟更自然的鼠标移动和滚动行为
            await page.mouse.move(Math.random() * 500, Math.random() * 300);
            // await page.mouse.move(Math.random() * 500, Math.random() * 300, { steps: 10 }); // 缓慢移动

            // 随机滚动页面
            await page.evaluate(() => {
                window.scrollBy(0, Math.floor(Math.random() * 500));
            });

            await randomDelay(500, 1000);

            // 再次滚动
            // await page.evaluate(() => {
            //     window.scrollBy(0, Math.floor(Math.random() * 300));
            // });

            // 随机点击页面某处
            // await page.mouse.click(100 + Math.random() * 200, 100 + Math.random() * 100);

            // 等待目标元素加载，增加超时时间
            await page.waitForSelector('a.iusc', { timeout: 5000 });

            logger.info(`等待目标元素加载成功`);

            // 随机延迟
            await randomDelay(500, 1000);
            if (shouldAbort && shouldAbort()) {
                logger.warn('scrapeImageUrl: 检测到请求已中断，主动停止');
                await browser.close();
                throw new Error('请求已中断');
            }
            // 提取图片链接
            const imageMurl = await page.evaluate(() => {
                const elements = Array.from(document.querySelectorAll('a.iusc'));
                for (const element of elements) {
                    const turl = element.getAttribute('m');
                    if (turl) {
                        try {
                            const parsedTurl = JSON.parse(turl).turl;
                            if (parsedTurl) return parsedTurl;
                        } catch (e) {
                            // 解析错误，继续尝试下一个元素
                            continue;
                        }
                    }
                }
                return null;
            });
            if (!imageMurl) {
                logger.error(`未找到景点图片链接: ${viewName}`);
                throw new Error('未找到景点图片链接');
            }
            await browser.close();
            logger.info(`爬取景点图片链接成功: ${viewName}, ${imageMurl}`);
            return imageMurl;
        } catch (error) {
            logger.error(`爬取景点图片链接失败: ${viewName}, ${error.message}`);
            await browser.close();
            throw error;
        }
    }, 2, 1000, 2); // 最多重试2次，初始延迟1秒，每次延迟翻倍
}

exports.viewPhoto = async (req, res) => {
    const { name } = req.body;

    if (!name) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    let aborted = false;
    req.on('close', () => {
        aborted = true;
    });

    try {
        const photoLink = await scrapeImageUrl(name, () => aborted);
        if (aborted) {
            logger.warn('请求已中断，未返回结果');
            return;
        }
        res.json({ success: true, url: photoLink });
    } catch (error) {
        if (aborted) {
            logger.warn('请求已中断，未返回错误');
            return;
        }
        logger.error(`爬取景点照片失败:${req.body}, ${error.message}`);
        res.status(500).json({ error: '爬取景点照片失败', details: error.message });
    }
};