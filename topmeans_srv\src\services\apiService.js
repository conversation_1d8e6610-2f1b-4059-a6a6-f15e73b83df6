const axios = require('axios');
const fs = require('fs');
const path = require('path');
const logger = require('../log/logger');

exports.getAMapKeys = async (req, res) => {
    const AMAP_CODE = process.env.AMAP_CODE
    const AMAP_KEY = process.env.AMAP_API_KEY;
    if (!AMAP_KEY || !AMAP_CODE) {
        logger.error('API 密钥未配置');
        return res.status(500).json({ error: 'API 密钥未配置' });
    }
    res.json({ success: true, AMAP_CODE, AMAP_KEY });
};

exports.getAMapScriptUrl = (req, res) => {
    const API_URL = process.env.AMAP_API_URL;
    if (!API_URL) {
        logger.error('API 密钥未配置');
        return res.status(500).json({ error: 'API 密钥未配置' });
    }
    return API_URL;
};

exports.callExternalDSAPI = async (req, res) => {
    const { msg } = req.body;
    const API_URL = process.env.DS_API_URL;
    const API_KEY = process.env.DS_API_KEY;

    try {
        const response = await axios.post(
            API_URL,
            {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'user',
                        content: msg
                    }
                ],
                stream: true,
                temperature: 0.7,
                max_tokens: 8192
            },
            {
                headers: {
                    'Authorization': `Bearer ${API_KEY}`,
                    'Content-Type': 'application/json'
                },
                responseType: 'stream' // 确保支持流式传输
            }
        );
        response.data.pipe(res);
    } catch (error) {
        logger.error(`DS API 调用失败: ${error.message}`);
        res.status(500).json({ error: 'DS API 调用失败', details: error.message });
    }
};

exports.savePlan = async (req, res) => {
    const { content, user, filename } = req.body;

    if (!content || !filename || !user) {
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    const PLAN_SAVE_DIR = path.join(__dirname, 'public', user);

    if (!fs.existsSync(PLAN_SAVE_DIR)) {
        fs.mkdirSync(PLAN_SAVE_DIR, { recursive: true });
    }

    // 保存到固定目录
    const filePath = path.join(PLAN_SAVE_DIR, filename);
    fs.writeFile(filePath, content, (err) => {
        if (err) {
            logger.error('保存内容失败:', err);
            return res.status(500).json({ error: '保存内容失败' });
        }

        res.json({ success: true, filePath: `/${user}/${filename}` });
    });
};

exports.saveAmapImg = async (req, res) => {
    const { image, user, filename } = req.body;

    if (!image || !filename || !user) {
        logger.error('缺少必要的参数:', { image, filename, user });
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    const IMAGE_SAVE_DIR = path.join(__dirname, 'public', user);

    if (!fs.existsSync(IMAGE_SAVE_DIR)) {
        fs.mkdirSync(IMAGE_SAVE_DIR, { recursive: true });
    }

    // 去掉 Base64 数据的前缀
    const base64Data = image.replace(/^data:image\/png;base64,/, '');

    // 保存图片到固定目录
    const filePath = path.join(IMAGE_SAVE_DIR, filename);
    fs.writeFile(filePath, base64Data, 'base64', (err) => {
        if (err) {
            logger.error('保存图片失败:', err);
            return res.status(500).json({ error: '保存图片失败' });
        }

        res.json({ success: true, filePath: `/${user}/${filename}` });
    });
};
