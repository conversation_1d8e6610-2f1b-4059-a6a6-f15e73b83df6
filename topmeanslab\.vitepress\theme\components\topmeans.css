/* ===== 主容器样式 ===== */
.travel-planning-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    background: linear-gradient(135deg,
        rgba(99, 102, 241, 0.05) 0%,
        rgba(168, 85, 247, 0.05) 50%,
        rgba(236, 72, 153, 0.05) 100%);
}

/* ===== 页面标题区域 ===== */
.page-header {
    position: relative;
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    overflow: hidden;
}

.header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 1rem 0;
    letter-spacing: -0.02em;
}

.title-icon {
    width: clamp(2rem, 5vw, 3rem);
    height: clamp(2rem, 5vw, 3rem);
    stroke: #667eea;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
}

.page-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: var(--vp-c-text-2);
    margin: 0;
    font-weight: 400;
    opacity: 0.8;
}

.header-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    pointer-events: none;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
    animation: float 6s ease-in-out infinite;
}

.decoration-circle:nth-child(1) {
    width: 200px;
    height: 200px;
    top: -100px;
    left: -100px;
    animation-delay: 0s;
}

.decoration-circle:nth-child(2) {
    width: 150px;
    height: 150px;
    top: -75px;
    right: -200px;
    animation-delay: 2s;
}

.decoration-circle:nth-child(3) {
    width: 100px;
    height: 100px;
    bottom: -150px;
    left: -50px;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* ===== 旅游表单样式 ===== */
.travel-form {
    background: var(--vp-c-bg);
    border-radius: 24px;
    padding: 3rem;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid var(--vp-c-divider-light);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.travel-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        #667eea 0%,
        #764ba2 50%,
        #667eea 100%);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

/* ===== 进度指示器 ===== */
.form-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
    padding: 1.5rem 0;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--vp-c-bg-soft);
    border: 2px solid var(--vp-c-divider);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--vp-c-text-2);
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.step-label {
    font-size: 0.75rem;
    color: var(--vp-c-text-2);
    font-weight: 500;
    text-align: center;
}

.progress-step.active .step-label {
    color: var(--vp-c-brand);
    font-weight: 600;
}

.progress-line {
    width: 80px;
    height: 2px;
    background: var(--vp-c-divider);
    margin: 0 1rem;
    position: relative;
    overflow: hidden;
    transition: background 0.3s ease;
}

.progress-line.active {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.progress-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    animation: progress-flow 2s ease-in-out infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-line:not(.active)::after {
    opacity: 1;
}

@keyframes progress-flow {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== 区域标题样式 ===== */
.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--vp-c-text-1);
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--vp-c-divider-light);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

.section-icon {
    width: 24px;
    height: 24px;
    stroke: var(--vp-c-brand);
    stroke-width: 2;
}

/* ===== 地点选择区域 ===== */
.location-section {
    margin-bottom: 3rem;
}

.location-inputs {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: end;
}

.location-item {
    position: relative;
}

.route-connector {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    margin-bottom: 1rem;
}

.route-connector svg {
    width: 24px;
    height: 24px;
    stroke: white;
    stroke-width: 2.5;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== 表单项样式 ===== */
.form-item {
    width: 100%;
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--vp-c-text-1);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.label-icon {
    width: 18px;
    height: 18px;
    stroke: var(--vp-c-brand);
    stroke-width: 2;
}

/* ===== 输入框样式 ===== */
.input-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--vp-c-divider-light);
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text-1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    position: relative;
    z-index: 2;
}

.form-input:focus {
    outline: none;
    border-color: var(--vp-c-brand);
    background: var(--vp-c-bg);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.form-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--vp-c-bg-mute);
}

.input-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05),
        rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 12px;
}

.input-wrapper:hover .input-decoration {
    opacity: 1;
}

/* ===== 选择框样式 ===== */
.select-input {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

.date-input {
    color-scheme: light dark;
}

/* ===== 旅行详情区域 ===== */
.travel-details-section {
    margin-bottom: 3rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.detail-item {
    background: var(--vp-c-bg-soft);
    padding: 1.5rem;
    border-radius: 16px;
    border: 1px solid var(--vp-c-divider-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.detail-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--vp-c-brand-light);
}

.detail-item:hover::before {
    transform: scaleX(1);
}

/* ===== 操作区域样式 ===== */
.action-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--vp-c-divider-light);
}

.button-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* ===== 按钮基础样式 ===== */
.btn-primary,
.btn-secondary {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    border-radius: 16px;
    font-family: inherit;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-width: 160px;
    height: 56px;
    text-decoration: none;
    outline: none;
}

.btn-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.btn-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
    transition: transform 0.3s ease;
}

.btn-text {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* ===== 主要按钮样式 ===== */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.4),
        0 6px 16px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover .btn-content {
    transform: scale(1.05);
}

.btn-primary:hover .btn-icon {
    transform: rotate(15deg) scale(1.1);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover .btn-shine {
    left: 100%;
}

/* ===== 次要按钮样式 ===== */
.btn-secondary {
    background: var(--vp-c-bg);
    color: var(--vp-c-text-1);
    border: 2px solid var(--vp-c-divider);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-brand);
    color: var(--vp-c-brand);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover .btn-icon {
    stroke: var(--vp-c-brand);
    transform: rotate(-15deg) scale(1.1);
}

.btn-secondary:active {
    transform: translateY(0);
}

/* ===== 加载状态样式 ===== */
.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
}

.loading-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    color: var(--vp-c-brand);
}

.loading-spinner svg {
    width: 100%;
    height: 100%;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-text {
    text-align: center;
}

.loading-message {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--vp-c-text-1);
    margin-bottom: 0.5rem;
    display: block;
}

.loading-dots {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background: var(--vp-c-brand);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* ===== 旧样式兼容性保留 ===== */
.option-area {
    display: none; /* 隐藏旧的选项区域 */
}

.vp-date-input,
.form-day-input,
.form-plan-input,
.form-travel-input {
    display: none; /* 隐藏旧的输入框 */
}

.btn-planning,
.btn-reset {
    /* 基础结构 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    padding: 0.9rem 1.8rem;
    border-radius: 10px;
    font-family: var(--vp-font-family-base);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-size: 1rem;

    /* 图标样式 */
    .icon {
        font-size: 1.2em;
        transition: transform 0.3s ease;
        filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
    }

    /* 文字装饰线 */
    .btn-text {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            height: 2px;
            background: currentColor;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
    }

    /* 焦点状态 (Accessibility) */
    &:focus-visible {
        outline: 2px solid var(--vp-c-brand-light);
        outline-offset: 2px;
    }
}

/* 主要按钮样式 */
.btn-planning {
    flex: 1;
    background: var(--vp-button-brand-bg);
    color: var(--vp-button-brand-text);
    border: 2px solid var(--vp-button-brand-border);
    box-shadow:
        var(--vp-shadow-1),
        inset 0 1px 1px rgba(255, 255, 255, 0.15);

    /* 悬停效果 */
    &:hover {
        transform: translateY(-2px);
        box-shadow:
            var(--vp-shadow-2),
            inset 0 1px 2px rgba(255, 255, 255, 0.2);

        .icon {
            transform: translateY(-2px) rotate(-15deg);
        }

        .btn-text::after {
            transform: scaleX(1);
        }
    }

    /* 点击效果 */
    &:active {
        transform: translateY(1px);
        box-shadow:
            var(--vp-shadow-1),
            inset 0 2px 3px rgba(0, 0, 0, 0.1);
    }

    /* 流光效果 */
    &::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg,
                transparent 25%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 75%);
        animation: shine 3s infinite linear;
        opacity: 0.3;
    }
}

/* 重置按钮样式 */
.btn-reset {
    flex: 0 0 auto;
    min-width: 100px;
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text-2);
    border: 2px solid var(--vp-c-divider);
    box-shadow: var(--vp-shadow-1);

    /* 悬停效果 */
    &:hover {
        background: var(--vp-c-bg-mute);
        color: var(--vp-c-text-1);
        border-color: var(--vp-c-brand-soft);
        transform: translateY(-1px);
        box-shadow: var(--vp-shadow-2);

        .icon {
            transform: rotate(180deg);
        }

        .btn-text::after {
            transform: scaleX(1);
        }
    }

    /* 点击效果 */
    &:active {
        transform: translateY(0);
        box-shadow: var(--vp-shadow-1);
    }
}

@keyframes shine {
    100% {
        transform: translateX(100%);
    }
}

/* 内容包装器 - 跟随travel-form大小调整 */
.content-wrapper {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 内容项目 */
.content-item {
    margin-bottom: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.answer-area {
    white-space: pre-wrap;
    max-width: 100%;
    font-family: var(--vp-font-family-base);
    line-height: 1.7;

    /* 交互细节 */
    transition:
        border-color 0.25s,
        background-color 0.25s,
        box-shadow 0.25s;
    /* 多属性过渡动画 */

    /* 内容排版增强 */
    white-space: pre-wrap;
    word-break: break-word;
    tab-size: 2;
}

.answer-area a {
    color: var(--vp-c-brand);
    text-decoration: none;
    border-bottom: 1px solid var(--vp-c-brand);
    transition: border-color 0.25s;
}

.answer-area a:hover {
    transform: scale(1.2) !important;
    transition: transform 0.5s ease !important;
}

.think-area {
    position: relative;
    padding: 1.5rem;
    margin: 2.5rem 0 2rem 0;
    /* 进一步增加上边距，为标签留出更多空间 */
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px dashed var(--vp-c-brand-soft);
    border-radius: 12px;
    font-family: var(--vp-font-family-base);
    font-size: 0.9rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    tab-size: 2;
    color: var(--vp-c-text-3);
    /* 使用更淡的文字颜色 */
    transition: all 0.3s ease;
    overflow: hidden;
}

.think-area::before {
    content: "🤔 正在思考...";
    position: absolute;
    top: 0px;
    /* 进一步调整位置，确保完全显示 */
    right: 20px;
    background: var(--vp-c-bg);
    padding: 0.5rem 0.8rem;
    /* 增加垂直内边距，让标签更饱满 */
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--vp-c-brand);
    border: 2px solid var(--vp-c-brand-soft);
    animation: thinkingPulse 2s ease-in-out infinite;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* 添加阴影，增强层次感 */
    z-index: 10;
    /* 确保标签在最上层 */
}

@keyframes thinkingPulse {

    0%,
    100% {
        opacity: 0.7;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

.think-area::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(45deg,
            transparent,
            transparent 20px,
            rgba(60, 130, 240, 0.05) 20px,
            rgba(60, 130, 240, 0.05) 40px);
    pointer-events: none;
    opacity: 0.3;
}

.think-area:hover {
    border-color: var(--vp-c-brand);
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(60, 130, 240, 0.15);
}

.think-area:hover::before {
    color: var(--vp-c-brand-dark);
    border-color: var(--vp-c-brand);
    animation-duration: 1s;
}

/* 暗色模式适配 */
.dark .think-area {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: var(--vp-c-brand-dark);
    color: var(--vp-c-text-3);
    /* 暗色模式下也使用更淡的文字颜色 */
}

.dark .think-area::before {
    background: var(--vp-c-bg);
    color: var(--vp-c-brand-light);
    border-color: var(--vp-c-brand-dark);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    /* 暗色模式下加深阴影 */
}

.dark .think-area::after {
    background: repeating-linear-gradient(45deg,
            transparent,
            transparent 20px,
            rgba(100, 150, 255, 0.08) 20px,
            rgba(100, 150, 255, 0.08) 40px);
}

.dark .think-area:hover {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border: 1px solid rgba(100, 150, 255, 0.4);
    box-shadow: 0 8px 24px rgba(100, 150, 255, 0.15);
    transform: translateY(-2px);
}

.dark .think-area:hover::before {
    color: #60a5fa;
    background: #1e293b;
    border: 1px solid rgba(100, 150, 255, 0.5);
    box-shadow: 0 4px 12px rgba(100, 150, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 640px) {
    .think-area {
        padding: 1rem;
        margin: 2rem 0 1.5rem 0;
        /* 移动端也需要足够的上边距 */
        font-size: 0.85rem;
    }

    .think-area::before {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
        /* 移动端调整内边距 */
        left: 15px;
        top: -18px;
        /* 移动端调整标签位置 */
    }
}

.hotel-link {
    display: inline-flex;
    align-items: center;
    gap: 0.7em;
    padding: 10px 32px;
    background: linear-gradient(90deg, #f9f6f2 0%, #f7e9ce 100%);
    color: #a67c52 !important;
    font-weight: 500;
    font-size: 1.15em;
    border-radius: 28px;
    border: 1.5px solid #f3e6d0;
    box-shadow: 0 2px 8px rgba(200, 170, 120, 0.08);
    text-decoration: none;
    transition:
        background 0.2s,
        color 0.2s,
        box-shadow 0.2s,
        border-color 0.2s;
    margin-left: 10px;
    vertical-align: middle;
    backdrop-filter: blur(2px);
}

.hotel-link:hover {
    background: linear-gradient(90deg, #f7e9ce 0%, #f9f6f2 100%);
    color: #8d6e4a;
    border-color: #e7d3b3;
    box-shadow: 0 4px 16px rgba(200, 170, 120, 0.13);
    text-decoration: none;
    transform: translateY(-1px) scale(1.03);
}

.dark .hotel-link {
    background: linear-gradient(90deg, #3a2c1a 0%, #bfa77a 100%);
    color: #f7e9ce !important;
    border: 1.5px solid #6e5a3a;
    box-shadow: 0 2px 8px rgba(120, 100, 60, 0.13);
}

.dark .hotel-link:hover {
    background: linear-gradient(90deg, #bfa77a 0%, #3a2c1a 100%);
    color: #fffbe8;
    border-color: #bfa77a;
    box-shadow: 0 4px 16px rgba(120, 100, 60, 0.18);
    transform: translateY(-1px) scale(1.03);
}

.view-image,
.food-image {
    width: 100%;
    height: auto;
    min-height: 200px;
    max-height: 400px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    margin: 1rem 0;
    object-fit: cover;
    aspect-ratio: 16/9;
    background: var(--vp-c-bg-soft);
    display: block;
    opacity: 0;
    animation: imageLoad 0.5s ease-in-out 0.2s forwards;
}

/* 图片加载动画 */
@keyframes imageLoad {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 图片加载完成后显示 */
.view-image.loaded,
.food-image.loaded {
    opacity: 1;
}

/* 图片容器（可选，用于更精细的控制） */
.image-container {
    width: 100%;
    margin: 1rem 0;
    border-radius: 12px;
    overflow: hidden;
    background: var(--vp-c-bg-soft);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.image-container .view-image,
.image-container .food-image {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    animation: none;
    opacity: 1;
}

.image-container .view-image:hover,
.image-container .food-image:hover {
    transform: none;
    box-shadow: none;
}

/* 图片加载错误时的样式 */
.view-image[src=""],
.food-image[src=""],
.view-image:not([src]),
.food-image:not([src]) {
    background: var(--vp-c-bg-soft);
    border: 2px dashed var(--vp-c-divider);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vp-c-text-2);
    font-size: 0.9rem;
    opacity: 1;
}

.view-image[src=""]:before,
.food-image[src=""]:before,
.view-image:not([src]):before,
.food-image:not([src]):before {
    content: "图片加载中...";
}

/* 图片懒加载优化 */
.view-image[loading="lazy"],
.food-image[loading="lazy"] {
    opacity: 0;
    animation: none;
}

.view-image[loading="lazy"].loaded,
.food-image[loading="lazy"].loaded {
    animation: imageLoad 0.5s ease-in-out forwards;
}

.view-image:hover,
.food-image:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 地图容器响应式样式 */
.planning-box {
    margin: 1.5rem 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.map-wrapper {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.map-wrapper.map-maximized {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 2rem !important;
    box-sizing: border-box !important;
    animation: fadeIn 0.3s ease-out !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    transform: none !important;
    max-width: none !important;
    max-height: none !important;
    overflow: hidden !important;
}

/* 深色模式下的遮罩背景 */
.dark .map-wrapper.map-maximized {
    background: rgba(0, 0, 0, 0.9) !important;
}

/* 确保最大化的地图脱离所有父容器限制 */
.map-wrapper.map-maximized {
    /* 重置所有可能的父容器影响 */
    contain: none !important;
    isolation: auto !important;
    clip: none !important;
    clip-path: none !important;
    mask: none !important;
    filter: none !important;
    backdrop-filter: none !important;
}

/* 强制重置可能影响定位的父容器样式 */
.travel-form:has(.map-wrapper.map-maximized) {
    overflow: visible !important;
    transform: none !important;
    contain: none !important;
    perspective: none !important;
    filter: none !important;
}

.planning-box:has(.map-wrapper.map-maximized) {
    overflow: visible !important;
    transform: none !important;
    contain: none !important;
    perspective: none !important;
    filter: none !important;
}

/* 点击遮罩层 */
.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    cursor: pointer;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100001;
}

/* 最大化状态下的控制按钮位置调整 */
.map-maximized .map-controls {
    top: 15px;
    right: 15px;
}

.map-control-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--vp-c-text-1);
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

/* 最大化状态下的关闭按钮样式 */
.map-maximized .map-control-btn {
    background: rgba(255, 82, 82, 0.9);
    color: white;
    font-size: 18px;
    font-weight: bold;
}

.map-maximized .map-control-btn:hover {
    background: rgba(255, 82, 82, 1);
    transform: scale(1.1);
}

.map-control-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.dark .map-control-btn {
    background: rgba(40, 40, 40, 0.9);
    color: var(--vp-c-text-1);
}

.dark .map-control-btn:hover {
    background: rgba(40, 40, 40, 1);
}

.map-container {
    width: 100%;
    height: clamp(300px, 50vh, 500px);
    min-height: 250px;
    background: var(--vp-c-bg-soft);
    border-radius: 12px;
}

.map-maximized .map-container {
    width: 90vw;
    height: 80vh;
    min-height: 80vh;
    max-width: 1200px;
    max-height: 800px;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    background: var(--vp-c-bg);
    border: 2px solid var(--vp-c-divider);
    position: relative;
    z-index: 100000;
}

/* 深色模式下的地图容器 */
.dark .map-maximized .map-container {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    border-color: var(--vp-c-divider);
}

.vitepress-divider {
    border: 0;
    height: 1px;
    background: var(--vp-c-divider);
    /* 使用主题变量 */
    margin: 32px auto;
    position: relative;
    width: 85%;
}

/* 添加渐变效果增强设计感 */
.vitepress-divider::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg,
            transparent 0%,
            var(--vp-c-brand) 50%,
            transparent 100%);
    top: -1px;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .container {
        padding: 16px;
    }

    .vitepress-divider {
        margin: 24px auto;
        width: 92%;
    }
}

.loading-circle {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #333;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Answer Area Container with Action Button */
.answer-area-container {
    position: relative;
    margin-bottom: 0.5rem;
    padding: 0;
    background: transparent;
    border-radius: 12px;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* .answer-area-container:hover {
    border-color: var(--vp-c-brand-soft);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
} */

/* Action Button Styles */
.answer-action-btn {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: var(--vp-font-family-base);
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10; /* 确保按钮在最上层 */

    /* VitePress 风格的颜色 */
    background: var(--vp-c-brand-soft);
    color: var(--vp-c-brand-1);
    border: 1px solid var(--vp-c-brand-soft);

    /* 微妙的阴影 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* 动画效果 */
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    animation: slideInUp 0.4s ease-out forwards;
}

.answer-action-btn:hover {
    background: var(--vp-c-brand);
    color: var(--vp-c-white);
    border-color: var(--vp-c-brand);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.answer-action-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.answer-action-btn:focus-visible {
    outline: 2px solid var(--vp-c-brand);
    outline-offset: 2px;
}

/* Button Icon and Text */
.answer-action-btn .btn-icon {
    font-size: 1em;
    line-height: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.answer-action-btn .btn-text {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Slide in animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Dark mode adjustments */
.dark .answer-action-btn {
    background: var(--vp-c-brand-dimm);
    color: var(--vp-c-brand-light);
    border-color: var(--vp-c-brand-dimm);
}

.dark .answer-action-btn:hover {
    background: var(--vp-c-brand);
    color: var(--vp-c-white);
    border-color: var(--vp-c-brand);
}

.answer-action-input-container {
    margin: 1rem 0 3rem 0;
    /* 底部增加更多间距，为绝对定位按钮留出空间 */
    position: relative;
}

.answer-action-input {
    width: 100%;
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1rem, 3vw, 1.25rem);
    border: 2px solid var(--vp-c-brand-soft);
    border-radius: 12px;
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    font-family: var(--vp-font-family-base);
    line-height: 1.5;
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text-1);
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    resize: none;
    min-height: 48px;

    /* 占位符样式 */
    &::placeholder {
        color: var(--vp-c-text-3);
        opacity: 0.8;
        font-style: italic;
    }
}

.answer-action-input:hover {
    border-color: var(--vp-c-brand);
    box-shadow: 0 2px 8px rgba(60, 130, 240, 0.15);
    transform: translateY(-1px);
}

.answer-action-input:focus {
    outline: none;
    border-color: var(--vp-c-brand);
    box-shadow:
        0 0 0 3px rgba(60, 130, 240, 0.1),
        0 4px 12px rgba(60, 130, 240, 0.2);
    transform: translateY(-1px);
}

/* 深色模式下的租车输入框 */
.dark .answer-action-input {
    background: var(--vp-c-bg-mute);
    border-color: var(--vp-c-brand-dimm);
    color: var(--vp-c-text-1);
}

.dark .answer-action-input::placeholder {
    color: var(--vp-c-text-3);
}

.dark .answer-action-input:hover {
    border-color: var(--vp-c-brand-light);
    box-shadow: 0 2px 8px rgba(100, 160, 255, 0.15);
}

.dark .answer-action-input:focus {
    border-color: var(--vp-c-brand-light);
    box-shadow:
        0 0 0 3px rgba(100, 160, 255, 0.1),
        0 4px 12px rgba(100, 160, 255, 0.2);
}

/* Loading Indicator Styles */
.loading-indicator {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--vp-c-bg);
    border: 1px solid var(--vp-c-divider);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--vp-c-text-2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;

    /* 入场动画 */
    animation: fadeInScale 0.3s ease-out;
}

/* 旋转的圆圈 */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--vp-c-divider);
    border-top: 2px solid var(--vp-c-brand);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 加载文本 */
.loading-text {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--vp-c-text-2);
    white-space: nowrap;
}

/* 旋转动画 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 入场动画 */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-5px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 深色模式适配 */
.dark .loading-indicator {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-divider);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .spinner {
    border-color: var(--vp-c-divider);
    border-top-color: var(--vp-c-brand);
}

.dark .loading-text {
    color: var(--vp-c-text-2);
}

/* 响应式设计 */
@media (max-width: 640px) {
    .loading-indicator {
        top: 8px;
        left: 8px;
        padding: 4px 8px;
        font-size: 0.75rem;
        gap: 6px;
    }

    .spinner {
        width: 14px;
        height: 14px;
        border-width: 1.5px;
    }

    .loading-text {
        font-size: 0.75rem;
    }

    .answer-action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        bottom: 8px;
        right: 8px;
    }

    .answer-action-btn .btn-icon {
        font-size: 0.9em;
    }

    /* 租车输入框移动端优化 */
    .answer-action-input {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        min-height: 44px;
        border-radius: 10px;
    }

    .answer-action-input-container {
        margin: 0.75rem 0 2.5rem 0;
        /* 移动端也为绝对定位按钮留出足够空间 */
    }
}

/* 滚动状态指示器 */
.scroll-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--vp-c-bg);
    border: 1px solid var(--vp-c-brand);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-icon {
    font-size: 1.2em;
    line-height: 1;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--vp-c-text-1);
    white-space: nowrap;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 深色模式适配 */
.dark .scroll-status-indicator {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-brand);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* 响应式断点优化 */

/* 超小屏幕设备 (手机竖屏) */
@media (max-width: 480px) {
    .travel-form {
        margin: 0 !important;
        padding: 1.25rem !important;
        border-radius: 16px !important;
        max-width: 100% !important;
    }

    .option-area {
        flex-direction: column;
        gap: 1rem;
    }

    .form-row,
    .travel-days,
    .plan-mode,
    .travel-mode {
        width: 100%;
        min-width: unset;
    }

    .form-label {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .icon-ori {
        margin-right: 0.75rem;
        font-size: 1.2em;
    }

    .view-image,
    .food-image {
        border-radius: 8px;
        min-height: 150px;
        max-height: 250px;
        aspect-ratio: 4/3;
        margin: 0.75rem 0;
    }

    .map-container {
        height: clamp(250px, 40vh, 350px);
        border-radius: 8px;
    }

    .answer-area {
        font-size: 0.875rem;
        line-height: 1.6;
    }

    /* 按钮组移动端优化 */
    .button-group {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-planning,
    .btn-reset {
        width: 100%;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .btn-reset {
        min-width: unset;
    }
}

/* 小屏幕设备 (手机横屏/小平板) */
@media (max-width: 640px) {
    .travel-form {
        margin: 0 !important;
        padding: 1.75rem !important;
        border-radius: 18px !important;
        max-width: 100% !important;
    }

    .option-area {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .form-row {
        flex: 1 1 100%;
        min-width: unset;
    }

    .travel-days,
    .plan-mode,
    .travel-mode {
        flex: 1 1 calc(33.333% - 0.5rem);
        min-width: 90px;
    }

    .answer-area-container {
        padding: 2.5rem 1rem 3.5rem 1rem;
        /* 小屏幕设备的内边距调整 */
        margin-bottom: 1rem;
    }

    .scroll-status-indicator {
        top: 10px;
        right: 10px;
        padding: 8px 12px;
    }

    .status-text {
        font-size: 0.8rem;
    }

    .map-container {
        height: clamp(280px, 45vh, 400px);
    }

    .map-control-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .map-wrapper.map-maximized {
        padding: 1rem;
    }

    .map-maximized .map-container {
        width: 95vw;
        height: 85vh;
        min-height: 85vh;
        max-width: none;
        max-height: none;
    }
}

/* 中等屏幕设备 (平板) */
@media (min-width: 641px) and (max-width: 1024px) {
    .travel-form {
        max-width: 100% !important;
        padding: 2rem !important;
        margin: 0 !important;
    }

    .option-area {
        gap: 0.75rem;
    }

    .form-row {
        flex: 1 1 auto;
        min-width: 180px;
    }

    .travel-days,
    .plan-mode,
    .travel-mode {
        flex: 0 1 auto;
        min-width: 110px;
    }

    .map-container {
        height: clamp(350px, 45vh, 450px);
    }

    .view-image,
    .food-image {
        min-height: 180px;
        max-height: 350px;
        aspect-ratio: 16/9;
    }
}

/* 大屏幕设备优化 */
@media (min-width: 1200px) {
    .travel-form {
        max-width: 100% !important;
        padding: 3rem 4rem !important;
        margin: 0 !important;
    }

    /* 大屏幕下的表单布局优化 */
    .option-area {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 1.5rem;
        align-items: end;
    }

    .form-row {
        grid-column: span 1;
    }

    .travel-days,
    .plan-mode,
    .travel-mode {
        grid-column: span 1;
    }

    .button-group {
        margin-top: 2rem;
        justify-content: center;
        gap: 2rem;
    }

    .answer-area {
        font-size: 1rem;
        line-height: 1.8;
    }

    .view-image,
    .food-image {
        min-height: 280px;
        max-height: 500px;
        aspect-ratio: 16/10;
        margin: 1.5rem 0;
    }

    .image-container {
        margin: 1.5rem 0;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {

    .form-input,
    .vp-date-input,
    .form-day-input,
    .form-plan-input,
    .form-travel-input {
        min-height: 44px;
        /* iOS 推荐的最小触摸目标 */
        font-size: 16px;
        /* 防止 iOS Safari 缩放 */
    }

    .btn-planning {
        min-height: 48px;
        padding: 1rem 2rem;
    }

    .answer-action-btn {
        min-height: 40px;
        padding: 10px 14px;
    }

    /* 租车输入框触摸设备优化 */
    .answer-action-input {
        min-height: 48px;
        /* iOS 推荐的最小触摸目标 */
        font-size: 16px;
        /* 防止 iOS Safari 缩放 */
        padding: 1rem 1.25rem;
    }

    /* 移除悬停效果，因为触摸设备没有真正的悬停 */
    .view-image:hover,
    .food-image:hover {
        transform: none;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .answer-action-input:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(60, 130, 240, 0.15);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {

    .form-input,
    .vp-date-input,
    .form-day-input,
    .form-plan-input,
    .form-travel-input {
        border-width: 3px;
    }

    .answer-area-container {
        border-width: 2px;
    }

    .answer-action-input {
        border-width: 3px;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .view-image,
    .food-image {
        transition: none;
    }

    .view-image:hover,
    .food-image:hover {
        transform: none;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .travel-form {
        margin: 0 !important;
        padding: 1rem 2rem !important;
        border-radius: 16px !important;
        max-width: 100% !important;
    }

    .map-container {
        height: clamp(200px, 35vh, 300px);
    }

    .scroll-status-indicator {
        top: 5px;
        right: 5px;
        padding: 6px 10px;
        font-size: 0.75rem;
    }
}

/* 高德地图搜索提示框样式优化 */
/* 浅色主题下的样式 */
.amap-sug-result {
    background: var(--vp-c-bg) !important;
    border: 1px solid var(--vp-c-divider) !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden !important;
}

.amap-sug-result .auto-item {
    background: var(--vp-c-bg) !important;
    color: var(--vp-c-text-1) !important;
    border-bottom: 1px solid var(--vp-c-divider-light) !important;
    padding: 12px 16px !important;
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
    transition: all 0.2s ease !important;
}

.amap-sug-result .auto-item:hover,
.amap-sug-result .auto-item.selected {
    background: var(--vp-c-brand-soft) !important;
    color: var(--vp-c-brand-1) !important;
}

.amap-sug-result .auto-item:last-child {
    border-bottom: none !important;
}

/* 深色主题下的特殊优化 */
.dark .amap-sug-result {
    background: var(--vp-c-bg-soft) !important;
    border-color: var(--vp-c-divider) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
}

.dark .amap-sug-result .auto-item {
    background: var(--vp-c-bg-soft) !important;
    color: var(--vp-c-text-1) !important;
    border-bottom-color: var(--vp-c-divider) !important;
}

.dark .amap-sug-result .auto-item:hover,
.dark .amap-sug-result .auto-item.selected {
    background: var(--vp-c-brand-dimm) !important;
    color: var(--vp-c-brand-light) !important;
}

/* 高德地图搜索提示框的其他元素优化 */
.amap-sug-result .auto-item .name {
    color: inherit !important;
    font-weight: 500 !important;
}

.amap-sug-result .auto-item .address {
    color: var(--vp-c-text-2) !important;
    font-size: 0.8rem !important;
    margin-top: 2px !important;
}

.dark .amap-sug-result .auto-item .address {
    color: var(--vp-c-text-2) !important;
}

/* 确保提示框在页面层级中正确显示 */
.amap-sug-result {
    z-index: 9999 !important;
    position: absolute !important;
}

/* 针对可能的其他高德地图UI元素的深色主题适配 */
.dark .amap-ui-autocomplete {
    background: var(--vp-c-bg-soft) !important;
    color: var(--vp-c-text-1) !important;
}

.dark .amap-ui-autocomplete .amap-ui-autocomplete-item {
    color: var(--vp-c-text-1) !important;
    background: var(--vp-c-bg-soft) !important;
}

.dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover {
    background: var(--vp-c-brand-dimm) !important;
    color: var(--vp-c-brand-light) !important;
}

/* 选择策略文本显示 */
.selected-strategy-text {
    margin-top: 8px;
    font-size: 12px;
    color: var(--vp-c-text-2);
    font-style: italic;
    padding: 6px 12px;
    background-color: var(--vp-c-bg-soft);
    border-radius: 6px;
    border-left: 3px solid var(--vp-c-brand);
    transition: all 0.3s ease;
}

.dark .selected-strategy-text {
    background-color: var(--vp-c-bg-alt);
    color: var(--vp-c-text-2);
    border-left-color: var(--vp-c-brand-light);
}

/* ===== Enhanced Markdown Styling ===== */

/* 提示容器 */
.tip-container {
    background: linear-gradient(135deg, #e8f5e8, #f0f9f0);
    border-left: 4px solid #52c41a;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.1);
}

.tip-title {
    font-weight: 600;
    color: #389e0d;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.tip-title::before {
    content: "💡";
    margin-right: 8px;
}

/* 警告容器 */
.warning-container {
    background: linear-gradient(135deg, #fff7e6, #fefcf6);
    border-left: 4px solid #fa8c16;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(250, 140, 22, 0.1);
}

.warning-title {
    font-weight: 600;
    color: #d46b08;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.warning-title::before {
    content: "⚠️";
    margin-right: 8px;
}

/* 表格样式 */
.table-container {
    margin: 16px 0;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.answer-area .markdown-table {
    width: 100% !important;
    border-collapse: collapse !important;
    background: var(--vp-c-bg) !important;
    font-size: 0.9rem !important;
    margin: 16px 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.answer-area .markdown-table th,
.answer-area .markdown-table td {
    padding: 12px 16px !important;
    text-align: left !important;
    border-bottom: 1px solid var(--vp-c-divider-light) !important;
}

.answer-area .markdown-table th {
    background: var(--vp-c-bg-soft) !important;
    font-weight: 600 !important;
    color: var(--vp-c-text-1) !important;
    border-bottom: 2px solid var(--vp-c-brand) !important;
}

.answer-area .markdown-table td {
    color: var(--vp-c-text-2) !important;
}

.answer-area .markdown-table tr:hover {
    background: linear-gradient(135deg, var(--vp-c-bg-soft), var(--vp-c-brand-soft)) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.answer-area .markdown-table {
    transition: all 0.3s ease !important;
}

.answer-area .markdown-table td {
    transition: all 0.3s ease !important;
}

.answer-area .markdown-table:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

/* 代码块样式 */
.answer-area .code-container {
    margin: 16px 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.answer-area .code-container pre {
    background: var(--vp-c-bg-alt) !important;
    padding: 20px !important;
    margin: 0 !important;
    overflow-x: auto !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
    font-size: 0.875rem !important;
    line-height: 1.6 !important;
    color: var(--vp-c-text-1) !important;
    border-left: 4px solid var(--vp-c-brand) !important;
}

.code-container code {
    background: transparent;
    padding: 0;
    font-size: inherit;
    color: inherit;
}

/* 行内代码样式 */
.answer-area .inline-code {
    background: linear-gradient(135deg, var(--vp-c-bg-soft), var(--vp-c-brand-soft)) !important;
    color: var(--vp-c-brand-1) !important;
    padding: 3px 8px !important;
    border-radius: 6px !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
    font-size: 0.875em !important;
    font-weight: 600 !important;
    border: 1px solid var(--vp-c-brand-soft) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

.answer-area .inline-code:hover {
    background: linear-gradient(135deg, var(--vp-c-brand-soft), var(--vp-c-brand)) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 图片样式 */
.image-container {
    margin: 20px 0;
    text-align: center;
}

.markdown-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.markdown-image:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.image-caption {
    margin-top: 8px;
    font-size: 0.875rem;
    color: var(--vp-c-text-2);
    font-style: italic;
}

/* 增强的标题样式 - 提高优先级 */
.answer-area h1,
.answer-area h2,
.answer-area h3,
.answer-area h4,
.answer-area h5,
.answer-area h6 {
    color: var(--vp-c-text-1) !important;
    font-weight: 600 !important;
    margin: 24px 0 16px 0 !important;
    line-height: 1.3 !important;
    transition: all 0.3s ease !important;
}

/* 标题悬停效果 */
.answer-area h1:hover,
.answer-area h2:hover,
.answer-area h3:hover {
    transform: translateX(5px) !important;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* h2和h3的装饰线 */
.answer-area h2::before {
    content: '▶' !important;
    color: var(--vp-c-brand) !important;
    margin-right: 8px !important;
    font-size: 0.8em !important;
    opacity: 0.8 !important;
}

.answer-area h3::before {
    content: '●' !important;
    color: var(--vp-c-brand-1) !important;
    margin-right: 8px !important;
    font-size: 0.6em !important;
    opacity: 0.7 !important;
}

.answer-area h4::before {
    content: '◦' !important;
    color: var(--vp-c-brand-1) !important;
    margin-right: 6px !important;
    font-size: 0.5em !important;
    opacity: 0.6 !important;
}

.answer-area h1 {
    font-size: 2rem !important;
    border-bottom: 2px solid var(--vp-c-brand) !important;
    padding-bottom: 8px !important;
}

.answer-area h2 {
    font-size: 1.5rem !important;
    border-bottom: 1px solid var(--vp-c-divider) !important;
    padding-bottom: 6px !important;
}

.answer-area h3 {
    font-size: 1.25rem !important;
    color: var(--vp-c-brand-1) !important;
}

.answer-area h4 {
    font-size: 1.125rem !important;
    color: var(--vp-c-text-1) !important;
}

.answer-area h5 {
    font-size: 1rem !important;
    color: var(--vp-c-text-1) !important;
}

.answer-area h6 {
    font-size: 0.875rem !important;
    color: var(--vp-c-text-2) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
}

/* 增强的列表样式 */
.answer-area ul,
.answer-area ol {
    margin: 16px 0 !important;
    padding-left: 24px !important;
}

.answer-area li {
    margin: 8px 0 !important;
    line-height: 1.6 !important;
}

.answer-area ul li {
    list-style-type: none !important;
    position: relative !important;
}

.answer-area ul li::before {
    content: "•" !important;
    color: var(--vp-c-brand) !important;
    font-weight: bold !important;
    position: absolute !important;
    left: -16px !important;
}

/* 引用块样式 */
.answer-area blockquote {
    background: var(--vp-c-bg-soft) !important;
    border-left: 4px solid var(--vp-c-brand) !important;
    margin: 16px 0 !important;
    padding: 16px 20px !important;
    border-radius: 0 8px 8px 0 !important;
    font-style: italic !important;
    color: var(--vp-c-text-2) !important;
}

.answer-area blockquote p {
    margin: 0 !important;
}

/* 分割线样式 */
.answer-area hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--vp-c-brand), transparent);
    margin: 32px 0;
}

/* 强调文本样式 */
.answer-area strong {
    color: var(--vp-c-text-1) !important;
    font-weight: 600 !important;
}

.answer-area em {
    color: var(--vp-c-brand-1) !important;
    font-style: italic !important;
}

.answer-area p {
    margin: 12px 0 !important;
    line-height: 1.7 !important;
    color: var(--vp-c-text-1) !important;
}

/* 链接样式 */
.answer-area a {
    color: var(--vp-c-brand-1) !important;
    text-decoration: none !important;
    border-bottom: 1px solid transparent !important;
    transition: all 0.3s ease !important;
}

.answer-area a:hover {
    color: var(--vp-c-brand) !important;
    border-bottom-color: var(--vp-c-brand) !important;
}

/* 深色模式适配 */
.dark .tip-container {
    background: linear-gradient(135deg, #162312, #1f2917);
    border-left-color: #73d13d;
}

.dark .tip-title {
    color: #95de64;
}

.dark .warning-container {
    background: linear-gradient(135deg, #2b1d05, #362508);
    border-left-color: #ffa940;
}

.dark .warning-title {
    color: #ffc069;
}

.dark .markdown-table {
    background: var(--vp-c-bg-alt);
}

.dark .markdown-table th {
    background: var(--vp-c-bg-mute);
}

.dark .inline-code {
    background: var(--vp-c-bg-mute);
    color: var(--vp-c-brand-light);
    border-color: var(--vp-c-divider);
}

.dark .markdown-image {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.dark .markdown-image:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 640px) {

    .tip-container,
    .warning-container {
        padding: 12px;
        margin: 12px 0;
    }

    .markdown-table th,
    .markdown-table td {
        padding: 8px 12px;
        font-size: 0.875rem;
    }

    .code-container pre {
        padding: 16px;
        font-size: 0.8rem;
    }

    .answer-area h1 {
        font-size: 1.5rem;
    }

    .answer-area h2 {
        font-size: 1.25rem;
    }
}

/* 全局加载横幅 */
.global-loading-banner {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 99999 !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: white !important;
    padding: 0.8rem 1rem !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    transform: none !important;
    clip: auto !important;
    overflow: visible !important;
}

.global-loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    max-width: 1200px;
    margin: 0 auto;
}

.global-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: globalSpin 1s linear infinite;
}

.global-loading-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes globalSpin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 天数导航 */
.day-navigation {
    display: flex;
    gap: 0.5rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--vp-c-bg-soft);
    border-radius: 12px;
    border: 1px solid var(--vp-c-divider-light);
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--vp-c-brand-soft) transparent;
}

.day-navigation::-webkit-scrollbar {
    height: 4px;
}

.day-navigation::-webkit-scrollbar-track {
    background: transparent;
}

.day-navigation::-webkit-scrollbar-thumb {
    background: var(--vp-c-brand-soft);
    border-radius: 2px;
}

.day-nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--vp-c-divider-light);
    border-radius: 10px;
    background: var(--vp-c-bg);
    color: var(--vp-c-text-2);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 120px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.day-nav-btn:hover:not(.disabled) {
    border-color: var(--vp-c-brand-soft);
    background: var(--vp-c-bg-soft);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.day-nav-btn.completed {
    border-color: var(--vp-c-brand);
    color: var(--vp-c-text-1);
    background: var(--vp-c-brand-soft);
}

.day-nav-btn.completed:hover {
    background: var(--vp-c-brand);
    color: white;
}

.day-nav-btn.active {
    border-color: var(--vp-c-brand);
    background: var(--vp-c-brand);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(60, 130, 240, 0.3);
}

.day-nav-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--vp-c-bg-mute);
    color: var(--vp-c-text-3);
}

.day-number {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.day-title {
    font-size: 0.75rem;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

/* 板块标题 */
.section-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 当内容展开时的样式 */
.answer-area-container.expanded .section-header {
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
}

/* 确保展开时header和content的边框连接 */
.answer-area-container.expanded .section-content {
    border-top: none;
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.section-header:hover::before {
    opacity: 1;
}

.header-icon {
    font-size: 1.8rem;
    z-index: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.header-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: inherit;
    z-index: 1;
    transition: all 0.3s ease;
}

.header-subtitle {
    font-size: 0.85rem;
    color: inherit;
    font-weight: 400;
    opacity: 0.9;
    z-index: 1;
    transition: all 0.3s ease;
}

/* 不同板块的颜色主题 */
.weather-header {
    background: linear-gradient(135deg, #8fb3d3 0%, #5d8aa8 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(143, 179, 211, 0.25);
}

.weather-header .header-title,
.weather-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rent-header {
    background: linear-gradient(135deg, #a8c09a 0%, #7d8471 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(168, 192, 154, 0.25);
}

.rent-header .header-title,
.rent-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.driving-header {
    background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(212, 165, 116, 0.25);
}

.driving-header .header-title,
.driving-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.map-header {
    background: linear-gradient(135deg, #9bb5a6 0%, #7a9485 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(155, 181, 166, 0.25);
}

.map-header .header-title,
.map-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.view-header {
    background: linear-gradient(135deg, #b5a7c7 0%, #9688a3 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(181, 167, 199, 0.25);
}

.view-header .header-title,
.view-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.food-header {
    background: linear-gradient(135deg, #d4a574 0%, #c4956b 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(212, 165, 116, 0.25);
}

.food-header .header-title,
.food-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hotel-header {
    background: linear-gradient(135deg, #c4a484 0%, #a8906f 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(196, 164, 132, 0.25);
}

.hotel-header .header-title,
.hotel-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cost-header {
    background: linear-gradient(135deg, #a8a8a8 0%, #8a8a8a 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(168, 168, 168, 0.25);
}

.cost-header .header-title,
.cost-header .header-subtitle {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 暗色主题下的莫兰迪色系适配 */
.dark .weather-header {
    background: linear-gradient(135deg, #6b8ca3 0%, #4a6b7a 100%);
    box-shadow: 0 4px 20px rgba(107, 140, 163, 0.3);
}

.dark .rent-header {
    background: linear-gradient(135deg, #8a9c7e 0%, #6b7a5f 100%);
    box-shadow: 0 4px 20px rgba(138, 156, 126, 0.3);
}

.dark .driving-header {
    background: linear-gradient(135deg, #b8956a 0%, #9c7f56 100%);
    box-shadow: 0 4px 20px rgba(184, 149, 106, 0.3);
}

.dark .map-header {
    background: linear-gradient(135deg, #7a9485 0%, #647a6f 100%);
    box-shadow: 0 4px 20px rgba(122, 148, 133, 0.3);
}

.dark .view-header {
    background: linear-gradient(135deg, #9688a3 0%, #7a6b85 100%);
    box-shadow: 0 4px 20px rgba(150, 136, 163, 0.3);
}

.dark .food-header {
    background: linear-gradient(135deg, #c4956b 0%, #a67f56 100%);
    box-shadow: 0 4px 20px rgba(196, 149, 107, 0.3);
}

.dark .hotel-header {
    background: linear-gradient(135deg, #a8906f 0%, #8a7a5f 100%);
    box-shadow: 0 4px 20px rgba(168, 144, 111, 0.3);
}

.dark .cost-header {
    background: linear-gradient(135deg, #8a8a8a 0%, #707070 100%);
    box-shadow: 0 4px 20px rgba(138, 138, 138, 0.3);
}

/* 响应式设计 */
@media (max-width: 640px) {
    .global-loading-banner {
        padding: 0.6rem 0.8rem;
    }

    .global-loading-text {
        font-size: 0.8rem;
    }

    .day-navigation {
        padding: 0.8rem;
        gap: 0.3rem;
    }

    .day-nav-btn {
        min-width: 100px;
        padding: 0.6rem 0.8rem;
    }

    .day-number {
        font-size: 0.8rem;
    }

    .day-title {
        font-size: 0.7rem;
        max-width: 80px;
    }

    .section-header {
        padding: 0.6rem 1rem;
        gap: 0.6rem;
        margin-bottom: 0;
    }

    .header-icon {
        font-size: 1.5rem;
    }

    .header-title {
        font-size: 1.1rem;
    }

    .header-subtitle {
        font-size: 0.75rem;
    }

    .answer-area-container {
        margin-bottom: 0.3rem;
    }

    .section-content {
        padding: 1rem;
    }
}

/* 暗色模式适配 */
.dark .global-loading-banner {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark .global-loading-text {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.dark .day-navigation {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-divider);
}

.dark .day-nav-btn {
    background: var(--vp-c-bg);
    border-color: var(--vp-c-divider);
}

.dark .day-nav-btn:hover:not(.disabled) {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-brand-soft);
}

.dark .day-nav-btn.completed {
    background: var(--vp-c-brand-soft);
    border-color: var(--vp-c-brand);
}

.dark .day-nav-btn.disabled {
    background: var(--vp-c-bg-mute);
    color: var(--vp-c-text-3);
}

/* 完成提示弹窗样式 */
.completion-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
    animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }

    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

.completion-modal {
    background: var(--vp-c-bg);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 480px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    border: 1px solid var(--vp-c-divider);
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 2rem 2rem 1rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--vp-c-brand-soft) 0%, var(--vp-c-brand) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: modalGlow 3s ease-in-out infinite;
}

@keyframes modalGlow {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.7;
    }
}

.modal-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    animation: modalIconBounce 0.6s ease-out;
    position: relative;
    z-index: 1;
}

@keyframes modalIconBounce {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.modal-body {
    padding: 2rem;
    text-align: center;
}

.modal-message {
    font-size: 1.1rem;
    color: var(--vp-c-text-1);
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.modal-subtitle {
    font-size: 0.95rem;
    color: var(--vp-c-text-2);
    margin: 0 0 2rem 0;
    line-height: 1.4;
}

.location {
    color: var(--vp-c-brand);
    font-weight: 600;
    background: var(--vp-c-brand-soft);
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    white-space: nowrap;
}

.modal-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem;
    background: var(--vp-c-bg-soft);
    border-radius: 10px;
    border: 1px solid var(--vp-c-divider-light);
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: var(--vp-c-text-1);
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--vp-c-brand-soft);
}

.feature-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.modal-footer {
    padding: 1rem 2rem 2rem 2rem;
    text-align: center;
}

.modal-confirm-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 2rem;
    background: linear-gradient(135deg, var(--vp-c-brand) 0%, var(--vp-c-brand-dark) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(60, 130, 240, 0.3);
    position: relative;
    overflow: hidden;
}

.modal-confirm-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modal-confirm-btn:hover::before {
    left: 100%;
}

.modal-confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(60, 130, 240, 0.4);
}

.modal-confirm-btn:active {
    transform: translateY(0);
}

.modal-confirm-btn .btn-icon {
    font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .completion-modal {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .modal-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-features {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .modal-footer {
        padding: 1rem 1.5rem 1.5rem 1.5rem;
    }

    .modal-icon {
        font-size: 2.5rem;
    }

    .modal-title {
        font-size: 1.3rem;
    }

    .modal-message {
        font-size: 1rem;
    }
}

/* 暗色模式适配 */
.dark .completion-modal {
    background: var(--vp-c-bg);
    border-color: var(--vp-c-divider);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
}

.dark .completion-modal-overlay {
    background: rgba(0, 0, 0, 0.7);
}

.dark .feature-item {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-divider);
}

.dark .feature-item:hover {
    border-color: var(--vp-c-brand-soft);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 规划失败提示弹窗样式 */
.failure-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
    animation: modalOverlayFadeIn 0.3s ease-out;
}

.failure-modal {
    background: var(--vp-c-bg);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 480px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    border: 1px solid var(--vp-c-divider);
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
}

.failure-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
}

.failure-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: modalGlow 3s ease-in-out infinite;
}

.failure-suggestions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem;
    background: var(--vp-c-bg-soft);
    border-radius: 10px;
    border: 1px solid var(--vp-c-divider-light);
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: var(--vp-c-text-1);
}

.suggestion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #ff6b6b;
}

.suggestion-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.modal-failure-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 2rem;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.modal-failure-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modal-failure-btn:hover::before {
    left: 100%;
}

.modal-failure-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
}

.modal-failure-btn:active {
    transform: translateY(0);
}

.modal-failure-btn .btn-icon {
    font-size: 1.4rem;
}

.modal-failure-btn .btn-text {
    font-weight: 600;
}

/* 暗色模式适配 - 失败窗口 */
.dark .failure-modal {
    background: var(--vp-c-bg);
    border-color: var(--vp-c-divider);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
}

.dark .failure-modal-overlay {
    background: rgba(0, 0, 0, 0.7);
}

.dark .suggestion-item {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-divider);
}

.dark .suggestion-item:hover {
    border-color: #ff6b6b;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 - 失败窗口 */
@media (max-width: 640px) {
    .failure-modal {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .failure-suggestions {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }
}

/* 天数详情面板样式 */
.day-detail-panel {
    margin: 1.5rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid var(--vp-c-brand-soft);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(60, 130, 240, 0.1);
    animation: panelSlideDown 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.day-detail-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(60, 130, 240, 0.05) 0%,
        rgba(100, 150, 255, 0.03) 50%,
        rgba(60, 130, 240, 0.05) 100%);
    pointer-events: none;
}

@keyframes panelSlideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.panel-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    animation: iconFloat 2s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

.panel-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--vp-c-text-1);
    margin: 0 0 0.5rem 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.panel-subtitle {
    font-size: 1rem;
    color: var(--vp-c-text-2);
    margin: 0;
    font-weight: 500;
}

.panel-body {
    position: relative;
    z-index: 1;
}

.panel-features {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 0.8rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    padding: 0.2rem;
    scrollbar-width: thin;
    scrollbar-color: var(--vp-c-brand-soft) transparent;
    scroll-behavior: smooth;
}

.panel-features::-webkit-scrollbar {
    height: 4px;
}

.panel-features::-webkit-scrollbar-track {
    background: transparent;
}

.panel-features::-webkit-scrollbar-thumb {
    background: var(--vp-c-brand-soft);
    border-radius: 2px;
}

.panel-features::-webkit-scrollbar-thumb:hover {
    background: var(--vp-c-brand);
}

.feature-button {
    flex: 0 0 auto;
    min-width: 120px;
    max-width: 140px;
    height: 90px;
    padding: 1rem 0.8rem;
    border: 2px solid var(--vp-c-border);
    border-radius: 12px;
    background: var(--vp-c-bg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
    backdrop-filter: blur(10px);
}

.feature-button:first-child {
    margin-left: 0;
}

.feature-button:last-child {
    margin-right: 0;
}

.feature-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.feature-button.available {
    border-color: var(--vp-c-brand-soft);
    background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%);
}

.feature-button.available:hover {
    border-color: var(--vp-c-brand);
    background: linear-gradient(135deg, var(--vp-c-brand-soft) 0%, var(--vp-c-bg-soft) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(60, 130, 240, 0.2);
}

.feature-button.available:hover::before {
    left: 100%;
}

.feature-button.active {
    border-color: var(--vp-c-brand);
    background: linear-gradient(135deg, var(--vp-c-brand-soft) 0%, #e3f2fd 100%);
    color: var(--vp-c-brand-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(60, 130, 240, 0.3);
}

.feature-button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: var(--vp-c-bg-mute);
    border-color: var(--vp-c-border);
}

.feature-button:disabled:hover {
    transform: none;
    box-shadow: none;
}

.feature-icon {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-button.available:hover .feature-icon {
    transform: scale(1.1);
}

.feature-button.active .feature-icon {
    transform: scale(1.1);
}

.feature-text {
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: color 0.3s ease;
}

.feature-button.active .feature-text {
    color: var(--vp-c-brand-dark);
    font-weight: 700;
}

/* Panel Actions 样式 */
.panel-actions {
    margin-top: 1.5rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.collapse-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(135deg, var(--vp-c-text-2) 0%, var(--vp-c-text-3) 100%);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.collapse-all-btn:hover {
    background: linear-gradient(135deg, var(--vp-c-text-1) 0%, var(--vp-c-text-2) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.collapse-all-btn .btn-icon {
    font-size: 1rem;
}

.collapse-all-btn .btn-text {
    font-weight: 600;
}

.dark .section-header:hover .header-icon {
    filter: brightness(1.3) saturate(1.2);
}

/* 天数详情面板暗色模式适配 */
.dark .day-detail-panel {
    background: linear-gradient(135deg, #1a1f2e 0%, #0f1419 100%);
    border: 1px solid rgba(100, 150, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.dark .day-detail-panel::before {
    background: linear-gradient(135deg,
        rgba(100, 150, 255, 0.05) 0%,
        rgba(60, 130, 240, 0.03) 50%,
        rgba(100, 150, 255, 0.05) 100%);
}

.dark .panel-header {
    border-bottom: 1px solid rgba(100, 150, 255, 0.15);
}

.dark .panel-title {
    color: #e2e8f0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark .panel-subtitle {
    color: #94a3b8;
}

.dark .feature-button {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border: 1px solid rgba(100, 150, 255, 0.2);
    color: #cbd5e1;
}

.dark .feature-button.available {
    border: 1px solid rgba(100, 150, 255, 0.4);
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: #e2e8f0;
}

.dark .feature-button.available:hover {
    border: 1px solid rgba(100, 150, 255, 0.6);
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(100, 150, 255, 0.2);
}

.dark .feature-button.active {
    border: 1px solid #60a5fa;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.dark .feature-button.active .feature-text {
    color: white;
    font-weight: 700;
}

.dark .collapse-all-btn {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 1px solid rgba(100, 150, 255, 0.3);
    color: #e5e7eb;
}

.dark .collapse-all-btn:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    border: 1px solid rgba(100, 150, 255, 0.5);
    box-shadow: 0 6px 16px rgba(100, 150, 255, 0.15);
}

/* 响应式设计 */
@media (max-width: 640px) {
    .day-detail-panel {
        padding: 1.2rem;
        margin: 1rem 0;
    }

        .panel-features {
        justify-content: center;
        gap: 0.6rem;
        margin: 1rem 0;
        padding: 0.1rem;
    }

    .feature-button {
        min-width: 100px;
        max-width: 110px;
        height: 80px;
        padding: 0.8rem 0.4rem;
    }

    .feature-icon {
        font-size: 1.2rem;
    }

    .feature-text {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    .panel-title {
        font-size: 1.2rem;
    }

    .panel-icon {
        font-size: 1.8rem;
    }

    /* 小屏幕滚动提示 */
    .panel-features::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 20px;
        background: linear-gradient(90deg, transparent 0%, var(--vp-c-bg) 100%);
        pointer-events: none;
    }

    .day-detail-panel {
        position: relative;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .panel-features {
        justify-content: center;
        gap: 0.4rem;
    }

    .feature-button {
        min-width: 85px;
        max-width: 90px;
        height: 75px;
        padding: 0.6rem 0.3rem;
    }

    .feature-icon {
        font-size: 1.1rem;
    }

    .feature-text {
        font-size: 0.7rem;
    }
}

/* 添加滚动阴影效果 */
.day-detail-panel {
    position: relative;
}

.day-detail-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(60, 130, 240, 0.03) 0%,
        rgba(100, 150, 255, 0.02) 50%,
        rgba(60, 130, 240, 0.03) 100%);
    z-index: 0;
    border-radius: inherit;
}

.panel-body {
    position: relative;
    z-index: 1;
}

/* Section Header Hover 效果优化 */
.section-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.section-header:hover .header-icon {
    transform: scale(1.05);
}

/* 切换指示器 */
.header-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    transition: transform 0.3s ease;
}

/* 内容展开动画 */
.section-content {
    animation: contentSlideDown 0.3s ease-out;
    padding: 1.5rem;
    margin-top: 0;
    background: var(--vp-c-bg-soft);
    border-radius: 0 0 12px 12px;
    border: 1px solid var(--vp-c-divider-light);
    position: relative; /* 为绝对定位的按钮提供定位上下文 */
}

@keyframes contentSlideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬浮滚动按钮 */
.floating-scroll-buttons {
    position: fixed;
    right: 20px;
    bottom: 80px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(60px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-scroll-buttons.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.scroll-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.scroll-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%);
    transition: all 0.3s ease;
    z-index: 0;
}

.scroll-btn-icon {
    position: relative;
    z-index: 1;
    color: var(--vp-c-text-1);
    transition: all 0.3s ease;
}

.scroll-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.scroll-btn:hover::before {
    background: linear-gradient(135deg,
        var(--vp-c-brand) 0%,
        var(--vp-c-brand-light) 100%);
}

.scroll-btn:hover .scroll-btn-icon {
    color: white;
    transform: scale(1.1);
}

.scroll-btn:active {
    transform: scale(0.95);
}

/* 按钮特定样式 */
.scroll-to-top {
    animation-delay: 0.1s;
}

.scroll-to-bottom {
    animation-delay: 0.2s;
}

/* 暗色模式适配 */
.dark .scroll-btn::before {
    background: linear-gradient(135deg,
        rgba(40, 40, 40, 0.9) 0%,
        rgba(60, 60, 60, 0.8) 100%);
}

.dark .scroll-btn:hover::before {
    background: linear-gradient(135deg,
        var(--vp-c-brand-dark) 0%,
        var(--vp-c-brand) 100%);
}

.dark .scroll-btn {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.dark .scroll-btn:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 移动端适配 */
@media (max-width: 640px) {
    .floating-scroll-buttons {
        right: 16px;
        bottom: 70px;
        gap: 6px;
    }

    .scroll-btn {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .floating-scroll-buttons {
        right: 12px;
        bottom: 60px;
    }

    .scroll-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* 显示动画 */
@keyframes floatIn {
    from {
        opacity: 0;
        transform: translateX(60px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.floating-scroll-buttons.show .scroll-btn {
    animation: floatIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 自定义地址自动完成组件样式 */
.custom-amap-suggest {
    scrollbar-width: thin;
    scrollbar-color: var(--vp-c-brand-soft) transparent;
    background: var(--vp-c-bg) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    position: fixed !important;
    z-index: 99999 !important;
    border: 2px solid var(--vp-c-divider) !important;
    border-radius: 12px !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2) !important;
}

.custom-amap-suggest::-webkit-scrollbar {
    width: 6px;
}

.custom-amap-suggest::-webkit-scrollbar-track {
    background: transparent;
}

.custom-amap-suggest::-webkit-scrollbar-thumb {
    background: var(--vp-c-brand-soft);
    border-radius: 3px;
}

.custom-amap-suggest::-webkit-scrollbar-thumb:hover {
    background: var(--vp-c-brand);
}

.custom-suggest-item {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    background: var(--vp-c-bg);
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.custom-suggest-item:hover,
.custom-suggest-item.selected {
    background: var(--vp-c-brand-soft) !important;
    color: var(--vp-c-brand-1) !important;
    transform: translateX(4px) !important;
    border-left: 3px solid var(--vp-c-brand) !important;
}

.dark .custom-suggest-item {
    background: var(--vp-c-bg-soft);
}

/* 确保在移动端也有良好的触摸体验 */
@media (hover: none) and (pointer: coarse) {
    .custom-suggest-item {
        padding: 16px !important;
        min-height: 60px;
        display: flex !important;
        align-items: center;
    }

    .custom-amap-suggest {
        max-height: 250px !important;
    }
}

/* 暗色主题下的特殊处理 */
.dark .custom-amap-suggest {
    background: var(--vp-c-bg-soft) !important;
    border-color: var(--vp-c-divider) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5) !important;
}

.dark .custom-suggest-item {
    border-bottom-color: var(--vp-c-divider) !important;
}

.dark .custom-suggest-item:hover,
.dark .custom-suggest-item.selected {
    background: var(--vp-c-brand-dimm) !important;
    color: var(--vp-c-brand-light) !important;
    transform: translateX(4px) !important;
    border-left: 3px solid var(--vp-c-brand-light) !important;
}

/* 确保建议列表在最顶层 */
.custom-amap-suggest {
    z-index: 99999 !important;
}

/* ===== 新增响应式设计 ===== */

/* 平板设备 */
@media (max-width: 1024px) {
    .travel-planning-container {
        padding: 1.5rem;
    }

    .travel-form {
        padding: 2rem;
    }

    .location-inputs {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .route-connector {
        width: 50px;
        height: 50px;
        margin: 0 auto 1rem auto;
        transform: rotate(90deg);
    }

    .details-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .button-group {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .travel-planning-container {
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 2rem;
        padding: 1rem 0;
    }

    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .title-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .travel-form {
        padding: 1.5rem;
        border-radius: 20px;
    }

    .form-progress {
        margin-bottom: 2rem;
        padding: 1rem 0.5rem;
        /* 768px屏幕暂时不启用滚动，内容还能清楚显示 */
    }

    .progress-step {
        gap: 0.25rem;
        flex-shrink: 0;
        min-width: 70px;
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .step-label {
        font-size: 0.625rem;
        white-space: nowrap;
    }

    .progress-line {
        width: 60px;
        margin: 0 0.5rem;
        flex-shrink: 0;
    }

    .section-title {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    .section-icon {
        width: 20px;
        height: 20px;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    .detail-item {
        padding: 1rem;
    }

    .form-input {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .route-connector {
        width: 40px;
        height: 40px;
    }

    .route-connector svg {
        width: 20px;
        height: 20px;
    }
}

/* 中小手机设备 - 适度压缩但不启用滚动 */
@media (max-width: 480px) {
    .form-progress {
        padding: 0.75rem 0.5rem;
        margin-bottom: 1.5rem;
        gap: 0.5rem;
    }

    .progress-step {
        flex-shrink: 0;
        min-width: 65px;
        gap: 0.25rem;
    }

    .step-number {
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .step-label {
        font-size: 0.625rem;
        white-space: nowrap;
    }

    .progress-line {
        width: 50px;
        margin: 0 0.375rem;
        flex-shrink: 0;
    }
}

/* 小手机设备 - 启用滚动 */
@media (max-width: 400px) {
    .travel-planning-container {
        padding: 0.75rem;
    }

    .travel-form {
        padding: 1rem;
        border-radius: 16px;
    }

    .page-title {
        font-size: 1.75rem;
    }

    .title-icon {
        width: 2rem;
        height: 2rem;
    }

    .form-progress {
        /* 保持横向布局，添加滚动 */
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 0.75rem 0.5rem;
        margin-bottom: 1.5rem;
        /* 添加滚动条样式 */
        scrollbar-width: thin;
        scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
        /* 最小宽度确保内容不被压缩 */
        min-width: 100%;
        justify-content: flex-start;
        gap: 0.75rem;
    }

    .form-progress::-webkit-scrollbar {
        height: 4px;
    }

    .form-progress::-webkit-scrollbar-track {
        background: transparent;
    }

    .form-progress::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.3);
        border-radius: 2px;
    }

    .form-progress::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.5);
    }

    .progress-step {
        /* 防止步骤被压缩 */
        flex-shrink: 0;
        min-width: 60px;
        gap: 0.25rem;
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .step-label {
        font-size: 0.6rem;
        white-space: nowrap;
    }

    .progress-line {
        /* 恢复横向连接线 */
        width: 40px;
        height: 2px;
        margin: 0 0.25rem;
        flex-shrink: 0;
    }

    .progress-line::after {
        /* 恢复横向动画 */
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, #667eea, transparent);
    }

    .section-title {
        font-size: 1rem;
        gap: 0.5rem;
    }

    .btn-content {
        padding: 0.875rem 1.5rem;
    }

    .btn-icon {
        width: 18px;
        height: 18px;
    }

    .btn-text {
        font-size: 0.9rem;
    }
}

/* 超小屏幕设备 - 极致压缩 */
@media (max-width: 350px) {
    .form-progress {
        padding: 0.5rem 0.25rem;
        gap: 0.5rem;
    }

    .progress-step {
        min-width: 50px;
    }

    .step-number {
        width: 24px;
        height: 24px;
        font-size: 0.65rem;
    }

    .step-label {
        font-size: 0.55rem;
    }

    .progress-line {
        width: 30px;
        margin: 0 0.125rem;
    }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
    .travel-planning-container {
        padding: 3rem;
    }

    .travel-form {
        padding: 4rem;
    }

    .location-inputs {
        gap: 3rem;
    }

    .route-connector {
        width: 80px;
        height: 80px;
    }

    .route-connector svg {
        width: 32px;
        height: 32px;
    }

    .details-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .detail-item {
        padding: 2rem;
    }
}