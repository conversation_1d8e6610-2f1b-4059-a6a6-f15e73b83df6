const express = require('express');
const apiRoutes = require('./routes/apiRoutes');
const rateLimiter = require('./middlewares/rateLimiter');
const path = require('path');

const FRONTEND_URL = process.env.FRONTEND_URL;

const app = express();

// 动态设置 CORS
app.options('*', (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', FRONTEND_URL);
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200); // 返回 HTTP 200 状态码
});

app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', FRONTEND_URL);
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    next();
 });

 // 增加请求体大小限制
 app.use(express.json({ limit: '10mb' })); // 将限制设置为 10MB
 app.use(express.urlencoded({ limit: '10mb', extended: true }));

 // 中间件
 app.use(express.json());
 app.use(rateLimiter);

 // 路由
 app.use('/api', apiRoutes);

 // 静态文件服务
 app.use('/uploads', express.static(path.join(__dirname, '..', 'public', 'uploads')));
 // 添加public目录的静态文件服务，用于访问用户攻略图片
 app.use('/api/public', express.static(path.join(__dirname, 'services', 'public')));

 module.exports = app;