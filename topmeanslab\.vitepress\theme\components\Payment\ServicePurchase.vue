<template>
  <div class="service-purchase">
    <el-tabs v-model="activeTab" class="service-tabs">
      <el-tab-pane label="单次服务" name="single">
        <el-card class="service-card">
          <template #header>
            <div class="card-header">
              <span>单次服务购买</span>
            </div>
          </template>

          <div class="service-info">
            <h3>{{ service.name }}</h3>
            <p class="service-description">{{ service.description }}</p>
            <div class="service-price">
              <span class="price-label">价格：</span>
              <span class="price-value">¥{{ service.price }}</span>
            </div>
          </div>

          <div class="service-actions">
            <el-button type="primary" @click="handleSinglePurchase">
              立即购买
            </el-button>
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="VIP会员" name="vip">
        <el-card class="vip-card">
          <template #header>
            <div class="card-header">
              <span>VIP会员订阅</span>
            </div>
          </template>

          <div class="vip-plans">
            <el-row :gutter="20">
              <el-col :span="8" v-for="plan in vipPlans" :key="plan.id">
                <el-card 
                  :class="['plan-card', { 'is-selected': selectedPlan === plan.id }]"
                  @click="selectedPlan = plan.id"
                >
                  <h3>{{ plan.name }}</h3>
                  <div class="plan-price">
                    <span class="price-value">¥{{ plan.price }}</span>
                    <span class="price-unit">/{{ plan.unit }}</span>
                  </div>
                  <ul class="plan-features">
                    <li v-for="(feature, index) in plan.features" :key="index">
                      {{ feature }}
                    </li>
                  </ul>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="vip-actions">
            <el-button 
              type="primary" 
              @click="handleVipPurchase"
              :disabled="!selectedPlan"
            >
              立即开通
            </el-button>
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="账户充值" name="recharge">
        <el-card class="recharge-card">
          <template #header>
            <div class="card-header">
              <span>账户充值</span>
            </div>
          </template>

          <div class="recharge-amount">
            <el-input-number 
              v-model="rechargeAmount"
              :min="100"
              :max="10000"
              :step="100"
              placeholder="请输入充值金额"
            />
          </div>

          <div class="recharge-actions">
            <el-button type="primary" @click="handleRecharge">
              立即充值
            </el-button>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 支付方式选择弹窗 -->
    <PaymentMethods
      v-if="showPayment"
      :amount="paymentAmount"
      @payment-success="handlePaymentSuccess"
      @payment-cancel="handlePaymentCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import PaymentMethods from './PaymentMethods.vue'

const activeTab = ref('single')
const showPayment = ref(false)
const paymentAmount = ref(0)
const selectedPlan = ref(null)
const rechargeAmount = ref(100)

// 模拟服务数据
const service = reactive({
  name: '高级服务',
  description: '提供专业的一对一服务，解决您的所有问题',
  price: 299
})

// 模拟VIP套餐数据
const vipPlans = [
  {
    id: 1,
    name: '月度VIP',
    price: 99,
    unit: '月',
    features: [
      '无限次使用服务',
      '优先响应',
      '专属客服'
    ]
  },
  {
    id: 2,
    name: '季度VIP',
    price: 269,
    unit: '季',
    features: [
      '无限次使用服务',
      '优先响应',
      '专属客服',
      '享受9折优惠'
    ]
  },
  {
    id: 3,
    name: '年度VIP',
    price: 999,
    unit: '年',
    features: [
      '无限次使用服务',
      '优先响应',
      '专属客服',
      '享受8折优惠',
      '年度专属礼包'
    ]
  }
]

const handleSinglePurchase = () => {
  paymentAmount.value = service.price
  showPayment.value = true
}

const handleVipPurchase = () => {
  const plan = vipPlans.find(p => p.id === selectedPlan.value)
  if (plan) {
    paymentAmount.value = plan.price
    showPayment.value = true
  }
}

const handleRecharge = () => {
  paymentAmount.value = rechargeAmount.value
  showPayment.value = true
}

const handlePaymentSuccess = () => {
  showPayment.value = false
  ElMessage.success('支付成功')
  // TODO: 更新用户状态或余额
}

const handlePaymentCancel = () => {
  showPayment.value = false
}
</script>

<style scoped>
.service-purchase {
  padding: 20px;
}

.service-tabs {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.service-info {
  text-align: center;
  padding: 20px 0;
}

.service-description {
  color: #666;
  margin: 10px 0;
}

.service-price {
  margin: 20px 0;
}

.price-label {
  color: #666;
}

.price-value {
  color: #f56c6c;
  font-weight: bold;
  font-size: 24px;
}

.service-actions,
.vip-actions,
.recharge-actions {
  text-align: center;
  margin-top: 20px;
}

.vip-plans {
  margin: 20px 0;
}

.plan-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.plan-card.is-selected {
  border-color: var(--el-color-primary);
}

.plan-features {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.plan-features li {
  margin: 10px 0;
  color: #666;
}

.recharge-amount {
  text-align: center;
  margin: 20px 0;
}
</style> 