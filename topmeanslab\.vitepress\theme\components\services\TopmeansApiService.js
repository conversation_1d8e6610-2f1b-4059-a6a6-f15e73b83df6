import log from 'loglevel';

const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;

/**
 * API服务类 - 负责所有后端API调用
 */
export class TopmeansApiService {
    constructor() {
        this.abortController = null;
    }

    /**
     * 获取酒店URL
     */
    async getHotelUrl(hotelName, account, formattedDateTime, day, lastEnd) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/hotel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: `${lastEnd}${hotelName.replace('*', '')}`,
                    user: account,
                    create_time: formattedDateTime,
                    day: `${day}`,
                })
            });

            if (!response.ok) {
                throw new Error('酒店信息获取失败');
            }

            const { success, url } = await response.json();
            return url;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取美食图片URL
     */
    async getFoodImgUrl(foodName, foodInfo) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/ai_img`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: `一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字：${foodName},相关信息：${foodInfo}`
                })
            });

            if (!response.ok) {
                throw new Error('美食图片获取失败');
            }

            const { success, url } = await response.json();
            return url;
        } catch (err) {
            throw err;
        }
    }

    async getAIImg(name, description) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/ai_img2`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: `${name}：${description}`
                })
            });

            if (!response.ok) {
                throw new Error('美食图片获取失败');
            }

            const { success, url } = await response.json();
            return url;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取景点URL
     */
    async getViewUrl(viewName) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/view`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: viewName
                })
            });

            if (!response.ok) {
                throw new Error('景点信息获取失败');
            }

            const { success, url } = await response.json();
            return url;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 保存计划到数据库
     */
    async savePlanToDB(planData) {
        const { content, account, filename } = planData;

        try {
            // 保存计划文件
            let response = await fetch(`${BACKEND_SRV_URL}/api/save_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    user: account,
                    filename: filename
                })
            });

            if (!response.ok) {
                throw new Error('保存计划失败，请检查网络连接');
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 添加计划到用户记录
     */
    async addPlanToUser(userData) {
        const { account, create_time, days } = userData;

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/user/add_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account: account,
                    create_time: create_time,
                    days: days,
                })
            });

            if (!response.ok) {
                throw new Error('计划存库失败，请检查网络连接');
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 发送消息并处理流式响应
     */
    async sendMessage(msg, onDataReceived, onComplete, onError) {
        // 取消之前的请求
        if (this.abortController) {
            this.abortController.abort();
        }

        this.abortController = new AbortController();

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ msg }),
                signal: this.abortController.signal
            });

            if (!response.ok) {
                throw new Error('DS API 请求失败!');
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder('utf-8');
            let result = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // 处理流式数据
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        if (!line.startsWith('data: ')) continue;

                        const jsonStr = line.slice(6);
                        if (jsonStr === '[DONE]') break;

                        const data = JSON.parse(jsonStr);
                        const msg = data.choices[0].delta.content;

                        if (msg) {
                            result += msg;
                            // 调用数据接收回调
                            if (onDataReceived) {
                                onDataReceived(result, msg);
                            }
                        }
                    } catch (err) {
                    }
                }
            }

            // 调用完成回调
            if (onComplete) {
                onComplete(result);
            }

            return result;
        } catch (error) {
            if (error.name === 'AbortError') {
                return null;
            }

            if (onError) {
                onError(error);
            }
            throw error;
        }
    }

    /**
     * 取消当前请求
     */
    cancelCurrentRequest() {
        if (this.abortController) {
            this.abortController.abort();
            this.abortController = null;
        }
    }

    /**
     * 获取API密钥
     */
    async getApiKeys() {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap_keys`, {
                method: 'GET',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error('获取API密钥失败，请检查网络连接');
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取地图脚本URL
     */
    async getMapScriptUrl() {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap`, {
                method: 'GET',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error('地图脚本加载失败，请检查网络连接');
            }

            return await response.json();
        } catch (err) {
            throw new Error('地图脚本加载失败，请检查网络连接');
        }
    }

    /**
     * 保存地图图片
     */
    async saveMapImage(imageData) {
        const { image, user, filename } = imageData;

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/save_amap_img`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: image,
                    user: user,
                    filename: filename
                })
            });

            if (!response.ok) {
                throw new Error('图片保存失败');
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 通用的GET请求方法
     */
    async get(endpoint, options = {}) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}${endpoint}`, {
                method: 'GET',
                credentials: 'include',
                ...options
            });

            if (!response.ok) {
                throw new Error(`GET请求失败: ${response.status}`);
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 通用的POST请求方法
     */
    async post(endpoint, data, options = {}) {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(data),
                credentials: 'include',
                ...options
            });

            if (!response.ok) {
                throw new Error(`POST请求失败: ${response.status}`);
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    /**
     * 检查网络连接状态
     */
    async checkConnection() {
        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/health`, {
                method: 'GET',
                timeout: 5000
            });
            return response.ok;
        } catch (err) {
            return false;
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.cancelCurrentRequest();
    }
}

// 创建单例实例
export const apiService = new TopmeansApiService();
